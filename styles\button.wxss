/* button样式修改 */
button {
	font-size: 30rpx;
	border-radius: 15rpx;
	/* line-height: 96rpx; */
	padding: 0rpx;
	margin: 0rpx;
	color: #fff;
	background: transparent;
}

button::after {
	border-color: transparent;
	border-radius: 30rpx;
}

button.plain::after {
	border-color: currentColor;
}

button[type='grey'] {
	color: #999999;
	background: #EEEEEE;
}

/* 订单按钮 */
button.order {
	min-width: 140rpx;
	padding: 0 20rpx;
	line-height: 52rpx;
	color: #5B5B5B;
	background: #fff;
	border-radius: 8rpx;
	border: 1rpx solid #979797;
	transform: rotateZ(360deg);
	font-size: 26rpx;
	font-weight: bold;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	margin-left: 20rpx
}


/* 购物车按钮 */
button.reduce,
button.add {
	width: 38rpx;
	height: 38rpx;
	border-radius: 10rpx;
	border: 1rpx solid #EEEEEE;
	transform: rotateZ(360deg);
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

button.reduce .iconfont,
button.add .iconfont {
	font-size: 24rpx;
	line-height: 24rpx;
	overflow: hidden;
}

button.addreduce,
input.addreduce {
	min-width: 60rpx;
	padding: 0 10rpx;
	height: 38rpx;
	color: #666666;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0rpx 10rpx;
}

input.addreduce {
	text-align: center;
	max-width: 150rpx;
}


/* 按钮按下 */
.button-hover {
	opacity: 0.6;
}

/* 清除按钮默认样式 */
.clear-btn {
	margin: 0;
	padding: 0;
	background: transparent;
	border-radius: 0rpx;
	line-height: 1.5;
	border: none;
	text-align: left;
	color: #333;
}

.clear-btn::after {
	border: none;
}
