(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/service"],{

/***/ 74:
/*!*************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/main.js?{"page":"pages%2Fservice"} ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _service = _interopRequireDefault(__webpack_require__(/*! ./pages/service.vue */ 75));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_service.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 75:
/*!********************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue ***!
  \********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./service.vue?vue&type=template&id=fe340868& */ 76);
/* harmony import */ var _service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./service.vue?vue&type=script&lang=js& */ 78);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./service.vue?vue&type=style&index=0&lang=scss& */ 80);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 69);

var renderjs





/* normalize component */

var component = Object(_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["render"],
  _service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/service.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 76:
/*!***************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=template&id=fe340868& ***!
  \***************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=template&id=fe340868& */ 77);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_template_id_fe340868___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 77:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=template&id=fe340868& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.isLoad
    ? _vm.banner.list.length > 0 || _vm.banner.service_filter
    : null
  var g1 = _vm.isLoad && g0 ? _vm.banner.list.length : null
  var g2 = _vm.isLoad && g0 ? _vm.banner.list.length : null
  var g3 = _vm.isLoad && g0 ? _vm.banner.list.length : null
  var g4 = _vm.isLoad && g0 ? _vm.banner.list.length : null
  var g5 =
    _vm.isLoad && g0 && _vm.banner.service_filter
      ? _vm.banner.list.length
      : null
  var g6 =
    _vm.isLoad && g0 && _vm.banner.service_filter ? _vm.tabList.length : null
  var g7 = _vm.isLoad ? _vm.service_cate.list.length : null
  var g8 = _vm.isLoad
    ? _vm.recommend_list && _vm.recommend_list.length > 0
    : null
  var m0 = _vm.isLoad && g8 ? _vm.$t("action.attendantName") : null
  var g9 =
    _vm.isLoad && _vm.loading
      ? _vm.list.current_page >= _vm.list.last_page &&
        _vm.list.data.length > 0 &&
        _vm.location.lng
      : null
  var g10 = _vm.isLoad
    ? !_vm.loading && _vm.list.data.length <= 0 && _vm.list.current_page == 1
    : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      return _vm.$util.goUrl({
        url: "/pages/technician",
        openType: "reLaunch",
      })
    }
    _vm.e1 = function ($event) {
      $event.stopPropagation()
      return _vm.$refs.coupon_auto_item.close()
    }
    _vm.e2 = function ($event) {
      $event.stopPropagation()
      return _vm.$refs.coupon_item.close()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        g7: g7,
        g8: g8,
        m0: m0,
        g9: g9,
        g10: g10,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 78:
/*!*********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=script&lang=js& */ 79);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 79:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 30));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 32));
var _vuex = __webpack_require__(/*! vuex */ 33);
var _siteinfo = _interopRequireDefault(__webpack_require__(/*! @/siteinfo.js */ 50));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var serviceListItem = function serviceListItem() {
  __webpack_require__.e(/*! require.ensure | components/service-list-item */ "components/service-list-item").then((function () {
    return resolve(__webpack_require__(/*! @/components/service-list-item.vue */ 1306));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var openMiniProgram = function openMiniProgram() {
  __webpack_require__.e(/*! require.ensure | components/open-mini-program */ "components/open-mini-program").then((function () {
    return resolve(__webpack_require__(/*! @/components/open-mini-program.vue */ 1313));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var tabbar = function tabbar() {
  __webpack_require__.e(/*! require.ensure | components/tabbar */ "components/tabbar").then((function () {
    return resolve(__webpack_require__(/*! @/components/tabbar.vue */ 1320));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    serviceListItem: serviceListItem,
    openMiniProgram: openMiniProgram,
    tabbar: tabbar
  },
  data: function data() {
    return {
      couponList: [],
      //优惠券 
      coupon_discount: 0,
      //优惠券 
      isLoad: false,
      options: {},
      loading: true,
      lockTap: false,
      openMiniForm: {}
    };
  },
  computed: (0, _vuex.mapState)({
    pageActive: function pageActive(state) {
      return state.service.pageActive;
    },
    activeIndex: function activeIndex(state) {
      return state.service.activeIndex;
    },
    tabList: function tabList(state) {
      return state.service.tabList;
    },
    param: function param(state) {
      return state.service.param;
    },
    list: function list(state) {
      return state.service.list;
    },
    banner: function banner(state) {
      return state.service.banner;
    },
    service_cate: function service_cate(state) {
      return state.service.service_cate;
    },
    recommend_list: function recommend_list(state) {
      return state.service.recommend_list;
    },
    recommend_style: function recommend_style(state) {
      return state.service.recommend_style;
    },
    have_coupon: function have_coupon(state) {
      return state.service.have_coupon;
    },
    primaryColor: function primaryColor(state) {
      return state.config.configInfo.primaryColor;
    },
    subColor: function subColor(state) {
      return state.config.configInfo.subColor;
    },
    configInfo: function configInfo(state) {
      return state.config.configInfo;
    },
    autograph: function autograph(state) {
      return state.user.autograph;
    },
    userInfo: function userInfo(state) {
      return state.user.userInfo;
    },
    location: function location(state) {
      return state.user.location;
    },
    locaRefuse: function locaRefuse(state) {
      return state.user.locaRefuse;
    },
    changeAddr: function changeAddr(state) {
      return state.user.changeAddr;
    },
    isGzhLogin: function isGzhLogin(state) {
      return state.user.isGzhLogin;
    },
    haveShieldOper: function haveShieldOper(state) {
      return state.user.haveShieldOper;
    },
    userCoachStatus: function userCoachStatus(state) {
      return state.user.userCoachStatus;
    },
    useChooseLocation: function useChooseLocation(state) {
      return state.user.useChooseLocation;
    },
    changeOnAddr: function changeOnAddr(state) {
      return state.user.changeOnAddr;
    },
    noChangeLoca: function noChangeLoca(state) {
      return state.user.noChangeLoca;
    },
    scanRecordId: function scanRecordId(state) {
      return state.user.scanRecordId;
    }
  }),
  onLoad: function onLoad(options) {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      var _options, _options$channel_id, channel_id, _this$configInfo$real, realtime_location, pageActive, changeAddr;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _options = options, _options$channel_id = _options.channel_id, channel_id = _options$channel_id === void 0 ? 0 : _options$channel_id;
              if (channel_id) {
                _this.addScanRecord({
                  type: 1,
                  qr_id: channel_id
                });
              }
              if (!channel_id) {
                _context.next = 8;
                break;
              }
              _context.next = 5;
              return _this.updateCommonOptions(options);
            case 5:
              _context.t0 = _context.sent;
              _context.next = 9;
              break;
            case 8:
              _context.t0 = options;
            case 9:
              options = _context.t0;
              _this.options = options;
              _this$configInfo$real = _this.configInfo.realtime_location, realtime_location = _this$configInfo$real === void 0 ? 0 : _this$configInfo$real;
              pageActive = _this.pageActive, changeAddr = _this.changeAddr;
              if (!pageActive) {
                _this.$util.showLoading();
              }
              uni.onNetworkStatusChange(function (res) {
                var isConnected = res.isConnected;
                if (isConnected && (!pageActive || pageActive && (realtime_location || changeAddr))) {
                  _this.initIndex();
                  return;
                }
              });
              _context.next = 17;
              return _this.initIndex();
            case 17:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  onShow: function onShow() {
    var _this2 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
      return _regenerator.default.wrap(function _callee2$(_context2) {
        while (1) {
          switch (_context2.prev = _context2.next) {
            case 0:
              if (_this2.haveShieldOper == 2) {
                _this2.initIndex();
                _this2.updateUserItem({
                  key: 'haveShieldOper',
                  val: 0
                });
              }
              if (_this2.pageActive && _this2.userInfo.id) {
                _this2.getCouponList();
              }
              if (!_this2.location.lat && _this2.locaRefuse) {
                _this2.toResetUtilLoca();
              }
            case 3:
            case "end":
              return _context2.stop();
          }
        }
      }, _callee2);
    }))();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    uni.showNavigationBarLoading();
    this.updateUserItem({
      key: 'changeAddr',
      val: false
    });
    this.initRefresh();
    uni.stopPullDownRefresh();
  },
  onReachBottom: function onReachBottom() {
    if (this.list.current_page >= this.list.last_page || this.loading) return;
    this.loading = true;
    this.getList(this.param.page + 1);
  },
  onShareAppMessage: function onShareAppMessage(e) {
    var _this$userInfo$id = this.userInfo.id,
      pid = _this$userInfo$id === void 0 ? 0 : _this$userInfo$id;
    var path = "/pages/service?pid=".concat(pid);
    this.$util.log(path);
    return {
      title: '',
      imageUrl: '',
      path: path
    };
  },
  watch: {
    locaRefuse: function locaRefuse(newval, oldval) {
      if (!newval) {
        this.toResetUtilLoca();
      }
    },
    changeOnAddr: function changeOnAddr(newval, oldval) {
      var noloca = this.noChangeLoca.noloca;
      if (newval && noloca) {
        this.initUtilLocaData();
      }
    },
    noChangeLoca: function noChangeLoca(newval, oldval) {
      var _this3 = this;
      setTimeout(function () {
        var _this3$changeOnAddr = _this3.changeOnAddr,
          lat = _this3$changeOnAddr.lat,
          lng = _this3$changeOnAddr.lng,
          _this3$changeOnAddr$u = _this3$changeOnAddr.unix,
          unix = _this3$changeOnAddr$u === void 0 ? 0 : _this3$changeOnAddr$u;
        var noloca = _this3.noChangeLoca.noloca;
        var cur_unix = _this3.$util.DateToUnix(_this3.$util.formatTime(new Date(), 'YY-M-D h:m:s'));
        if (noloca && (!lat && !lng || !unix || unix && cur_unix - unix >= 1)) {
          _this3.getUtilLocation();
        }
      }, 800);
    }
  },
  methods: _objectSpread(_objectSpread(_objectSpread({}, (0, _vuex.mapActions)(['getConfigInfo', 'getUserInfo', 'updateCommonOptions', 'addScanRecord', 'updateScanRecord', 'getServiceIndex', 'getRecommendList', 'getServiceList'])), (0, _vuex.mapMutations)(['updateUserItem', 'updateServiceItem', 'updateTechnicianItem', 'updateMapItem', 'updateDynamicItem', 'updateShopstoreItem'])), {}, {
    initIndex: function initIndex() {
      var _arguments = arguments,
        _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var refresh, _this4$options, _this4$options$pid, pid, _this4$options$channe, channel_id, _this4$configInfo$rea, rloca, location, locaRefuse, changeAddr, _this4$configInfo, _this4$configInfo$plu, plugAuth, _this4$configInfo$rea2, realtime_location, _plugAuth$recommend, recommend, _this4$userCoachStatu, coach_status, coach_position, privacyCheck, _this4$changeOnAddr, _this4$changeOnAddr$l, change_lat, _this4$changeOnAddr$l2, change_lng, _this4$changeOnAddr$u, unix, cur_unix, noloca, loca;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                refresh = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (!(!_this4.configInfo.id || refresh)) {
                  _context3.next = 4;
                  break;
                }
                _context3.next = 4;
                return _this4.getConfigInfo();
              case 4:
                _this4$options = _this4.options, _this4$options$pid = _this4$options.pid, pid = _this4$options$pid === void 0 ? 0 : _this4$options$pid, _this4$options$channe = _this4$options.channel_id, channel_id = _this4$options$channe === void 0 ? 0 : _this4$options$channe;
                _this4$configInfo$rea = _this4.configInfo.realtime_location, rloca = _this4$configInfo$rea === void 0 ? 0 : _this4$configInfo$rea;
                if (!(!refresh && _this4.pageActive && !rloca && !_this4.changeAddr && (!pid || !channel_id))) {
                  _context3.next = 11;
                  break;
                }
                _this4.isLoad = true;
                _this4.loading = false;
                _this4.$util.hideAll();
                return _context3.abrupt("return");
              case 11:
                _context3.next = 13;
                return _this4.getServiceIndex();
              case 13:
                _this4.isLoad = true;
                location = _this4.location, locaRefuse = _this4.locaRefuse, changeAddr = _this4.changeAddr;
                _this4$configInfo = _this4.configInfo, _this4$configInfo$plu = _this4$configInfo.plugAuth, plugAuth = _this4$configInfo$plu === void 0 ? {} : _this4$configInfo$plu, _this4$configInfo$rea2 = _this4$configInfo.realtime_location, realtime_location = _this4$configInfo$rea2 === void 0 ? 0 : _this4$configInfo$rea2;
                _plugAuth$recommend = plugAuth.recommend, recommend = _plugAuth$recommend === void 0 ? false : _plugAuth$recommend;
                _this4$userCoachStatu = _this4.userCoachStatus, coach_status = _this4$userCoachStatu.status, coach_position = _this4$userCoachStatu.coach_position;
                if (!(realtime_location && !changeAddr || !realtime_location && recommend && !location.lat)) {
                  _context3.next = 38;
                  break;
                }
                privacyCheck = _this4.$refs.user_privacy.check();
                if (!privacyCheck) {
                  _context3.next = 25;
                  break;
                }
                _this4.$refs.user_privacy.open();
                _this4.loading = false;
                _this4.$util.hideAll();
                return _context3.abrupt("return");
              case 25:
                if (!(coach_status == 2 && coach_position)) {
                  _context3.next = 36;
                  break;
                }
                _this4$changeOnAddr = _this4.changeOnAddr, _this4$changeOnAddr$l = _this4$changeOnAddr.lat, change_lat = _this4$changeOnAddr$l === void 0 ? 0 : _this4$changeOnAddr$l, _this4$changeOnAddr$l2 = _this4$changeOnAddr.lng, change_lng = _this4$changeOnAddr$l2 === void 0 ? 0 : _this4$changeOnAddr$l2, _this4$changeOnAddr$u = _this4$changeOnAddr.unix, unix = _this4$changeOnAddr$u === void 0 ? 0 : _this4$changeOnAddr$u;
                cur_unix = _this4.$util.DateToUnix(_this4.$util.formatTime(new Date(), 'YY-M-D h:m:s'));
                noloca = change_lat && change_lng && unix && cur_unix - unix < 3 ? false : true;
                if (!noloca) {
                  loca = Object.assign({}, _this4.location, {
                    lat: change_lat,
                    lng: change_lng,
                    is_util_loca: 1
                  });
                  _this4.updateUserItem({
                    key: 'location',
                    val: loca
                  });
                }
                _this4.updateUserItem({
                  key: 'noChangeLoca',
                  val: {
                    noloca: noloca
                  }
                });
                if (!_this4.noChangeLoca.noloca) {
                  _context3.next = 33;
                  break;
                }
                return _context3.abrupt("return");
              case 33:
                _this4.initUtilLocaData();
                _context3.next = 37;
                break;
              case 36:
                _this4.getUtilLocation();
              case 37:
                return _context3.abrupt("return");
              case 38:
                _this4.initUtilLocaData();
              case 39:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    getUtilLocation: function getUtilLocation() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _yield$_this5$$util$g, _yield$_this5$$util$g2, lat, _yield$_this5$$util$g3, lng, val;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.next = 2;
                return _this5.$util.getLocation();
              case 2:
                _yield$_this5$$util$g = _context4.sent;
                _yield$_this5$$util$g2 = _yield$_this5$$util$g.lat;
                lat = _yield$_this5$$util$g2 === void 0 ? 0 : _yield$_this5$$util$g2;
                _yield$_this5$$util$g3 = _yield$_this5$$util$g.lng;
                lng = _yield$_this5$$util$g3 === void 0 ? 0 : _yield$_this5$$util$g3;
                val = Object.assign({}, _this5.location, {
                  lat: lat,
                  lng: lng,
                  is_util_loca: 1
                });
                _this5.updateUserItem({
                  key: 'location',
                  val: val
                });
                _this5.initUtilLocaData();
              case 10:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    initUtilLocaData: function initUtilLocaData() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var _this6$location, _this6$location$lng, lng, _this6$location$lat, lat, _this6$configInfo, _this6$configInfo$plu, plugAuth, _this6$configInfo$rea, realtime_location, _plugAuth$recommend2, recommend;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _this6.updateUserItem({
                  key: 'noChangeLoca',
                  val: {
                    noloca: false
                  }
                });
                _this6$location = _this6.location, _this6$location$lng = _this6$location.lng, lng = _this6$location$lng === void 0 ? 0 : _this6$location$lng, _this6$location$lat = _this6$location.lat, lat = _this6$location$lat === void 0 ? 0 : _this6$location$lat;
                _this6.updateServiceItem({
                  key: 'pageActive',
                  val: true
                });
                _this6.$util.hideAll();
                _this6$configInfo = _this6.configInfo, _this6$configInfo$plu = _this6$configInfo.plugAuth, plugAuth = _this6$configInfo$plu === void 0 ? {} : _this6$configInfo$plu, _this6$configInfo$rea = _this6$configInfo.realtime_location, realtime_location = _this6$configInfo$rea === void 0 ? 0 : _this6$configInfo$rea;
                _plugAuth$recommend2 = plugAuth.recommend, recommend = _plugAuth$recommend2 === void 0 ? false : _plugAuth$recommend2;
                if (!(!lat && !lng && (realtime_location || !realtime_location && recommend))) {
                  _context5.next = 13;
                  break;
                }
                if (_this6.userInfo.id && _this6.have_coupon) {
                  _this6.getCouponList();
                }
                _this6.updateServiceItem({
                  key: 'recommend_list',
                  val: []
                });
                _this6.updateServiceItem({
                  key: 'list',
                  val: {
                    data: [],
                    last_page: 1,
                    current_page: 1
                  }
                });
                _this6.loading = false;
                _this6.$util.hideAll();
                return _context5.abrupt("return");
              case 13:
                if (!recommend) {
                  _context5.next = 18;
                  break;
                }
                _context5.next = 16;
                return Promise.all([_this6.getRecommendList({
                  lat: lat,
                  lng: lng
                }), _this6.getList(1)]);
              case 16:
                _context5.next = 20;
                break;
              case 18:
                _context5.next = 20;
                return _this6.getList(1);
              case 20:
                if ((realtime_location || !realtime_location && recommend) && !_this6.changeAddr) {
                  _this6.$util.getMapInfo();
                }
                if (_this6.userInfo.id && _this6.have_coupon) {
                  _this6.getCouponList();
                }
              case 22:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    initRefresh: function initRefresh() {
      this.initIndex(true);
    },
    toResetUtilLoca: function toResetUtilLoca() {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    toOpenLocation: function toOpenLocation() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var privacyCheck;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                privacyCheck = _this7.$refs.user_privacy.check();
                if (!privacyCheck) {
                  _context7.next = 4;
                  break;
                }
                _this7.$refs.user_privacy.open();
                return _context7.abrupt("return");
              case 4:
                _context7.next = 6;
                return _this7.$util.checkAuth({
                  type: 'userLocation',
                  checkApp: true
                });
              case 6:
                _this7.initIndex();
              case 7:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    getList: function getList() {
      var _arguments2 = arguments,
        _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var page, _this8$configInfo$rea, realtime_location, tabList, activeIndex, _this8$banner$service, service_filter, _tabList$activeIndex, sort, sign, desc, oldList, location, _location$lng, lng, _location$lat, lat, param;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                page = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : 0;
                _this8$configInfo$rea = _this8.configInfo.realtime_location, realtime_location = _this8$configInfo$rea === void 0 ? 0 : _this8$configInfo$rea;
                if (page) {
                  tabList = _this8.tabList, activeIndex = _this8.activeIndex;
                  _this8$banner$service = _this8.banner.service_filter, service_filter = _this8$banner$service === void 0 ? true : _this8$banner$service;
                  if (!service_filter) {
                    activeIndex = 0;
                  }
                  _tabList$activeIndex = tabList[activeIndex], sort = _tabList$activeIndex.sort, sign = _tabList$activeIndex.sign;
                  if (realtime_location && activeIndex == 0) {
                    sort = 'type desc';
                  }
                  desc = activeIndex == 0 || sign == 1 ? '' : 'desc';
                  _this8.updateServiceItem({
                    key: 'param',
                    val: {
                      page: page,
                      sort: "".concat(sort, " ").concat(desc)
                    }
                  });
                }
                _this8.loading = true;
                oldList = _this8.list, location = _this8.location;
                _location$lng = location.lng, lng = _location$lng === void 0 ? 0 : _location$lng, _location$lat = location.lat, lat = _location$lat === void 0 ? 0 : _location$lat;
                if (!(!lat && !lng && realtime_location)) {
                  _context8.next = 10;
                  break;
                }
                _this8.loading = false;
                _this8.$util.hideAll();
                return _context8.abrupt("return");
              case 10:
                param = Object.assign({}, _this8.param, {
                  lat: lat,
                  lng: lng
                });
                _context8.next = 13;
                return _this8.getServiceList(param);
              case 13:
                _this8.loading = false;
                _this8.$util.hideAll();
              case 15:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    handerTabChange: function handerTabChange(index) {
      this.updateServiceItem({
        key: 'activeIndex',
        val: index
      });
      var tabList = this.$util.deepCopy(this.tabList);
      var _tabList$index = tabList[index],
        is_sign = _tabList$index.is_sign,
        sign = _tabList$index.sign;
      if (is_sign) {
        tabList[index].sign = sign == 0 ? 1 : 0;
      }
      this.updateServiceItem({
        key: 'tabList',
        val: tabList
      });
      this.$util.showLoading();
      uni.pageScrollTo({
        scrollTop: 0
      });
      this.getList(1);
    },
    getCouponList: function getCouponList() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var _this9$location, lat, lng, _yield$_this9$$api$se, _yield$_this9$$api$se2, discount, list;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _this9$location = _this9.location, lat = _this9$location.lat, lng = _this9$location.lng;
                _context9.next = 3;
                return _this9.$api.service.couponList({
                  lat: lat,
                  lng: lng
                });
              case 3:
                _yield$_this9$$api$se = _context9.sent;
                _yield$_this9$$api$se2 = _yield$_this9$$api$se.discount;
                discount = _yield$_this9$$api$se2 === void 0 ? 0 : _yield$_this9$$api$se2;
                list = _yield$_this9$$api$se.list;
                _this9.couponList = list;
                _this9.coupon_discount = discount;
                if (_this9.isLoad) {
                  if (discount * 1 > 0) {
                    _this9.$refs.coupon_auto_item.open();
                  }
                  if (discount * 1 == 0 && list.length > 0) {
                    _this9.$refs.coupon_item.open();
                  }
                }
                _this9.loading = false;
                _this9.$util.hideAll();
              case 12:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9);
      }))();
    },
    toAppShare: function toAppShare() {
      var _this10 = this;
      var _this$userInfo$id2 = this.userInfo.id,
        pid = _this$userInfo$id2 === void 0 ? 0 : _this$userInfo$id2;
      var title = '首页';
      var siteroot = _siteinfo.default.siteroot;
      var url = siteroot.split('/index.php')[0];
      var href = "".concat(url, "/h5/#/pages/service?pid=").concat(pid);
      var imageUrl = '';
      this.$jweixin.wxReady(function () {
        _this10.$jweixin.showOptionMenu();
        _this10.$jweixin.shareAppMessage(title, '', href, imageUrl);
        _this10.$jweixin.shareTimelineMessage(title, href, imageUrl);
      });
    },
    toBindChannel: function toBindChannel() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var _this11$options, _this11$options$pid, pid, _this11$options$chann, channel_id, _this11$userInfo$id, id, uid;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                // 移除自动登录逻辑，只处理已登录用户的绑定
                _this11$options = _this11.options, _this11$options$pid = _this11$options.pid, pid = _this11$options$pid === void 0 ? 0 : _this11$options$pid, _this11$options$chann = _this11$options.channel_id, channel_id = _this11$options$chann === void 0 ? 0 : _this11$options$chann;
                _this11$userInfo$id = _this11.userInfo.id, id = _this11$userInfo$id === void 0 ? 0 : _this11$userInfo$id; // 注释掉自动获取用户信息的逻辑
                // if ((pid || channel_id) && !id) {
                // 	await this.getUserInfo()
                // }
                uid = _this11.userInfo.id;
                if (!(channel_id && uid)) {
                  _context10.next = 6;
                  break;
                }
                _context10.next = 6;
                return _this11.$api.user.bindChannel({
                  channel_id: channel_id
                });
              case 6:
                if (_this11.scanRecordId) {
                  _this11.updateScanRecord();
                }
              case 7:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10);
      }))();
    },
    // 轮播图
    goBanner: function goBanner(e) {
      // connect_type 1查看大图，2文章
      var connect_type = e.connect_type,
        _e$type_id = e.type_id,
        id = _e$type_id === void 0 ? 0 : _e$type_id,
        current = e.img,
        _e$linkType = e.linkType,
        linkType = _e$linkType === void 0 ? 0 : _e$linkType,
        _e$link = e.link,
        link = _e$link === void 0 ? [] : _e$link;
      if (connect_type) {
        switch (connect_type) {
          case 1:
            this.$util.previewImage({
              current: current,
              urls: [current]
            });
            break;
          case 2:
            this.$util.goUrl({
              url: "/user/pages/article?id=".concat(id)
            });
            break;
        }
        return;
      }
      var url = link[0].url;
      this.toConfirmGoUrl(url, linkType, current[0].url);
    },
    // 导航栏
    goCate: function goCate(e) {
      var id = e.id,
        title = e.title,
        _e$url = e.url,
        url = _e$url === void 0 ? '' : _e$url,
        _e$linkType2 = e.linkType,
        linkType = _e$linkType2 === void 0 ? 0 : _e$linkType2,
        _e$link2 = e.link,
        link = _e$link2 === void 0 ? [] : _e$link2,
        _e$img = e.img,
        img = _e$img === void 0 ? [] : _e$img;
      if (linkType) {
        url = link[0].url;
      }
      if (!url) {
        url = "/user/pages/service/list?id=".concat(id, "&title=").concat(title);
      }
      if (['/technician/pages/apply?type=1', '/agent/pages/apply'].includes(url)) {
        this.$util.toCheckLogin({
          url: url
        });
        return;
      }
      if (!linkType) {
        this.$util.goUrl({
          url: url
        });
        return;
      }
      this.toConfirmGoUrl(url, linkType, img[0].url, title);
    },
    toConfirmGoUrl: function toConfirmGoUrl(url, linkType, current) {
      var title = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';
      if (['/technician/pages/apply?type=1', '/agent/pages/apply'].includes(url)) {
        this.$util.toCheckLogin({
          url: url
        });
        return;
      }
      if (linkType == 5) {
        this.$util.previewImage({
          current: current,
          urls: [current]
        });
        return;
      }
      var methodObj = this.configInfo.methodObj;
      var openType = methodObj[linkType];
      this.$util.goUrl({
        url: url,
        openType: openType
      });
    },
    userGetCoupon: function userGetCoupon() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var ids, res;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                ids = [];
                _this12.couponList.forEach(function (v) {
                  ids.push(v.id);
                });
                _context11.next = 4;
                return _this12.$api.service.userGetCoupon({
                  coupon_id: ids
                });
              case 4:
                res = _context11.sent;
                _this12.$util.showToast({
                  title: "\u9886\u53D6\u6210\u529F"
                });
                setTimeout(function () {
                  _this12.$util.goUrl({
                    url: '/user/pages/coupon/list'
                  });
                }, 1000);
                _this12.$refs.coupon_item.close();
                _this12.loading = false;
                _this12.$util.hideAll();
              case 10:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11);
      }))();
    },
    toCouponGetBtn: function toCouponGetBtn() {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                _this13.$refs.coupon_auto_item.close();
                _this13.$util.goUrl({
                  url: '/user/pages/coupon/list'
                });
              case 2:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12);
      }))();
    },
    toTechnician: function toTechnician(index) {
      var _this$recommend_list$ = this.recommend_list[index],
        id = _this$recommend_list$.id,
        city_id = _this$recommend_list$.city_id,
        coach_name = _this$recommend_list$.coach_name;
      this.updateTechnicianItem({
        key: 'pageActive',
        val: false
      });
      this.$util.goUrl({
        url: "/pages/technician?coach_id=".concat(id, "&coach_name=").concat(coach_name, "&city_id=").concat(city_id),
        openType: "reLaunch"
      });
    }
  })
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 80:
/*!******************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=style&index=0&lang=scss& ***!
  \******************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./service.vue?vue&type=style&index=0&lang=scss& */ 81);
/* harmony import */ var _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_software_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_service_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 81:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[74,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/service.js.map