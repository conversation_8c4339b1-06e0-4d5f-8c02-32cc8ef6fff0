{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?8387", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?518c", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?0376", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?a96d", "uni-app:///components/shop-banner.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?bc91", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/shop-banner.vue?b87c"], "names": ["props", "detail", "type", "default", "isShare", "setCurrent", "watch", "data", "statusBarHeight", "videoContexts", "playVideo", "current", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "created", "methods", "handerSwiperChange", "e", "swiperTransition", "playCurrent", "onPlay", "onPause", "onEnded", "onError", "onTimeUpdate", "onWaiting", "onProgress", "onLoadedMetaData", "handerBannerClick", "url", "video_url", "openType", "goBack", "uni", "delta"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoDlzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;EACA;EACAG;IACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA,IACAR,UACAS,SADAT;MAEA;MACA;MACA;IACA;IACAU;MACA;IAAA,CAQA;IACAC;MACA;MACA;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;MACA,mBAGA;QAFAC;QAAA,qCACAC;QAAAA;MAEA;QACA;QACA;MACA;MACA;MACA;QACAC;QAAA;QACAF;MACA;IACA;IACAG;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAA0nC,CAAgB,6mCAAG,EAAC,C;;;;;;;;;;;ACA9oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/shop-banner.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./shop-banner.vue?vue&type=template&id=6d69a310&\"\nvar renderjs\nimport script from \"./shop-banner.vue?vue&type=script&lang=js&\"\nexport * from \"./shop-banner.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shop-banner.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/shop-banner.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shop-banner.vue?vue&type=template&id=6d69a310&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.playVideo && _vm.detail.images.length\n  var g1 = g0 ? _vm.detail.images.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shop-banner.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shop-banner.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"rel\">\r\n\t\t<view class=\"abs\" style=\"z-index: 99;\" :style=\"{top:statusBarHeight + 'px'}\">\r\n\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t<view @tap.stop=\"goBack\" class=\"flex-center circle home-return-btn\" v-if=\"!isShare\"\r\n\t\t\t\t:class=\"[{'back-user-ios': configInfo.isIos},{'back-user-android': !configInfo.isIos}]\">\r\n\t\t\t\t<view class=\"iconfont icon-left c-base text-bold\" style=\"font-size: 40rpx;\"></view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view @tap=\"$util.goUrl({url:`/pages/service`,openType:`reLaunch`})\"\r\n\t\t\t\t:class=\"[{'back-user-ios': configInfo.isIos},{'back-user-android': !configInfo.isIos}]\" v-if=\"isShare\">\r\n\t\t\t\t<view class=\"iconshouye iconfont\"></view>\r\n\t\t\t\t<view class=\"back-user_text\">回到首页</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<view class='banner'>\r\n\t\t\t<swiper class='banner-swiper' :style=\"{background: playVideo && !detail.video_vid ? '#000':'#f4f6f8'}\"\r\n\t\t\t\t@change='handerSwiperChange' @transition=\"swiperTransition\" :autoplay=\"playVideo ? false : true\"\r\n\t\t\t\t:current=\"current\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in detail.images\" :key=\"index\" @tap='handerBannerClick(index)'>\r\n\t\t\t\t\t<block v-if=\"index == 0 && detail.video_url\">\r\n\t\t\t\t\t\t<block v-if=\"!playVideo\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"playCurrent\"\r\n\t\t\t\t\t\t\t\tclass=\"banner-swiper c-base iconfont icontushucxuanzebofangtiaozhuan abs flex-center\"\r\n\t\t\t\t\t\t\t\tstyle=\"top: 0rpx;font-size: 80rpx;z-index: 9;\"></view>\r\n\t\t\t\t\t\t\t<image mode=\"aspectFill\" class='banner-img' :src='item'></image>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t\t<txv-video :vid=\"detail.video_vid\" :playerid=\"detail.video_vid\" width=\"100%\" height=\"100%\"\r\n\t\t\t\t\t\t\t:controls=\"true\" :autoplay=\"true\" :isHiddenStop=\"true\" v-if=\"playVideo && detail.video_vid\">\r\n\t\t\t\t\t\t</txv-video>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<view class=\"video-box\" v-if=\"playVideo && !detail.video_vid\">\r\n\t\t\t\t\t\t\t<video :id=\"`video_id`\" class=\"my-video\" :loop=\"false\" enable-play-gesture\r\n\t\t\t\t\t\t\t\t:enable-progress-gesture=\"false\" :src=\"detail.video_url\" :autoplay=\"playVideo\"\r\n\t\t\t\t\t\t\t\t@play=\"onPlay\" @pause=\"onPause\" @ended=\"onEnded\" @timeupdate=\"onTimeUpdate\"\r\n\t\t\t\t\t\t\t\t@waiting=\"onWaiting\" @progress=\"onProgress\" @loadedmetadata=\"onLoadedMetaData\"></video>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<image mode=\"aspectFill\" class='banner-img' :src='item' v-else></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<view class='banner-tagitem banner-tagitem_count' v-if=\"!playVideo && detail.images.length\">\r\n\t\t\t\t{{current+1}}/{{detail.images.length}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tdetail: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tisShare: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetCurrent: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t'detail.images'(newValue, oldValue) {\r\n\t\t\t\tthis.current = 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: uni.getSystemInfoSync().statusBarHeight,\r\n\t\t\t\tvideoContexts: {},\r\n\t\t\t\tplayVideo: false,\r\n\t\t\t\tcurrent: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t}),\r\n\t\tcreated() {\r\n\t\t\tthis.videoContexts = uni.createVideoContext(`video_id`, this)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thanderSwiperChange(e) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcurrent,\r\n\t\t\t\t} = e.detail\r\n\t\t\t\tthis.current = current;\r\n\t\t\t\tthis.videoContexts.pause()\r\n\t\t\t\tthis.playVideo = false;\r\n\t\t\t},\r\n\t\t\tswiperTransition(e) {\r\n\t\t\t\t// // #ifdef H5\r\n\t\t\t\t// if(this.playVideo && this.current == 0 && e.detail.dx < 30){\r\n\t\t\t\t// \tthis.current = 1\r\n\t\t\t\t// \tthis.videoContexts.pause()\r\n\t\t\t\t// \tthis.playVideo = false;\r\n\t\t\t\t// \tconsole.log(e,\"======swiperTransition\")\r\n\t\t\t\t// }\r\n\t\t\t\t// // #endif\r\n\t\t\t},\r\n\t\t\tplayCurrent() {\r\n\t\t\t\tthis.videoContexts.play()\r\n\t\t\t\tthis.playVideo = true\r\n\t\t\t},\r\n\t\t\tonPlay(e) {},\r\n\t\t\tonPause(e) {},\r\n\t\t\tonEnded(e) {},\r\n\t\t\tonError(e) {},\r\n\t\t\tonTimeUpdate(e) {},\r\n\t\t\tonWaiting(e) {},\r\n\t\t\tonProgress(e) {},\r\n\t\t\tonLoadedMetaData(e) {},\r\n\t\t\thanderBannerClick(index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\timage_url: url,\r\n\t\t\t\t\tvideo_url = ''\r\n\t\t\t\t} = this.detail\r\n\t\t\t\tif (index == 0 && video_url) {\r\n\t\t\t\t\tthis.playVideo = true;\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!url) return\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\topenType: 'web', //this.configInfo.methodObj[jump_type],\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.home-return-btn {\r\n\t\tmargin-top: 10rpx;\r\n\t\tmargin-left: 24rpx;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: none;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.video-box {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 500rpx;\r\n\t}\r\n\r\n\t.my-video {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 80%;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 120rpx;\r\n\t}\r\n\r\n\t.banner {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.banner-swiper {\r\n\t\twidth: 750rpx;\r\n\t\theight: 564rpx;\r\n\t}\r\n\r\n\t.banner-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.banner-taglist {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: absolute;\r\n\t\tbottom: 32rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.banner-tagitem {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 90rpx;\r\n\t\theight: 42rpx;\r\n\t\tborder-radius: 21rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\tcolor: #2b2b2b;\r\n\t\tfont-size: 22rpx;\r\n\t\tmargin-left: 32rpx;\r\n\t}\r\n\r\n\t.banner-tagitem:nth-child(1) {\r\n\t\tmargin-left: 0;\r\n\t}\r\n\r\n\t.banner-tagitem_count {\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tbottom: 32rpx;\r\n\t\tright: 32rpx;\r\n\t\tz-index: 10;\r\n\t}\r\n\r\n\t.banner-tagitem_active {\r\n\t\tbackground: #19c865;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shop-banner.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shop-banner.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110368942\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}