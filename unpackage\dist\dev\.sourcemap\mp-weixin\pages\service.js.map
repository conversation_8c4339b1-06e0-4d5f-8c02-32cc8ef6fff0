{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?f8c6", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?1d24", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?284b", "uni-app:///pages/service.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?fdba", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/pages/service.vue?be38"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "serviceListItem", "openMiniProgram", "tabbar", "data", "couponList", "coupon_discount", "isLoad", "options", "loading", "lockTap", "openMiniForm", "computed", "pageActive", "activeIndex", "tabList", "param", "list", "banner", "service_cate", "recommend_list", "recommend_style", "have_coupon", "primaryColor", "subColor", "configInfo", "autograph", "userInfo", "location", "locaRefuse", "changeAddr", "isGzhLogin", "haveShieldOper", "userCoachStatus", "useChooseLocation", "changeOnAddr", "noChangeLoca", "scanRecordId", "onLoad", "channel_id", "type", "qr_id", "realtime_location", "uni", "isConnected", "res", "onShow", "key", "val", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "id", "pid", "title", "imageUrl", "path", "watch", "noloca", "setTimeout", "lat", "lng", "unix", "methods", "initIndex", "refresh", "plugAuth", "recommend", "coach_status", "coach_position", "privacyCheck", "cur_unix", "loca", "is_util_loca", "getUtilLocation", "initUtilLocaData", "last_page", "current_page", "Promise", "initRefresh", "toResetUtilLoca", "toOpenLocation", "checkApp", "getList", "page", "service_filter", "sort", "sign", "desc", "oldList", "handerTabChange", "is_sign", "scrollTop", "getCouponList", "discount", "toAppShare", "siteroot", "siteInfo", "href", "toBindChannel", "uid", "goBanner", "connect_type", "e", "type_id", "current", "img", "linkType", "link", "urls", "url", "goCate", "toConfirmGoUrl", "methodObj", "openType", "userGetCoupon", "ids", "coupon_id", "toCouponGetBtn", "toTechnician", "city_id", "coach_name"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA0xB,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmO9yB;AAKA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,WAGA9B,wCADA+B;cAEA;gBACA;kBACAC;kBACAC;gBACA;cACA;cAAA,KACAF;gBAAA;gBAAA;cAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA;cAAA;YAAA;cAAA/B;cACA;cAAA,wBAGA,iBADAkC;cAGA7B,aAEA,MAFAA,YACAiB,aACA,MADAA;cAEA;gBACA;cACA;cACAa;gBACA,IACAC,cACAC,IADAD;gBAEA;kBACA;kBACA;gBACA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EASA;EACAE;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAaA;gBACA;gBACA;kBACAC;kBACAC;gBACA;cACA;cACA;gBACA;cACA;cACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAEAN;IAEA;MACAI;MACAC;IACA;IACA;IACAL;EACA;EACAO;IACA;IACA;IACA;EACA;EACAC;IACA,wBAEA,cADAC;MAAAC;IAEA;IACA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA5B;MACA;QACA;MACA;IACA;IACAM;MACA,IACAuB,SACA,kBADAA;MAEA;QACA;MACA;IACA;IACAtB;MAAA;MACAuB;QACA,0BAIA;UAHAC;UACAC;UAAA,4CACAC;UAAAA;QAEA,IACAJ,SACA,oBADAA;QAEA;QACA;UACA;QACA;MACA;IACA;EACA;EACAK,uDACA,mHACA,wDACA,IACA,yGACA,2CACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,iBAKA,oDAFAZ,0GACAd;gBAAA,wBAIA,kBADAG;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAoBA;cAAA;gBACA;gBAMAd,WAGA,OAHAA,UACAC,aAEA,OAFAA,YACAC,aACA,OADAA;gBAAA,oBAKA,6DAFAoC,+HACAxB;gBAAA,sBAIAwB,SADAC;gBAAA,wBAOA,wBAFAC,6CACAC;gBAAA,MAQA3B;kBAAA;kBAAA;gBAAA;gBAGA4B;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAIAF;kBAAA;kBAAA;gBAAA;gBAAA,sBAKA,iEAHAR,6HACAC,8HACAC;gBAEAS;gBACAb;gBACA;kBACAc;oBACAZ;oBACAC;oBACAY;kBACA;kBACA;oBACA1B;oBACAC;kBACA;gBACA;gBACA;kBACAD;kBACAC;oBACAU;kBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;cAAA;gBAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAgCA;cAAA;gBAAA;gBAAA,+CAFAd;gBAAAA;gBAAA,+CACAC;gBAAAA;gBAEAb;kBACAY;kBACAC;kBACAY;gBACA;gBACA;kBACA1B;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA5B;kBACAC;oBACAU;kBACA;gBACA;gBAAA,kBAIA,uDAFAG,2GACAD;gBAEA;kBACAb;kBACAC;gBACA;gBACA;gBAAA,oBAKA,6DAFAkB,8HACAxB;gBAAA,uBAIAwB,SADAC;gBAAA,MAQA;kBAAA;kBAAA;gBAAA;gBAGA;kBACA;gBACA;gBACA;kBACApB;kBACAC;gBACA;gBACA;kBACAD;kBACAC;oBACA5C;oBACAwE;oBACAC;kBACA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA,KAKAV;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAW;kBACAlB;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAGA;kBACA;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkB;MACA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAmCA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAX;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACA9B;kBACA0C;gBACA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,wBAGA,kBADA1C;gBAEA;kBAEA3B,UAEA,OAFAA,SACAD,cACA,OADAA;kBAAA,wBAIA,cADAuE;kBAEA;oBACAvE;kBACA;kBAAA,uBAIAC,sBAFAuE,kCACAC;kBAGA;oBACAD;kBACA;kBACAE;kBACA;oBACAzC;oBACAC;sBACAoC;sBACAE;oBACA;kBACA;gBACA;gBACA;gBAEAG,UAEA,OAFAxE,MACAW,WACA,OADAA;gBAAA,gBAOAA,SAFAiC,yEAEAjC,SADAgC;gBAAA,MAMA;kBAAA;kBAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;gBAIA5C;kBACA4C;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MACA;QACA3C;QACAC;MACA;MACA;MACA,qBAGAjC;QAFA4E;QACAJ;MAEA;QACAxE;MACA;MACA;QACAgC;QACAC;MACA;MACA;MACAL;QACAiD;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kBAIA,iBAFAjC,2BACAC;gBAAA;gBAAA,OAKA;kBACAD;kBACAC;gBACA;cAAA;gBAAA;gBAAA,+CALAiC;gBAAAA;gBACA7E;gBAKA;gBACA;gBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA8E;MAAA;MACA,yBAEA,cADA3C;QAAAC;MAEA;MACA,IACA2C,WACAC,kBADAD;MAEA;MACA;MACA;MACA;QACA;QACA,4CACAE,MACA3C;QACA,6CACA2C,MACA3C;MACA;IACA;IACA4C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA,kBAIA,uDAFA9C,6GACAd;gBAAA,sBAIA,iBADAa,mEAEA;gBACA;gBACA;gBACA;gBAEAgD,MACA,iBADAhD;gBAAA,MAEAb;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAA;gBACA;cAAA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA8D;MACA;MACA,IACAC,eAKAC,EALAD;QAAA,aAKAC,EAJAC;QAAApD;QACAqD,UAGAF,EAHAG;QAAA,cAGAH,EAFAI;QAAAA;QAAA,UAEAJ,EADAK;QAAAA;MAEA;QACA;UACA;YACA;cACAH;cACAI;YACA;YACA;UACA;YACA;cACAC;YACA;YACA;QAAA;QAEA;MACA;MACA,IACAA,MACAF,QADAE;MAEA;IACA;IACA;IACAC;MACA,IACA3D,KAMAmD,EANAnD;QACAE,QAKAiD,EALAjD;QAAA,SAKAiD,EAJAO;QAAAA;QAAA,eAIAP,EAHAI;QAAAA;QAAA,WAGAJ,EAFAK;QAAAA;QAAA,SAEAL,EADAG;QAAAA;MAEA;QACAI;MACA;MACA;QACAA,4CACA1D;MACA;MACA,uCACA,qBACA,UACA0D;QACA;UACAA;QACA;QACA;MACA;MAEA;QACA;UACAA;QACA;QACA;MACA;MACA,+CACAxD;IACA;IACA0D;MAAA;MACA,uCACA,qBACA,UACAF;QACA;UACAA;QACA;QACA;MACA;MACA;QACA;UACAL;UACAI;QACA;QACA;MACA;MAEA,IACAI,YACA,gBADAA;MAEA;MAoBA;QACAH;QACAI;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA,qBACAD;kBACAE;gBACA;cAAA;gBAHAxE;gBAIA;kBACAS;gBACA;gBACAK;kBACA;oBACAmD;kBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAQ;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACAR;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MACA,4BAIA;QAHAnE;QACAoE;QACAC;MAEA;QACA1E;QACAC;MACA;MACA;QACA8D;QACAI;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7+BA;AAAA;AAAA;AAAA;AAAy9C,CAAgB,66CAAG,EAAC,C;;;;;;;;;;;ACA7+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/service.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/service.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./service.vue?vue&type=template&id=fe340868&\"\nvar renderjs\nimport script from \"./service.vue?vue&type=script&lang=js&\"\nexport * from \"./service.vue?vue&type=script&lang=js&\"\nimport style0 from \"./service.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/service.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=template&id=fe340868&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoad\n    ? _vm.banner.list.length > 0 || _vm.banner.service_filter\n    : null\n  var g1 = _vm.isLoad && g0 ? _vm.banner.list.length : null\n  var g2 = _vm.isLoad && g0 ? _vm.banner.list.length : null\n  var g3 = _vm.isLoad && g0 ? _vm.banner.list.length : null\n  var g4 = _vm.isLoad && g0 ? _vm.banner.list.length : null\n  var g5 =\n    _vm.isLoad && g0 && _vm.banner.service_filter\n      ? _vm.banner.list.length\n      : null\n  var g6 =\n    _vm.isLoad && g0 && _vm.banner.service_filter ? _vm.tabList.length : null\n  var g7 = _vm.isLoad ? _vm.service_cate.list.length : null\n  var g8 = _vm.isLoad\n    ? _vm.recommend_list && _vm.recommend_list.length > 0\n    : null\n  var m0 = _vm.isLoad && g8 ? _vm.$t(\"action.attendantName\") : null\n  var g9 =\n    _vm.isLoad && _vm.loading\n      ? _vm.list.current_page >= _vm.list.last_page &&\n        _vm.list.data.length > 0 &&\n        _vm.location.lng\n      : null\n  var g10 = _vm.isLoad\n    ? !_vm.loading && _vm.list.data.length <= 0 && _vm.list.current_page == 1\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/pages/technician\",\n        openType: \"reLaunch\",\n      })\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.coupon_auto_item.close()\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.coupon_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        m0: m0,\n        g9: g9,\n        g10: g10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"pages-home\">\r\n\t\t<block v-if=\"isLoad\">\r\n\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t<uni-nav-bar :fixed=\"true\" :shadow=\"false\" :statusBar=\"true\" :title=\"configInfo.app_text\" color=\"#ffffff\"\r\n\t\t\t\t:backgroundColor=\"primaryColor\">\r\n\t\t\t</uni-nav-bar>\r\n\t\t\t<view :style=\"{height:`${configInfo.navBarHeight}px`}\"></view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<view :class=\"[{'mt-md':banner.list.length ==0},{'rel':banner.list.length >0}]\"\r\n\t\t\t\t:style=\"{height:banner.list.length > 0? banner.banner_height + (banner.service_filter ? 64 : 0) + 'rpx' :`84rpx`}\"\r\n\t\t\t\tv-if=\"banner.list.length > 0 || banner.service_filter\">\r\n\t\t\t\t<banner @change=\"goBanner\" :list=\"banner.list\" :height=\"banner.banner_height\" :margin=\"0\"\r\n\t\t\t\t\t:autoplay=\"true\" :indicatorActiveColor=\"primaryColor\" :dotWidth=\"20\" :dotBottom=\"30\"\r\n\t\t\t\t\tv-if=\"banner.list.length > 0\">\r\n\t\t\t\t</banner>\r\n\t\t\t\t<view class=\"search-box flex-center fill-base ml-md mr-md radius\"\r\n\t\t\t\t\t:class=\"[{'abs':banner.list.length>0}]\" v-if=\"banner.service_filter\">\r\n\t\t\t\t\t<view style=\"width: 92%;\">\r\n\t\t\t\t\t\t<tab @change=\"handerTabChange\" :isLine=\"false\" :list=\"tabList\" :activeIndex=\"activeIndex*1\"\r\n\t\t\t\t\t\t\t:activeColor=\"primaryColor\" :width=\"100/tabList.length + '%'\" height=\"84rpx\"></tab>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 8%;\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- \t<view class=\"pd-lg\" style=\"word-break: break-all;\">{{JSON.stringify(userCoachStatus)}}</view>\r\n\t\t\t<view class=\"pd-lg\" style=\"word-break: break-all;\">changeOnAddr=={{JSON.stringify(changeOnAddr)}}</view>\r\n\t\t\t<view class=\"pd-lg\" style=\"word-break: break-all;\">{{useChooseLocation}}</view>\r\n\t\t\t<view class=\"pd-lg\" style=\"word-break: break-all;\">noChangeLoca=={{JSON.stringify(noChangeLoca)}}</view> -->\r\n\r\n\t\t\t<!-- 分类 -->\r\n\t\t\t<view class=\"fill-base mt-md ml-md mr-md radius-16\" style=\"overflow: hidden;\"\r\n\t\t\t\tv-if=\"service_cate.list.length>0\">\r\n\t\t\t\t<column @change=\"goCate\" :list=\"service_cate.list\" :indicatorActiveColor=\"primaryColor\"\r\n\t\t\t\t\t:colNum=\"service_cate.col_num\" :rowNum=\"service_cate.row_num\">\r\n\t\t\t\t</column>\r\n\t\t\t</view>\r\n \r\n \r\n\t\t\t<view class=\"fill-base mt-md ml-md mr-md pt-lg pl-lg pr-md pb-lg radius-16\"\r\n\t\t\t\tv-if=\"recommend_list && recommend_list.length > 0\">\r\n\t\t\t\t<view @tap.stop=\"$util.goUrl({url: `/pages/technician`,openType: `reLaunch`})\"\r\n\t\t\t\t\tclass=\"flex-between pb-lg\">\r\n\t\t\t\t\t<view class=\"f-paragraph c-black text-bold\">推荐{{$t('action.attendantName')}}</view>\r\n\t\t\t\t\t<view class=\"flex-y-center f-caption c-caption\">查看更多<i class=\"iconfont icon-right\"\r\n\t\t\t\t\t\t\tstyle=\"font-size: 24rpx;\"></i></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-x class=\"recommend-technician\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in recommend_list\" :key=\"index\">\r\n\t\t\t\t\t\t<view @tap.stop=\"toTechnician(index)\" class=\"recommend-item type-1\" v-if=\"recommend_style == 1\">\r\n\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t<view class=\"cover radius-16\">\r\n\t\t\t\t\t\t\t\t<view class=\"h5-image cover radius-16\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${item.work_img}')`}\">\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t\t<image mode=\"aspectFill\" lazy-load class=\"cover radius-16\" :src=\"item.work_img\"></image>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t<view class=\"flex-center f-desc c-title mt-md\">\r\n\t\t\t\t\t\t\t\t<view class=\"ellipsis\">{{item.coach_name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @tap.stop=\"toTechnician(index)\" class=\"recommend-item type-2 pd-md\"\r\n\t\t\t\t\t\t\tv-if=\"recommend_style == 2\">\r\n\t\t\t\t\t\t\t<view class=\"flex-center pb-sm\">\r\n\t\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t\t<view class=\"cover radius\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"h5-image cover radius\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${item.work_img}')`}\">\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t\t\t<image mode=\"aspectFill\" lazy-load class=\"cover radius\" :src=\"item.work_img\"></image>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t<view class=\"flex-1 ml-sm\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"f-desc ellipsis\">{{item.coach_name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-baseline\" style=\"margin-top: 4rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"iconfont iconyduixingxingshixin icon-font-color\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"star-text flex-y-center f-caption\">{{item.star}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"new-technician flex-center f-icontext\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{color:primaryColor,border:`1rpx solid ${primaryColor}`}\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.is_new\">新人\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"f-icontext c-caption\" v-else>30天接单{{item.order_count||0}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<view class=\"fill-base mt-md ml-md mr-md pd-lg radius-16\" v-for=\"(item,index) in list.data\" :key=\"index\">\r\n\t\t\t\t<service-list-item :info=\"item\"></service-list-item>\r\n\t\t\t</view>\r\n\r\n\t\t\t<load-more :noMore=\"list.current_page>=list.last_page&&list.data.length>0&&location.lng\" :loading=\"loading\"\r\n\t\t\t\tv-if=\"loading\">\r\n\t\t\t</load-more>\r\n\r\n\t\t\t<block v-if=\"!loading&&list.data.length<=0&&list.current_page==1\">\r\n\t\t\t\t<abnor v-if=\"location.lat&&location.lng\">\r\n\t\t\t\t</abnor>\r\n\t\t\t\t<block v-if=\"!location.lat&&!location.lng\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<abnor type=\"NOT_LOCATION\" title=\"暂无数据\"></abnor>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<abnor type=\"NOT_LOCATION\" title=\"暂无数据\" @confirm=\"toOpenLocation\"\r\n\t\t\t\t\t\t:button=\"[{ text: '开启定位' , type: 'confirm' }]\" btnSize=\"\"></abnor>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</block>\r\n\t\t\t</block>\r\n\r\n\t\t\t<view class=\"space-footer\"></view>\r\n\t\t\t<uni-popup ref=\"coupon_auto_item\" type=\"center\" :maskClick=\"false\">\r\n\t\t\t\t<view class=\"coupon-auto-popup rel\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view class=\"h5-image bg-img abs\"\r\n\t\t\t\t\t\t:style=\"{ backgroundImage : `url('https://lbqny.migugu.com/admin/anmo/mine/coupon-bg.png')`}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<image mode=\"aspectFill\" lazy-load class=\"bg-img abs\"\r\n\t\t\t\t\t\tsrc=\"https://lbqny.migugu.com/admin/anmo/mine/coupon-bg.png\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<i @tap.stop=\"$refs.coupon_auto_item.close()\" class=\"iconfont icon-guanbi-fill c-base abs\"></i>\r\n\t\t\t\t\t<view class=\"content flex-center flex-column abs\">\r\n\t\t\t\t\t\t<view class=\"title text-bold flex-center\">恭喜获得</view>\r\n\t\t\t\t\t\t<view class=\"f-mini-title text-bold\">价值{{coupon_discount||0}}元的优惠券,\r\n\t\t\t\t\t\t\t已自动发放到您的卡包,点击下方按钮前去查看吧~</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<image class=\"get-btn abs\" src=\"https://lbqny.migugu.com/admin/anmo/mine/coupon-get-btn.png\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view @tap.stop=\"toCouponGetBtn\" class=\"get-btn flex-center f-mini-title text-bold c-base abs\">\r\n\t\t\t\t\t\t前往卡包查看\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\t\t\t<uni-popup ref=\"coupon_item\" type=\"center\" :maskClick=\"false\">\r\n\t\t\t\t<view class=\"coupon-popup flex-center rel\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view class=\"h5-image bg-img\"\r\n\t\t\t\t\t\t:style=\"{ backgroundImage : `url('https://lbqnyv2.migugu.com/bianzu3.png')`}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<image mode=\"aspectFill\" lazy-load class=\"bg-img\" src=\"https://lbqnyv2.migugu.com/bianzu3.png\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t\t<i @tap.stop=\"$refs.coupon_item.close()\" class=\"iconfont icon-close c-base abs\"></i>\r\n\t\t\t\t\t<view class=\"coupon-info flex-center flex-column\">\r\n\t\t\t\t\t\t<view class=\"tops flex-center flex-column\">\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t成功领取\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t卡券将放入“我的-我的卡券”\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"lists flex-center\">\r\n\t\t\t\t\t\t\t<scroll-view scroll-y style=\"width: 420rpx;height:100%;\">\r\n\t\t\t\t\t\t\t\t<view class=\"list flex-between\" v-for=\"(item, index) in couponList\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<image src=\"https://lbqny.migugu.com/admin/anmo/coupon/coupon.png\"\r\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\">\r\n\t\t\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-center flex-column\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{item.discount}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"price_text\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{item.full*1>0?`满${item.full}可用`:`立减`}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"title flex-y-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"ellipsis-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{item.title}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btns flex-center\" @tap.stop=\"userGetCoupon\">\r\n\t\t\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t\t\t领取到卡包\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\t\t\t<view :style=\"{height: `${configInfo.tabbarHeight}px`}\"></view>\r\n\t\t\t<tabbar :cur=\"1\"></tabbar>\r\n\r\n\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t<open-location-info ref=\"open_location_info\" :pageActive=\"pageActive\" :home=\"true\"></open-location-info>\r\n\t\t\t<login-info></login-info>\r\n\t\t\t<!-- #endif -->\r\n\r\n\t\t\t<change-user-type></change-user-type>\r\n\r\n\t\t</block>\r\n\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<open-mini-program ref=\"open_mini_program\" :info=\"openMiniForm\"></open-mini-program>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\timport siteInfo from '@/siteinfo.js';\r\n\timport serviceListItem from \"@/components/service-list-item.vue\"\r\n\timport openMiniProgram from \"@/components/open-mini-program.vue\"\r\n\timport tabbar from \"@/components/tabbar.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tserviceListItem,\r\n\t\t\topenMiniProgram,\r\n\t\t\ttabbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcouponList: [], //优惠券 \r\n\t\t\t\tcoupon_discount: 0, //优惠券 \r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tloading: true,\r\n\t\t\t\tlockTap: false,\r\n\t\t\t\topenMiniForm: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tpageActive: state => state.service.pageActive,\r\n\t\t\tactiveIndex: state => state.service.activeIndex,\r\n\t\t\ttabList: state => state.service.tabList,\r\n\t\t\tparam: state => state.service.param,\r\n\t\t\tlist: state => state.service.list,\r\n\t\t\tbanner: state => state.service.banner,\r\n\t\t\tservice_cate: state => state.service.service_cate,\r\n\t\t\trecommend_list: state => state.service.recommend_list,\r\n\t\t\trecommend_style: state => state.service.recommend_style,\r\n\t\t\thave_coupon: state => state.service.have_coupon,\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tautograph: state => state.user.autograph,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\tlocation: state => state.user.location,\r\n\t\t\tlocaRefuse: state => state.user.locaRefuse,\r\n\t\t\tchangeAddr: state => state.user.changeAddr,\r\n\t\t\tisGzhLogin: state => state.user.isGzhLogin,\r\n\t\t\thaveShieldOper: state => state.user.haveShieldOper,\r\n\t\t\tuserCoachStatus: state => state.user.userCoachStatus,\r\n\t\t\tuseChooseLocation: state => state.user.useChooseLocation,\r\n\t\t\tchangeOnAddr: state => state.user.changeOnAddr,\r\n\t\t\tnoChangeLoca: state => state.user.noChangeLoca,\r\n\t\t\tscanRecordId: state => state.user.scanRecordId,\r\n\t\t}),\r\n\t\tasync onLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tchannel_id = 0\r\n\t\t\t} = options\r\n\t\t\tif (channel_id) {\r\n\t\t\t\tthis.addScanRecord({\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\tqr_id: channel_id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\toptions = channel_id ? await this.updateCommonOptions(options) : options\r\n\t\t\tthis.options = options\r\n\t\t\tlet {\r\n\t\t\t\trealtime_location = 0\r\n\t\t\t} = this.configInfo\r\n\t\t\tlet {\r\n\t\t\t\tpageActive,\r\n\t\t\t\tchangeAddr\r\n\t\t\t} = this\r\n\t\t\tif (!pageActive) {\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t}\r\n\t\t\tuni.onNetworkStatusChange((res) => {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tisConnected\r\n\t\t\t\t} = res\r\n\t\t\t\tif (isConnected && (!pageActive || (pageActive && (realtime_location || changeAddr)))) {\r\n\t\t\t\t\tthis.initIndex()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tawait this.initIndex()\r\n\t\t\t// #ifdef H5\r\n\t\t\tlet {\r\n\t\t\t\tapp_text\r\n\t\t\t} = this.configInfo\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: app_text || '首页'\r\n\t\t\t})\r\n\t\t\t// #endif \r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif (this.$jweixin.isWechat()) {\r\n\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\tthis.toAppShare()\r\n\t\t\t}\r\n\t\t\tlet {\r\n\t\t\t\tapp_text\r\n\t\t\t} = this.configInfo\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: app_text || '首页'\r\n\t\t\t})\r\n\t\t\t// #endif \r\n\t\t\tif (this.haveShieldOper == 2) {\r\n\t\t\t\tthis.initIndex()\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'haveShieldOper',\r\n\t\t\t\t\tval: 0\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.pageActive && this.userInfo.id) {\r\n\t\t\t\tthis.getCouponList()\r\n\t\t\t}\r\n\t\t\tif (!this.location.lat && this.locaRefuse) {\r\n\t\t\t\tthis.toResetUtilLoca()\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.showNavigationBarLoading()\r\n\t\t\t// #endif\r\n\t\t\tthis.updateUserItem({\r\n\t\t\t\tkey: 'changeAddr',\r\n\t\t\t\tval: false\r\n\t\t\t})\r\n\t\t\tthis.initRefresh();\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.list.current_page >= this.list.last_page || this.loading) return;\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.getList(this.param.page + 1);\r\n\t\t},\r\n\t\tonShareAppMessage(e) {\r\n\t\t\tlet {\r\n\t\t\t\tid: pid = 0\r\n\t\t\t} = this.userInfo\r\n\t\t\tlet path = `/pages/service?pid=${pid}`\r\n\t\t\tthis.$util.log(path)\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '',\r\n\t\t\t\timageUrl: '',\r\n\t\t\t\tpath,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tlocaRefuse(newval, oldval) {\r\n\t\t\t\tif (!newval) {\r\n\t\t\t\t\tthis.toResetUtilLoca()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeOnAddr(newval, oldval) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tnoloca,\r\n\t\t\t\t} = this.noChangeLoca\r\n\t\t\t\tif (newval && noloca) {\r\n\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnoChangeLoca(newval, oldval) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\tunix = 0\r\n\t\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tnoloca\r\n\t\t\t\t\t} = this.noChangeLoca\r\n\t\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\t\tif (noloca && ((!lat && !lng) || !unix || (unix && (cur_unix - unix >= 1)))) {\r\n\t\t\t\t\t\tthis.getUtilLocation()\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 800)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getUserInfo', 'updateCommonOptions', 'addScanRecord', 'updateScanRecord',\r\n\t\t\t\t'getServiceIndex', 'getRecommendList', 'getServiceList'\r\n\t\t\t]),\r\n\t\t\t...mapMutations(['updateUserItem', 'updateServiceItem', 'updateTechnicianItem', 'updateMapItem',\r\n\t\t\t\t'updateDynamicItem', 'updateShopstoreItem'\r\n\t\t\t]),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t\tif (!this.configInfo.id || refresh) {\r\n\t\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tpid = 0,\r\n\t\t\t\t\t\t\tchannel_id = 0\r\n\t\t\t\t\t} = this.options\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\trealtime_location: rloca = 0\r\n\t\t\t\t\t} = this.configInfo\r\n\t\t\t\t\tif (!refresh && this.pageActive && (!rloca && !this.changeAddr) && (!pid || !channel_id)) {\r\n\t\t\t\t\t\tthis.isLoad = true\r\n\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 移除自动登录调用，只获取配置信息\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t// 注释掉自动登录相关代码\r\n\t\t\t\t\t// if (this.isGzhLogin) {\r\n\t\t\t\t\t// \tsetTimeout(() => {\r\n\t\t\t\t\t// \t\tthis.toBindChannel()\r\n\t\t\t\t\t// \t}, 1000)\r\n\t\t\t\t\t// } else {\r\n\t\t\t\t\t// \tawait this.toBindChannel()\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t// 注释掉自动登录相关代码\r\n\t\t\t\t\t// await this.toBindChannel()\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\tawait this.getServiceIndex()\r\n\t\t\t\t\tthis.isLoad = true\r\n\r\n\r\n\r\n\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlocation,\r\n\t\t\t\t\t\tlocaRefuse,\r\n\t\t\t\t\t\tchangeAddr\r\n\t\t\t\t\t} = this\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tplugAuth = {},\r\n\t\t\t\t\t\t\trealtime_location = 0\r\n\t\t\t\t\t} = this.configInfo\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\trecommend = false\r\n\t\t\t\t\t} = plugAuth\r\n\r\n\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tstatus: coach_status,\r\n\t\t\t\t\t\tcoach_position\r\n\t\t\t\t\t} = this.userCoachStatus\r\n\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tif (!locaRefuse && ((realtime_location && !changeAddr) || (!realtime_location && recommend && !location\r\n\t\t\t\t\t\t\t.lat))) {\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\tif ((realtime_location && !changeAddr) || (!realtime_location && recommend && !location.lat)) {\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tif (coach_status == 2 && coach_position) {\r\n\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\tlat: change_lat = 0,\r\n\t\t\t\t\t\t\t\t\tlng: change_lng = 0,\r\n\t\t\t\t\t\t\t\t\tunix = 0\r\n\t\t\t\t\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\t\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\t\t\t\t\tlet noloca = change_lat && change_lng && (unix && (cur_unix - unix < 3)) ? false : true\r\n\t\t\t\t\t\t\t\tif (!noloca) {\r\n\t\t\t\t\t\t\t\t\tlet loca = Object.assign({}, this.location, {\r\n\t\t\t\t\t\t\t\t\t\tlat: change_lat,\r\n\t\t\t\t\t\t\t\t\t\tlng: change_lng,\r\n\t\t\t\t\t\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\t\t\t\t\t\tval: loca\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\t\tkey: 'noChangeLoca',\r\n\t\t\t\t\t\t\t\t\tval: {\r\n\t\t\t\t\t\t\t\t\t\tnoloca\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tif (this.noChangeLoca.noloca) return\r\n\t\t\t\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.getUtilLocation()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t\t},\r\n\t\t\t\t\tasync getUtilLocation() {\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tif (this.$jweixin.isWechat()) {\r\n\t\t\t\t\t\t\t\tlet wxReady = await this.$jweixin.wxReady2();\r\n\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\tlatitude: lat = 0,\r\n\t\t\t\t\t\t\t\t\tlongitude: lng = 0\r\n\t\t\t\t\t\t\t\t} = wxReady ? await this.$jweixin.getWxLocation() : {\r\n\t\t\t\t\t\t\t\t\tlatitude: 0,\r\n\t\t\t\t\t\t\t\t\tlongitude: 0\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// await this.$jweixin.wxReady3()\r\n\t\t\t\t\t\t\t\t// let {\r\n\t\t\t\t\t\t\t\t// \tlatitude: lat = 0,\r\n\t\t\t\t\t\t\t\t// \tlongitude: lng = 0\r\n\t\t\t\t\t\t\t\t// } = await this.$jweixin.getWxLocation()\r\n\t\t\t\t\t\t\t\tlet val = Object.assign({}, this.location, {\r\n\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\t\t\t\t\tval\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\tlat = 0,\r\n\t\t\t\t\t\t\t\t\tlng = 0\r\n\t\t\t\t\t\t\t} = await this.$util.getLocation()\r\n\t\t\t\t\t\t\tlet val = Object.assign({}, this.location, {\r\n\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\t\t\t\tval\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tasync initUtilLocaData() {\r\n\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\tkey: 'noChangeLoca',\r\n\t\t\t\t\t\t\t\tval: {\r\n\t\t\t\t\t\t\t\t\tnoloca: false\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\tlng = 0,\r\n\t\t\t\t\t\t\t\t\tlat = 0\r\n\t\t\t\t\t\t\t} = this.location\r\n\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\tkey: 'pageActive',\r\n\t\t\t\t\t\t\t\tval: true\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\tplugAuth = {},\r\n\t\t\t\t\t\t\t\t\trealtime_location = 0\r\n\t\t\t\t\t\t\t} = this.configInfo\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\trecommend = false\r\n\t\t\t\t\t\t\t} = plugAuth\r\n\r\n\t\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tif (!lat && !lng) {\r\n\t\t\t\t\t\t\t\t// #endif \r\n\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\tif (!lat && !lng && (realtime_location || (!realtime_location && recommend))) {\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\tif (this.userInfo.id && this.have_coupon) {\r\n\t\t\t\t\t\t\t\t\t\tthis.getCouponList()\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\t\t\tkey: 'recommend_list',\r\n\t\t\t\t\t\t\t\t\t\tval: []\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\t\t\tkey: 'list',\r\n\t\t\t\t\t\t\t\t\t\tval: {\r\n\t\t\t\t\t\t\t\t\t\t\tdata: [],\r\n\t\t\t\t\t\t\t\t\t\t\tlast_page: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcurrent_page: 1\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\tif (recommend) {\r\n\t\t\t\t\t\t\t\t\tawait Promise.all([this.getRecommendList({\r\n\t\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\t\tlng\r\n\t\t\t\t\t\t\t\t\t}), this.getList(1)])\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tawait this.getList(1)\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif ((realtime_location || (!realtime_location && recommend)) && !this.changeAddr) {\r\n\t\t\t\t\t\t\t\t\tthis.$util.getMapInfo()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (this.userInfo.id && this.have_coupon) {\r\n\t\t\t\t\t\t\t\t\tthis.getCouponList()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tinitRefresh() {\r\n\t\t\t\t\t\t\t\t\tthis.initIndex(true)\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tasync toResetUtilLoca() {\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\tlat: locaLat = 0\r\n\t\t\t\t\t\t\t\t\t\t} = this.location\r\n\t\t\t\t\t\t\t\t\t\tif (!locaLat && this.pageActive) {\r\n\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\tlng = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlat = 0\r\n\t\t\t\t\t\t\t\t\t\t\t} = await this.$util.getUtilLocation()\r\n\t\t\t\t\t\t\t\t\t\t\tif (!lat && !lng) return\r\n\t\t\t\t\t\t\t\t\t\t\tthis.$refs.open_location_info.pShow = false\r\n\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\trecommend\r\n\t\t\t\t\t\t\t\t\t\t\t} = this.configInfo.plugAuth\r\n\t\t\t\t\t\t\t\t\t\t\tif (recommend) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tawait Promise.all([this.getRecommendList({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlng\r\n\t\t\t\t\t\t\t\t\t\t\t\t}), this.getList(1)])\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tawait this.getList(1)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tthis.$util.getMapInfo()\r\n\t\t\t\t\t\t\t\t\t\t\tlet updateArr = ['updateTechnicianItem', 'updateMapItem',\r\n\t\t\t\t\t\t\t\t\t\t\t\t'updateDynamicItem', 'updateShopstoreItem'\r\n\t\t\t\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t\t\t\t\tupdateArr.map(item => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis[item]({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'pageActive',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tval: false\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tasync toOpenLocation() {\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN \r\n\t\t\t\t\t\t\t\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\t\t\t\t\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype: 'userLocation',\r\n\t\t\t\t\t\t\t\t\t\t\t\tcheckApp: true\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN \r\n\t\t\t\t\t\t\t\t\t\t\tthis.initIndex()\r\n\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tasync getList(page = 0) {\r\n\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\trealtime_location = 0\r\n\t\t\t\t\t\t\t\t\t\t\t} = this.configInfo\r\n\t\t\t\t\t\t\t\t\t\t\tif (page) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttabList,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tactiveIndex\r\n\t\t\t\t\t\t\t\t\t\t\t\t} = this\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tservice_filter = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t} = this.banner\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (!service_filter) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tactiveIndex = 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsort,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsign\r\n\t\t\t\t\t\t\t\t\t\t\t\t} = tabList[activeIndex]\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (realtime_location && activeIndex == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsort = 'type desc'\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet desc = activeIndex == 0 || sign == 1 ? '' : 'desc'\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'param',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tval: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpage: page,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsort: `${sort} ${desc}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tthis.loading = true\r\n\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\tlist: oldList,\r\n\t\t\t\t\t\t\t\t\t\t\t\tlocation,\r\n\t\t\t\t\t\t\t\t\t\t\t} = this\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\tlng = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlat = 0\r\n\t\t\t\t\t\t\t\t\t\t\t} = location\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\t\tif (!lat && !lng) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (!lat && !lng && realtime_location) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tlet param = Object.assign({}, this.param, {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlng\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tawait this.getServiceList(param)\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\thanderTabChange(index) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'activeIndex',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tval: index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlet tabList = this.$util.deepCopy(this.tabList)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tis_sign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t} = tabList[index];\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (is_sign) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttabList[index].sign = sign == 0 ? 1 : 0;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.updateServiceItem({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'tabList',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tval: tabList\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tscrollTop: 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.getList(1)\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\tasync getCouponList() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlng\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.location\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdiscount = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlist\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = await this.$api.service.couponList({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlng\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.couponList = list\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.coupon_discount = discount\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (this.isLoad) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (discount * 1 > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.coupon_auto_item.open()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (discount * 1 == 0 && list.length > 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.coupon_item.open()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttoAppShare() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid: pid = 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.userInfo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet title = '首页'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsiteroot\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = siteInfo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet url = siteroot.split('/index.php')[0]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet href = `${url}/h5/#/pages/service?pid=${pid}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet imageUrl = ''\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$jweixin.showOptionMenu()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$jweixin.shareAppMessage(title, '',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timageUrl)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$jweixin.shareTimelineMessage(title,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timageUrl)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tasync toBindChannel() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 移除自动登录逻辑，只处理已登录用户的绑定\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpid = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tchannel_id = 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.options\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid = 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.userInfo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 注释掉自动获取用户信息的逻辑\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// if ((pid || channel_id) && !id) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// \tawait this.getUserInfo()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid: uid\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.userInfo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (channel_id && uid) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tawait this.$api.user.bindChannel({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tchannel_id\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (this.scanRecordId) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.updateScanRecord()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 轮播图\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgoBanner(e) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// connect_type 1查看大图，2文章\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconnect_type,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttype_id: id = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timg: current,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlinkType = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlink = []\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = e\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (connect_type) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tswitch (connect_type) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.previewImage({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcurrent,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turls: [current]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: `/user/pages/article?id=${id}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = link[0]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.toConfirmGoUrl(url, linkType, current[0].url)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 导航栏\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgoCate(e) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl = '',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlinkType = 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlink = [],\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timg = []\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = e\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (linkType) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl = link[0].url\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (!url) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t`/user/pages/service/list?id=${id}&title=${title}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (['/technician/pages/apply?type=1',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'/agent/pages/apply'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t].includes(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.toCheckLogin({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (!linkType) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.toConfirmGoUrl(url, linkType, img[0].url,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoConfirmGoUrl(url, linkType, current, title = '') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (['/technician/pages/apply?type=1',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'/agent/pages/apply'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t].includes(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.toCheckLogin({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (linkType == 5) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.previewImage({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcurrent,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turls: [current]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmethodObj\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.configInfo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet openType = methodObj[linkType]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (openType === 'web') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.location.href = url\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (openType === 'miniProgram') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet arr = url.split(\";\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet appid = arr[0]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet path = arr.length > 1 ? arr[1] : ''\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.openMiniForm = {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tappid,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpath,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timg: current\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.open_mini_program.open()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topenType\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tasync userGetCoupon() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet ids = []\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.couponList.forEach(v => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tids.push(v.id)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet res = await this.$api.service\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.userGetCoupon({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcoupon_id: ids\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: `领取成功`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/user/pages/coupon/list'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.coupon_item.close()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tasync toCouponGetBtn() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$refs.coupon_auto_item.close()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/user/pages/coupon/list'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoTechnician(index) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcity_id,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcoach_name\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} = this.recommend_list[index]\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'pageActive',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tval: false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: `/pages/technician?coach_id=${id}&coach_name=${coach_name}&city_id=${city_id}`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topenType: `reLaunch`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.pages-home {\r\n\t\t.search-box {\r\n\t\t\twidth: 710rpx;\r\n\t\t\tbottom: 0;\r\n\t\t\tz-index: 9;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\t.recommend-technician {\r\n\t\t\twhite-space: nowrap;\r\n\t\t\twidth: 650rpx;\r\n\r\n\t\t\t.recommend-item {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t}\r\n\r\n\t\t\t.recommend-item.type-1 {\r\n\t\t\t\twidth: 143rpx;\r\n\t\t\t\tmargin-left: 26rpx;\r\n\r\n\t\t\t\t.cover {\r\n\t\t\t\t\twidth: 143rpx;\r\n\t\t\t\t\theight: 143rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ellipsis {\r\n\t\t\t\t\tmax-width: 143rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.recommend-item.type-2 {\r\n\t\t\t\twidth: 203rpx;\r\n\t\t\t\theight: 151rpx;\r\n\t\t\t\tbackground: #F4F6F7;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\r\n\t\t\t\t.cover {\r\n\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ellipsis {\r\n\t\t\t\t\tmax-width: 82rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.iconyduixingxingshixin {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tbackground-image: -webkit-linear-gradient(270deg, #FAD961 0%, #F76B1C 100%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.star-text {\r\n\t\t\t\t\theight: 26rpx;\r\n\t\t\t\t\tcolor: #FF9519;\r\n\t\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.new-technician {\r\n\t\t\t\t\twidth: 67rpx;\r\n\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\ttransform: rotateZ(360deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.recommend-item:nth-child(1) {\r\n\t\t\t\tmargin-left: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.coupon-auto-popup {\r\n\t\twidth: 600rpx;\r\n\t\theight: 737rpx;\r\n\t\tpadding: 0 40rpx;\r\n\r\n\t\t.bg-img {\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 40rpx;\r\n\t\t\twidth: 520rpx;\r\n\t\t\theight: 607rpx;\r\n\t\t}\r\n\r\n\t\t.icon-guanbi-fill {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\ttop: 22rpx;\r\n\t\t\tright: 0;\r\n\t\t\tz-index: 999;\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\ttop: 200rpx;\r\n\t\t\tleft: 40rpx;\r\n\t\t\twidth: 520rpx;\r\n\t\t\theight: 365rpx;\r\n\t\t\tpadding: 0rpx 60rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 52rpx;\r\n\t\t\t\tcolor: #F12E1A;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.f-mini-title {\r\n\t\t\t\tcolor: #3A3A3A;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.get-btn {\r\n\t\t\ttop: 657rpx;\r\n\t\t\tleft: 77rpx;\r\n\t\t\twidth: 448rpx;\r\n\t\t\theight: 81rpx;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.get-btn.flex-center {\r\n\t\t\tz-index: 2\r\n\t\t}\r\n\t}\r\n\r\n\t.coupon-popup {\r\n\t\twidth: 658rpx;\r\n\t\theight: 865rpx;\r\n\r\n\t\t.bg-img {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t.icon-close {\r\n\t\t\tfont-size: 60rpx;\r\n\t\t\ttop: 50rpx;\r\n\t\t\tright: 60rpx;\r\n\t\t\tz-index: 999;\r\n\t\t}\r\n\r\n\t\t.coupon-info {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\r\n\t\t\t.tops {\r\n\t\t\t\twidth: 480rpx;\r\n\t\t\t\tcolor: #FB4523;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 260rpx;\r\n\r\n\t\t\t\t>view:nth-child(1) {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.lists {\r\n\t\t\t\twidth: 500rpx;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t\tpadding: 10rpx;\r\n\t\t\t\toverflow-x: hidden;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 222rpx;\r\n\r\n\t\t\t\t.list {\r\n\t\t\t\t\twidth: 420rpx;\r\n\t\t\t\t\theight: 130rpx;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t>image {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t>view {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tleft: 8rpx;\r\n\r\n\t\t\t\t\t\t>view:nth-child(1) {\r\n\t\t\t\t\t\t\twidth: 38%;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t>view:nth-child(2) {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #FB4523;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tline-height: 36rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.price_text {\r\n\t\t\t\t\t\t\tcolor: #ccc;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tview.btns {\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: absolute;\r\n\t\t\theight: 82rpx;\r\n\t\t\tbottom: 0rpx;\r\n\t\t\tleft: 0;\r\n\r\n\t\t\t>view {\r\n\t\t\t\twidth: 422rpx;\r\n\t\t\t\theight: 82rpx;\r\n\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754123825801\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}