{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?5436", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?0c02", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?390d", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?1c88", "uni-app:///agent/pages/order/refund.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?ae8f", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/refund.vue?7a6c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "isLoad", "options", "statusType", "refundForm", "lockTap", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "onLoad", "agent", "methods", "initIndex", "refresh", "bg", "id", "<PERSON><PERSON><PERSON>", "order_id", "rnum", "aitem", "rmprice", "bitem", "residue_material_price", "rtprice", "residue_service_price", "num", "can_refund_num", "tprice", "true_price", "mprice", "material_price", "setTimeout", "url", "openType", "initRefresh", "changeNum", "refund_num", "title", "checkInput", "handleCheckOrderItem", "all", "car_price", "cprice", "cnum", "is_check", "item", "service", "material", "tsprice", "tsmprice", "crnum", "ttrprice", "total", "anum", "not", "toConfirmRefund", "subForm", "all_refund_num", "refund_car_price", "list", "order", "snum", "csnum", "sorder", "rprice", "refund_price", "refund_material_price", "rsprice"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAyxB,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC0O7yB;AAIA;AAAA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA,qBAEAT,QADAU;MAAAA;IAEAV;IACA;IACA;EACA;EACAW,uDACA,2DACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MASA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;kBACAC;gBACA;gBAAA,gBAIA,eAFAC,uBACAL;gBAEAM;gBAAA;gBAAA;gBAAA,OAGA;kBACAC;gBACA;cAAA;gBAFAnB;gBAGAA;gBACAA;gBACAA;gBACAA;gBACAoB;gBACApB;kBACAqB;oBACA,IACAC,UAKAC,MALAC;sBACAC,UAIAF,MAJAG;sBACAC,MAGAJ,MAHAK;sBACAC,SAEAN,MAFAO;sBACAC,SACAR,MADAS;oBAEAZ;oBACAG;oBACAA;oBACAA;oBAEAM;oBACAE;oBACAR;oBACAA;oBACAA;oBACAA;oBAEA;oBACA;oBACAA;oBACAA;oBACAA;kBACA;kBACAF,kFACA,QACA;gBACA;gBACArB;gBACA;kBACAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAiC;kBACA;kBACA;kBACA;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,wBAIA,oDAFAC,+CACAV;gBAEAD;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAY;gBACA;gBAAA;cAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MAAA;MAAA;MACA,uBAKA;QAJAC;QACAC;QACAC;QACAC;MAGA;QACA,IACAC,WACA,iDADAA;QAEA;MACA;MAEA;QACA;UACAC;YACA1B;UACA;QACA;MACA;MACA;QACA;QACA;UACAuB;QACA;QACA;UACAA;QACA;QACA;UACA;QACA;MACA;MAEA;QAEA,4BAUA;UATAI;UACAC;UACAC;UACAC;UACAC;UACAzB;UACAF;UACAH;UACA+B;QAGA;QACA;UACA5B;QACA;QACA;UACAH;QACA;QAEA4B;QACAC;QACA1B;QACAH;QAEA;QAEA;UACA;UACA;UACA;UACA;UACA,sFACA+B;QACA;MACA;MACApB;QACA;QACA;QACA;QACA;UACAc;YACA;cACAO;cACAC;YACA;cACAC;YACA;UACA;QACA;QACAZ;QACAU;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAEAb,OAIAa,QAJA9B,gBACA2B,OAGAG,QAHAC,gBACAf,SAEAc,QAFAE,kBACAC,OACAH,QADAI;gBAEAlB;gBACAkB;gBACAC;gBACAC;gBACAC;kBACA;gBACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAA;kBACAF;kBACA;oBACAC;kBACA;gBACA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIAF;kBACA;oBACA;kBACA;kBACA;oBACA;sBACA,IACAzC,OAKAG,MALAe;wBACA4B,SAIA3C,MAJA4C;wBACApC,SAGAR,MAHA6C;wBACAC,UAEA9C,MAFAG;wBACAJ,UACAC,MADAC;sBAEA;sBACA;sBACA;wBACAP;wBACAU;wBACAG;wBACAE;sBACA;oBACA;oBACA8B;sBACA3C;sBACA0C;sBACAlB;oBACA;kBACA;gBACA;gBACA;kBACAmB;oBACA3C;oBACA0C;oBACAlB;kBACA;gBACA;gBAEA/B,QACA,eADAA;gBAEAM;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBACA4C;gBACA;cAAA;gBACA;gBACA;kBACAvB;gBACA;gBACAN;kBACA;kBACA;kBACA;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;AC9jBA;AAAA;AAAA;AAAA;AAAw9C,CAAgB,46CAAG,EAAC,C;;;;;;;;;;;ACA5+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/order/refund.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/order/refund.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./refund.vue?vue&type=template&id=9113c0c8&\"\nvar renderjs\nimport script from \"./refund.vue?vue&type=script&lang=js&\"\nexport * from \"./refund.vue?vue&type=script&lang=js&\"\nimport style0 from \"./refund.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/order/refund.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=template&id=9113c0c8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.isLoad\n    ? _vm.__map(_vm.refundForm.order, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.order_goods.length > 0 && index == 0\n        var l0 = g0\n          ? _vm.__map(item.order_goods, function (aitem, aindex) {\n              var $orig = _vm.__get_orig(aitem)\n              var g1 = aitem.is_check && aindex != item.order_goods.length - 1\n              var g2 = !(item.pay_type == 2)\n                ? [3, 4, 5].includes(item.pay_type)\n                : null\n              return {\n                $orig: $orig,\n                g1: g1,\n                g2: g2,\n              }\n            })\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          l0: l0,\n        }\n      })\n    : null\n  var g3 = _vm.isLoad ? _vm.refundForm.order.length : null\n  var l3 =\n    _vm.isLoad && g3 > 1\n      ? _vm.__map(_vm.refundForm.order, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g4 = item.order_goods.length > 0 && index != 0\n          var l2 = g4\n            ? _vm.__map(item.order_goods, function (aitem, aindex) {\n                var $orig = _vm.__get_orig(aitem)\n                var g5 = aitem.is_check && aindex != item.order_goods.length - 1\n                var g6 = !(item.pay_type == 2)\n                  ? [3, 4, 5].includes(item.pay_type)\n                  : null\n                return {\n                  $orig: $orig,\n                  g5: g5,\n                  g6: g6,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            g4: g4,\n            l2: l2,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: 1,\n        openType: \"navigateBack\",\n      })\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g3: g3,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"user-order-refund\" v-if=\"isLoad\">\r\n\r\n\t\t<block v-for=\"(item, index) in refundForm.order\" :key=\"index\">\r\n\t\t\t<view class=\"fill-base pd-lg mt-md ml-md mr-md radius-16\" v-if=\"item.order_goods.length > 0 && index == 0\">\r\n\t\t\t\t<view class=\"f-title c-title text-bold pb-lg\">首单项目</view>\r\n\t\t\t\t<block v-for=\"(aitem,aindex) in item.order_goods\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"item-child flex-warp\"\r\n\t\t\t\t\t\t:class=\"[{'mt-lg':aindex!=0},{'pb-lg b-1px-b':aitem.is_check && aindex != item.order_goods.length-1}]\">\r\n\t\t\t\t\t\t<view @tap=\"handleCheckOrderItem(1,index, aindex)\" class=\"flex-y-center\" style=\"height:147rpx\">\r\n\t\t\t\t\t\t\t<i class=\"iconfont mr-md\"\r\n\t\t\t\t\t\t\t\t:class=\"[{'icon-xuanze':!aitem.is_check},{'icon-xuanze-fill':aitem.is_check}]\"\r\n\t\t\t\t\t\t\t\t:style=\"{color:aitem.is_check ? primaryColor : '#CACACA'}\"></i>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t<view @tap=\"handleCheckOrderItem(1,index, aindex)\" class=\"flex-warp\">\r\n\t\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t\t<view class=\"goods-img\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"h5-image goods-img\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${aitem.goods_cover}')`}\">\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t\t\t<image mode=\"aspectFill\" class=\"goods-img\" :src=\"aitem.goods_cover\"></image>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t\t\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f-title c-title text-bold max-300 ellipsis\">{{aitem.goods_name}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{color:item.pay_type==2?primaryColor: [3,4,5].includes(item.pay_type)?subColor: item.pay_type == 6 ? '#11C95E' : '#333'}\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ statusType[item.pay_type] }}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"f-caption c-caption mt-sm\" style=\"height:30rpx\">\r\n\t\t\t\t\t\t\t\t\t\t<block v-if=\"aitem.material_price * 1 > 0\">物料费：¥{{aitem.material_price}}</block>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between mt-md\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc c-warning text-bold\">¥{{aitem.true_price}} </view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc\" style=\"color:#4D4D4D\">x{{aitem.can_refund_num}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"mt-sm\" v-if=\"aitem.is_check\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\" v-if=\"aitem.true_price * 1 > 0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">项目金额</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center c-title\">\r\n\t\t\t\t\t\t\t\t\t\t<input @input=\"checkInput($event,index,aindex,'refund_price')\"\r\n\t\t\t\t\t\t\t\t\t\t\t@blur=\"handleCheckOrderItem(0,index, aindex)\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"refundForm.order[index].order_goods[aindex].refund_price\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"number\" class=\"text-right\" placeholder=\"请输入物料费\" />\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"ml-sm\">元</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\" v-if=\"aitem.material_price * 1 > 0\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">物料费</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center c-title\">\r\n\t\t\t\t\t\t\t\t\t\t<input @input=\"checkInput($event,index,aindex,'refund_material_price')\"\r\n\t\t\t\t\t\t\t\t\t\t\t@blur=\"handleCheckOrderItem(0,index, aindex)\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-model=\"refundForm.order[index].order_goods[aindex].refund_material_price\"\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"number\" class=\"text-right\" placeholder=\"请输入物料费\" />\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"ml-sm\">元</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">退款数量</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-warp\">\r\n\t\t\t\t\t\t\t\t\t\t<button @tap.stop=\"changeNum(index,aindex,-1)\" class=\"reduce\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{borderColor:primaryColor,color:primaryColor}\"><i\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icon-jian-bold\"></i></button>\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"addreduce clear-btn\">{{aitem.refund_num}}</button>\r\n\t\t\t\t\t\t\t\t\t\t<button @tap.stop=\"changeNum(index,aindex,1)\" class=\"add\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{background:primaryColor,borderColor:primaryColor}\"><i\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icon-jia-bold\"></i></button>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">累计退款金额</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"c-title text-bold\">¥{{aitem.total_refund_price}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"flex-between f-paragraph pt-lg mt-lg b-1px-t\">\r\n\t\t\t\t\t<view class=\"c-paragraph\">服务时间</view>\r\n\t\t\t\t\t<view class=\"c-title\">{{item.time_text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\r\n\r\n\t\t<view class=\"fill-base pd-lg mt-md ml-md mr-md radius-16\" v-if=\"refundForm.order.length > 1\">\r\n\t\t\t<block v-for=\"(item, index) in refundForm.order\" :key=\"index\">\r\n\t\t\t\t<block v-if=\"item.order_goods.length > 0 && index != 0\">\r\n\t\t\t\t\t<view class=\"f-title c-title text-bold\" v-if=\"index==1\">加钟项目</view>\r\n\t\t\t\t\t<block v-for=\"(aitem,aindex) in item.order_goods\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"item-child flex-warp mt-lg\"\r\n\t\t\t\t\t\t\t:class=\"[{'pb-lg b-1px-b':aitem.is_check && aindex!=item.order_goods.length-1}]\">\r\n\t\t\t\t\t\t\t<view @tap=\"handleCheckOrderItem(1, index, aindex)\" class=\"flex-y-center\"\r\n\t\t\t\t\t\t\t\tstyle=\"height:147rpx\">\r\n\t\t\t\t\t\t\t\t<i class=\"iconfont mr-md\"\r\n\t\t\t\t\t\t\t\t\t:class=\"[{'icon-xuanze':!aitem.is_check},{'icon-xuanze-fill':aitem.is_check}]\"\r\n\t\t\t\t\t\t\t\t\t:style=\"{color:aitem.is_check ? primaryColor : '#CACACA'}\"></i>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t<view @tap=\"handleCheckOrderItem(1, index, aindex)\" class=\"flex-warp\">\r\n\t\t\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"goods-img\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"h5-image goods-img\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${aitem.goods_cover}')`}\">\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t\t\t\t<image mode=\"aspectFill\" class=\"goods-img\" :src=\"aitem.goods_cover\"></image>\r\n\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f-title c-title text-bold max-300 ellipsis\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{aitem.goods_name}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"{color:item.pay_type==2?primaryColor: [3,4,5].includes(item.pay_type)?subColor: item.pay_type == 6 ? '#11C95E' : '#333'}\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{{ statusType[item.pay_type] }}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"f-caption c-caption mt-sm\" style=\"height:30rpx\">\r\n\t\t\t\t\t\t\t\t\t\t\t<block v-if=\"aitem.material_price * 1 > 0\">物料费：¥{{aitem.material_price}}\r\n\t\t\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-between mt-md\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc c-warning text-bold\">¥{{aitem.true_price}} </view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"f-desc\" style=\"color:#4D4D4D\">x{{aitem.can_refund_num}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"mt-sm\" v-if=\"aitem.is_check\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\" v-if=\"aitem.true_price * 1 > 0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">项目金额</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center c-title\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input @input=\"checkInput($event,index,aindex,'refund_price')\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t@blur=\"handleCheckOrderItem(0,index, aindex)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"refundForm.order[index].order_goods[aindex].refund_price\"\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\" class=\"text-right\" placeholder=\"请输入物料费\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"ml-sm\">元</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\" v-if=\"aitem.material_price * 1 > 0\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">物料费</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-y-center c-title\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input @input=\"checkInput($event,index,aindex,'refund_material_price')\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t@blur=\"handleCheckOrderItem(0,index, aindex)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-model=\"refundForm.order[index].order_goods[aindex].refund_material_price\"\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\" class=\"text-right\" placeholder=\"请输入物料费\" />\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"ml-sm\">元</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">退款数量</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"flex-warp\">\r\n\t\t\t\t\t\t\t\t\t\t\t<button @tap.stop=\"changeNum(index,aindex,-1)\" class=\"reduce\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"{borderColor:primaryColor,color:primaryColor}\"><i\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icon-jian-bold\"></i></button>\r\n\t\t\t\t\t\t\t\t\t\t\t<button class=\"addreduce clear-btn\">{{aitem.refund_num}}</button>\r\n\t\t\t\t\t\t\t\t\t\t\t<button @tap.stop=\"changeNum(index,aindex,1)\" class=\"add\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"{background:primaryColor,borderColor:primaryColor}\"><i\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"iconfont icon-jia-bold\"></i></button>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-between f-paragraph mt-md\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"c-paragraph\">累计退款金额</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"c-title text-bold\">¥{{aitem.total_refund_price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"flex-between f-paragraph pt-lg mt-lg b-1px-t\">\r\n\t\t\t\t\t\t<view class=\"c-paragraph\">服务时间</view>\r\n\t\t\t\t\t\t<view class=\"c-title\">{{item.time_text}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"fill-base pd-lg mt-md ml-md mr-md radius-16\">\r\n\t\t\t<view class=\"flex-between f-paragraph mb-md\" v-if=\"refundForm.car_price*1>0\">\r\n\t\t\t\t<view class=\"c-paragraph\">车费</view>\r\n\t\t\t\t<view class=\"flex-y-center c-title\" v-if=\"refundForm.can_refund_num === refundForm.all_refund_num\">\r\n\t\t\t\t\t<input @input=\"checkInput($event,-1,-1,'refund_car_price')\" @blur=\"handleCheckOrderItem(3)\"\r\n\t\t\t\t\t\tv-model=\"refundForm.refund_car_price\" type=\"number\" class=\"text-right\" placeholder=\"请输入车费\" />\r\n\t\t\t\t\t<view class=\"ml-sm\">元</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"c-title\" v-else>¥{{refundForm.refund_car_price}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between f-paragraph mb-md\" v-if=\"refundForm.discount*1>0\">\r\n\t\t\t\t<view class=\"c-paragraph\">卡券抵扣</view>\r\n\t\t\t\t<view class=\"c-title\">¥{{ refundForm.discount }}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between f-paragraph\">\r\n\t\t\t\t<view class=\"c-paragraph\">总计</view>\r\n\t\t\t\t<view class=\"c-warning text-bold\">¥{{refundForm.total_refund_price}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"space-max-footer\"></view>\r\n\r\n\t\t<view class=\"refund-bottom-info fill-base fix pl-lg pr-lg\">\r\n\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view @tap=\"handleCheckOrderItem(2)\" class=\"flex-y-center\" v-if=\"refundForm.can_refund_num > 0\">\r\n\t\t\t\t\t\t<i class=\"iconfont mr-md\"\r\n\t\t\t\t\t\t\t:class=\"[{'icon-xuanze':!refundForm.is_check},{'icon-xuanze-fill':refundForm.is_check}]\"\r\n\t\t\t\t\t\t\t:style=\"{color:refundForm.is_check ? primaryColor : '#CACACA'}\"></i>全选\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t<button @tap.stop=\"$util.goUrl({url:1,openType:`navigateBack`})\" class=\"clear-btn order\">取消</button>\r\n\t\t\t\t\t<button @tap.stop=\"toConfirmRefund\" class=\"clear-btn order\"\r\n\t\t\t\t\t\t:style=\"{color:'#fff',background:primaryColor,borderColor:primaryColor}\">确认</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"space-safe\"></view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tstatusType: {\r\n\t\t\t\t\t'-1': '已取消',\r\n\t\t\t\t\t1: '待支付',\r\n\t\t\t\t\t2: '待接单',\r\n\t\t\t\t\t3: '待服务',\r\n\t\t\t\t\t4: this.$t('action.attendantName') + '出发',\r\n\t\t\t\t\t5: this.$t('action.attendantName') + '到达',\r\n\t\t\t\t\t6: '服务中',\r\n\t\t\t\t\t7: '已完成',\r\n\t\t\t\t\t8: '待转单'\r\n\t\t\t\t},\r\n\t\t\t\trefundForm: {},\r\n\t\t\t\tlockTap: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tagent = 0\r\n\t\t\t} = options\r\n\t\t\toptions.agent = agent * 1\r\n\t\t\tthis.options = options\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getCoachInfo']),\r\n\t\t\t...mapMutations(['updateTechnicianItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!this.configInfo.id || refresh) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t}\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\ttry {\r\n\r\n\t\t\t\t\tlet data = await this.$api[methodKey].canRefundOrderInfo({\r\n\t\t\t\t\t\torder_id: id\r\n\t\t\t\t\t})\r\n\t\t\t\t\tdata.is_check = false\r\n\t\t\t\t\tdata.refund_car_price = data.car_price\r\n\t\t\t\t\tdata.total_refund_price = 0\r\n\t\t\t\t\tdata.all_refund_num = 0\r\n\t\t\t\t\tlet rnum = 0\r\n\t\t\t\t\tdata.order.map(aitem => {\r\n\t\t\t\t\t\taitem.order_goods.map(bitem => {\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\tresidue_material_price: rmprice,\r\n\t\t\t\t\t\t\t\tresidue_service_price: rtprice,\r\n\t\t\t\t\t\t\t\tcan_refund_num: num,\r\n\t\t\t\t\t\t\t\ttrue_price: tprice,\r\n\t\t\t\t\t\t\t\tmaterial_price: mprice\r\n\t\t\t\t\t\t\t} = bitem\r\n\t\t\t\t\t\t\trnum += num\r\n\t\t\t\t\t\t\tbitem.is_check = false\r\n\t\t\t\t\t\t\tbitem.true_single_price = tprice\r\n\t\t\t\t\t\t\tbitem.true_single_material_price = mprice\r\n\r\n\t\t\t\t\t\t\ttprice = (rtprice / num).toFixed(2)\r\n\t\t\t\t\t\t\tmprice = (rmprice / num).toFixed(2)\r\n\t\t\t\t\t\t\tbitem.true_price = tprice\r\n\t\t\t\t\t\t\tbitem.material_price = mprice\r\n\t\t\t\t\t\t\tbitem.refund_price = tprice\r\n\t\t\t\t\t\t\tbitem.refund_material_price = mprice\r\n\r\n\t\t\t\t\t\t\tlet trprice = (tprice * num + mprice * num).toFixed(2) * 1\r\n\t\t\t\t\t\t\tlet ttrprice = (rtprice * 1 + rmprice * 1).toFixed(2) * 1\r\n\t\t\t\t\t\t\tbitem.total_refund_price = trprice > ttrprice ? ttrprice : trprice\r\n\t\t\t\t\t\t\tbitem.true_total_refund_price = ttrprice\r\n\t\t\t\t\t\t\tbitem.refund_num = num\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\taitem.time_text = this.$util.formatTime(aitem.start_time * 1000, 'YY-M-D h:m') +\r\n\t\t\t\t\t\t\t' - ' +\r\n\t\t\t\t\t\t\tthis.$util.formatTime(aitem.end_time * 1000, 'YY-M-D h:m')\r\n\t\t\t\t\t})\r\n\t\t\t\t\tdata.can_refund_num = rnum\r\n\t\t\t\t\tif (!rnum) {\r\n\t\t\t\t\t\tdata.total_refund_price = data.car_price\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.refundForm = data\r\n\t\t\t\t\tthis.isLoad = true\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\t\tthis.$util.getPage(-2).initRefresh()\r\n\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\turl: 1,\r\n\t\t\t\t\t\t\topenType: `navigateBack`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\t// 加/减数量\r\n\t\t\tasync changeNum(index, aindex, mol) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\trefund_num,\r\n\t\t\t\t\tcan_refund_num\r\n\t\t\t\t} = this.refundForm.order[index].order_goods[aindex]\r\n\t\t\t\tlet num = refund_num + mol\r\n\t\t\t\tif (num > can_refund_num || num < 1) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: !num ? `数量至少为1` : `数量不可大于${can_refund_num}`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.refundForm.order[index].order_goods[aindex].refund_num = num\r\n\t\t\t\tthis.handleCheckOrderItem(0, index, aindex)\r\n\t\t\t},\r\n\t\t\tcheckInput(e, index, aindex, key) {\r\n\t\t\t\tlet val = this.$util.formatMoney(e.detail.value)\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tif (index == -1) {\r\n\t\t\t\t\t\tthis.refundForm[key] = val\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex][key] = val\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//type：1单选，2全选，3车费\r\n\t\t\thandleCheckOrderItem(type = 0, index = 0, aindex = 0) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_check: all,\r\n\t\t\t\t\tcar_price,\r\n\t\t\t\t\trefund_car_price: cprice,\r\n\t\t\t\t\tcan_refund_num: cnum\r\n\t\t\t\t} = this.refundForm\r\n\r\n\t\t\t\tif (type == 1) {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tis_check\r\n\t\t\t\t\t} = this.refundForm.order[index].order_goods[aindex]\r\n\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].is_check = !is_check\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type == 2) {\r\n\t\t\t\t\tthis.refundForm.order.map(item => {\r\n\t\t\t\t\t\titem.order_goods.map(aitem => {\r\n\t\t\t\t\t\t\taitem.is_check = !all\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (type == 3) {\r\n\t\t\t\t\tlet reg = /^(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/\r\n\t\t\t\t\tif (!reg.test(cprice)) {\r\n\t\t\t\t\t\tcprice = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (cprice * 1 > car_price) {\r\n\t\t\t\t\t\tcprice = car_price\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.refundForm.refund_car_price = cprice\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type == 0) {\r\n\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tresidue_service_price: service,\r\n\t\t\t\t\t\tresidue_material_price: material,\r\n\t\t\t\t\t\ttrue_single_price: tsprice,\r\n\t\t\t\t\t\ttrue_single_material_price: tsmprice,\r\n\t\t\t\t\t\tcan_refund_num: crnum,\r\n\t\t\t\t\t\trefund_num: num,\r\n\t\t\t\t\t\trefund_price: rtprice,\r\n\t\t\t\t\t\trefund_material_price: rmprice,\r\n\t\t\t\t\t\ttrue_total_refund_price: ttrprice\r\n\t\t\t\t\t} = this.refundForm.order[index].order_goods[aindex]\r\n\r\n\t\t\t\t\tlet reg = /^(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/\r\n\t\t\t\t\tif (!reg.test(rtprice) || rtprice * 1 == 0) {\r\n\t\t\t\t\t\trtprice = service * 1 === 0 ? 0 : 0.01\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!reg.test(rmprice)) {\r\n\t\t\t\t\t\trmprice = 0\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ttsprice = crnum === num ? (service * 1 / num).toFixed(2) : tsprice\r\n\t\t\t\t\ttsmprice = crnum === num ? (material * 1 / num).toFixed(2) : tsmprice\r\n\t\t\t\t\trtprice = rtprice * 1 > tsprice * 1 ? tsprice : rtprice\r\n\t\t\t\t\trmprice = rmprice * 1 > tsmprice * 1 ? tsmprice : rmprice\r\n\r\n\t\t\t\t\tlet trprice = (rtprice * num + rmprice * num).toFixed(2)\r\n\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].true_price = tsprice\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].refund_price = rtprice\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].material_price = tsmprice\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].refund_material_price = rmprice\r\n\t\t\t\t\t\tthis.refundForm.order[index].order_goods[aindex].total_refund_price = trprice * 1 >\r\n\t\t\t\t\t\t\tttrprice ? ttrprice : trprice\r\n\t\t\t\t\t}, 0)\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet total = 0\r\n\t\t\t\t\tlet anum = 0\r\n\t\t\t\t\tlet not = 0\r\n\t\t\t\t\tthis.refundForm.order.map(item => {\r\n\t\t\t\t\t\titem.order_goods.map(aitem => {\r\n\t\t\t\t\t\t\tif (aitem.is_check) {\r\n\t\t\t\t\t\t\t\ttotal += aitem.total_refund_price * 1\r\n\t\t\t\t\t\t\t\tanum += aitem.refund_num\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tnot++\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\tcprice = cnum === anum ? cprice : 0\r\n\t\t\t\t\ttotal += cprice * 1\r\n\t\t\t\t\tthis.refundForm.total_refund_price = total.toFixed(2)\r\n\t\t\t\t\tthis.refundForm.all_refund_num = anum\r\n\t\t\t\t\tthis.refundForm.is_check = not === 0\r\n\t\t\t\t}, 100)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * @name: 确认退款\r\n\t\t\t */\r\n\t\t\tasync toConfirmRefund() {\r\n\t\t\t\tlet subForm = JSON.parse(JSON.stringify(this.refundForm))\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcan_refund_num: cnum,\r\n\t\t\t\t\tall_refund_num: anum,\r\n\t\t\t\t\trefund_car_price: cprice,\r\n\t\t\t\t\torder: list\r\n\t\t\t\t} = subForm\r\n\t\t\t\tcprice = cnum === anum ? cprice : 0\r\n\t\t\t\tlet order = []\r\n\t\t\t\tlet snum = 0\r\n\t\t\t\tlet csnum = 0\r\n\t\t\t\tlet sorder = list.filter(item => {\r\n\t\t\t\t\treturn !item.is_add\r\n\t\t\t\t})\r\n\t\t\t\tif (sorder.length > 0) {\r\n\t\t\t\t\tsorder[0].order_goods.map(item => {\r\n\t\t\t\t\t\tsnum += item.can_refund_num\r\n\t\t\t\t\t\tif (item.is_check) {\r\n\t\t\t\t\t\t\tcsnum += item.refund_num\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (snum === csnum && cnum !== anum) {\r\n\t\t\t\t\t\tthis.$message.error(`请把加钟项目一并退款哦`)\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlist.map(item => {\r\n\t\t\t\t\tlet arr = item.order_goods.filter(aitem => {\r\n\t\t\t\t\t\treturn aitem.is_check\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (arr.length > 0) {\r\n\t\t\t\t\t\tlet list = arr.map(bitem => {\r\n\t\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\t\trefund_num: rnum,\r\n\t\t\t\t\t\t\t\trefund_price: rprice,\r\n\t\t\t\t\t\t\t\trefund_material_price: mprice,\r\n\t\t\t\t\t\t\t\tresidue_service_price: rsprice,\r\n\t\t\t\t\t\t\t\tresidue_material_price: rmprice\r\n\t\t\t\t\t\t\t} = bitem\r\n\t\t\t\t\t\t\tlet trprice = (rprice * rnum).toFixed(2) * 1\r\n\t\t\t\t\t\t\tlet tmprice = (mprice * rnum).toFixed(2) * 1\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\tid: bitem.id,\r\n\t\t\t\t\t\t\t\tnum: rnum,\r\n\t\t\t\t\t\t\t\ttrue_price: trprice > rsprice ? rsprice : trprice,\r\n\t\t\t\t\t\t\t\tmaterial_price: tmprice > rmprice ? rmprice : tmprice\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\torder.push({\r\n\t\t\t\t\t\t\torder_id: item.order_id,\r\n\t\t\t\t\t\t\tlist,\r\n\t\t\t\t\t\t\tcar_price: item.is_add ? 0 : cprice\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif (order.length === 0) {\r\n\t\t\t\t\torder.push({\r\n\t\t\t\t\t\torder_id: list[0].order_id,\r\n\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\tcar_price: cprice\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.$api[methodKey].applyOrderRefund({\r\n\t\t\t\t\t\torder\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `操作成功`\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\t\tthis.$util.getPage(-2).initRefresh()\r\n\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\turl: 1,\r\n\t\t\t\t\t\t\topenType: `navigateBack`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.user-order-refund {\r\n\r\n\r\n\t\t.item-child {\r\n\r\n\t\t\t.icon-xuanze,\r\n\t\t\t.icon-xuanze-fill {\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.goods-img {\r\n\t\t\t\twidth: 147rpx;\r\n\t\t\t\theight: 147rpx;\r\n\t\t\t\tborder-radius: 26rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.goods-num {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.textarea-info {\r\n\t\t\t\tbackground: #F7F7F7;\r\n\r\n\t\t\t\t.input-textarea {\r\n\t\t\t\t\twidth: 570rpx;\r\n\t\t\t\t\theight: 300rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.item-child.b-1px-b:after {\r\n\t\t\tleft: 58rpx;\r\n\t\t}\r\n\r\n\t\t.refund-bottom-info {\r\n\t\t\tbottom: 0;\r\n\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.flex-between {\r\n\t\t\t\theight: 122rpx;\r\n\r\n\t\t\t\t.order {\r\n\t\t\t\t\twidth: 189rpx;\r\n\t\t\t\t\theight: 76rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\t\tborder: 1rpx solid #C7C7C7;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./refund.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110363424\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}