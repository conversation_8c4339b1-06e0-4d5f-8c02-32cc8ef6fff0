{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?c67d", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?3b0a", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?e6fc", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?a4d6", "uni-app:///agent/pages/technician/list.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?a9ce", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/list.vue?c848"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "options", "placeholder", "tabList", "id", "title", "number", "activeIndex", "authStatusType", "statusType", "cityType", "loading", "isLoad", "param", "page", "limit", "status", "list", "sh_info", "sh_time", "sh_text", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "onLoad", "uni", "onPullDownRefresh", "onReachBottom", "methods", "initIndex", "refresh", "bg", "handerTabChange", "initRefresh", "toSearch", "getList", "oldList", "newList", "toReject", "goDetail", "is_update", "admin_id", "aid", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAuxB,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+G3yB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MACAC;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAjB;MACA;MACAkB;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA;IACAC;MACAtB;IACA;IACA;EACA;EACAuB;IAEAD;IAEA;IACAA;EACA;EACAE;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBASA;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEAC,UACA,OADArB;gBAGAD,SACA,mCADAZ;gBAEAS;kBACAG;gBACA;gBACA;kBACAH;kBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA0B;gBACA;kBACA;gBACA;kBACAA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA,4BAGA;QAFApB;QACAD;MAEA;QACAC;QACAD;MACA;MACA;IACA;IACAsB;MACA,6BAIA;QAHArC;QACAsC;QACAC;MAEA,IACAC,MACA,UADAD;MAEA;MACA;QACAE;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAAs9C,CAAgB,06CAAG,EAAC,C;;;;;;;;;;;ACA1+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/technician/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/technician/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=398f1c76&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/technician/list.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=398f1c76&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoad ? _vm.tabList.length : null\n  var g1 =\n    _vm.isLoad && _vm.loading\n      ? _vm.list.current_page >= _vm.list.last_page && _vm.list.data.length > 0\n      : null\n  var g2 = _vm.isLoad\n    ? !_vm.loading && _vm.list.data.length <= 0 && _vm.list.current_page == 1\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/technician/apply\",\n      })\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.reject_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"agent-technician-list\" v-if=\"isLoad\">\r\n\t\t<fixed>\r\n\t\t\t<view class=\"search-info fill-base pt-lg pb-md\">\r\n\t\t\t\t<view class=\"flex-center pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t<view @tap.stop=\"$util.goUrl({url:`/agent/pages/technician/apply`})\"\r\n\t\t\t\t\t\tclass=\"dynamic-btn flex-center f-caption mr-lg radius\"\r\n\t\t\t\t\t\t:style=\"{color:primaryColor,border:`1rpx solid ${primaryColor}`}\">\r\n\t\t\t\t\t\t<i class=\"iconfont icon-jia-bold\"></i>\r\n\t\t\t\t\t\t添加\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t<search @input=\"toSearch\" type=\"input\" :padding=\"0\" :radius=\"30\" backgroundColor=\"#F0F0F0\"\r\n\t\t\t\t\t\t\t:placeholder=\"placeholder\">\r\n\t\t\t\t\t\t</search>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"padding-right:30rpx\">\r\n\t\t\t\t\t<tab @change=\"handerTabChange\" :isLine=\"true\" :list=\"tabList\" :activeIndex=\"activeIndex*1\"\r\n\t\t\t\t\t\tcolor=\"#9D9D9D\" :activeColor=\"primaryColor\" :width=\"100/tabList.length + '%'\" height=\"100rpx\"\r\n\t\t\t\t\t\t:numberType=\"2\"></tab>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</fixed>\r\n\t\t<view @tap.stop=\"goDetail(index)\" class=\"list-item fill-base flex-warp mt-md ml-md mr-md pd-lg radius-16\"\r\n\t\t\tv-for=\"(item,index) in list.data\" :key=\"index\">\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"avatar rel\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view class=\"avatar radius\">\r\n\t\t\t\t\t\t<view class=\"h5-image avatar radius\" :style=\"{ backgroundImage : `url('${item.work_img}')`}\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar radius\" :src=\"item.work_img\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t<view class=\"item-tag flex-center f-icontext c-base radius-20\"\r\n\t\t\t\t\t\t:class=\"[{'have-user':item.auth_status}]\">\r\n\t\t\t\t\t\t{{authStatusType[item.auth_status]}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t<view class=\"f-icontext c-paragraph\">ID：{{item.id}}</view>\r\n\t\t\t\t\t<view class=\"f-paragraph\"\r\n\t\t\t\t\t\t:style=\"{color:item.status==1||item.is_update==1?primaryColor:item.status==4?subColor:item.status==3?'#E82F21':'#999999'}\">\r\n\t\t\t\t\t\t{{statusType[item.status]}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-y-center pb-lg b-1px-b\">\r\n\t\t\t\t\t<view class=\"f-paragraph c-black text-bold ellipsis\" style=\"max-width: 377rpx;\">{{item.coach_name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"status-btn flex-center f-icontext ml-md\"\r\n\t\t\t\t\t\t:style=\"{color:item.admin_add==1?primaryColor:subColor,border:`1rpx solid ${item.admin_add==1?primaryColor:subColor}`}\">\r\n\t\t\t\t\t\t{{item.admin_add==1?'平台添加':'用户申请'}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f-icontext c-paragraph mt-md\">{{item.mobile}}</view>\r\n\t\t\t\t<view class=\"f-icontext c-paragraph mt-sm\">\r\n\t\t\t\t\t所属代理商：{{item.admin_name || '-'}}{{item.admin_id?` (${cityType[item.city_type]})`:''}}</view>\r\n\t\t\t\t<view class=\"f-icontext c-paragraph mt-sm\">申请时间：{{item.create_time}}</view>\r\n\t\t\t\t<view class=\"flex-between mt-md\">\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t\t<view class=\"edit-btn flex-center f-desc radius\"\r\n\t\t\t\t\t\t\t:style=\"{color:primaryColor,border:`1rpx solid ${primaryColor}`}\"\r\n\t\t\t\t\t\t\tv-if=\"!item.is_update && item.admin_id == list.admin_id\">编辑\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"edit-btn flex-center f-desc radius\"\r\n\t\t\t\t\t\t\t:style=\"{color:subColor,border:`1rpx solid ${subColor}`}\"\r\n\t\t\t\t\t\t\tv-if=\"item.is_update || (item.admin_id != list.admin_id)\">查看\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @tap.stop=\"toReject(index)\" class=\"edit-btn flex-center f-desc radius ml-md\"\r\n\t\t\t\t\t\t\t:style=\"{color:primaryColor,border:`1rpx solid ${primaryColor}`}\" v-if=\"item.status ==4\">\r\n\t\t\t\t\t\t\t驳回原因</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<load-more :noMore=\"list.current_page>=list.last_page&&list.data.length>0\" :loading=\"loading\" v-if=\"loading\">\r\n\t\t</load-more>\r\n\t\t<abnor v-if=\"!loading&&list.data.length<=0&&list.current_page==1\"></abnor>\r\n\t\t<view class=\"space-footer\"></view>\r\n\r\n\t\t<uni-popup ref=\"reject_item\" type=\"center\" :custom=\"true\" :zIndex=\"999\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">驳回原因</view>\r\n\t\t\t\t<scroll-view scroll-y @touchmove.stop.prevent class=\"refund-text mt-lg\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text decode=\"emsp\" style=\"word-break:break-all;\">{{sh_info.sh_text || '没有填写原因哦'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-md f-caption c-caption\">驳回时间：{{sh_info.sh_time}}</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.reject_item.close()\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\toptions: {},\r\n\t\t\t\tplaceholder: '请输入' + this.$t('action.attendantName') + '姓名/手机号',\r\n\t\t\t\ttabList: [{\r\n\t\t\t\t\tid: 0,\r\n\t\t\t\t\ttitle: '全部',\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '申请中',\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\ttitle: '已授权',\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\ttitle: '已驳回',\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: -1,\r\n\t\t\t\t\ttitle: '重新审核',\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tauthStatusType: {\r\n\t\t\t\t\t0: '未认证',\r\n\t\t\t\t\t1: '认证中',\r\n\t\t\t\t\t2: '已认证',\r\n\t\t\t\t\t3: '认证失败'\r\n\t\t\t\t},\r\n\t\t\t\tstatusType: {\r\n\t\t\t\t\t1: '申请中',\r\n\t\t\t\t\t2: '已授权',\r\n\t\t\t\t\t3: '取消授权',\r\n\t\t\t\t\t4: '已驳回'\r\n\t\t\t\t},\r\n\t\t\t\tcityType: {\r\n\t\t\t\t\t1: '城市',\r\n\t\t\t\t\t2: '区县',\r\n\t\t\t\t\t3: '省'\r\n\t\t\t\t},\r\n\t\t\t\tloading: true,\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tparam: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t\tstatus: 0\r\n\t\t\t\t},\r\n\t\t\t\tlist: {\r\n\t\t\t\t\tdata: []\r\n\t\t\t\t},\r\n\t\t\t\tsh_info: {\r\n\t\t\t\t\tsh_time: '',\r\n\t\t\t\t\tsh_text: ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tthis.options = options\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.$t('action.attendantName') + '管理'\r\n\t\t\t})\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.showNavigationBarLoading()\r\n\t\t\t// #endif\r\n\t\t\tthis.initRefresh();\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.list.current_page >= this.list.last_page || this.loading) return;\r\n\t\t\tthis.param.page = this.param.page + 1;\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\thanderTabChange(index) {\r\n\t\t\t\tthis.activeIndex = index\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tthis.param.page = 1;\r\n\t\t\t\tthis.getList();\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\ttoSearch(val) {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.param.name = val\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tasync getList() {\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlist: oldList\r\n\t\t\t\t} = this\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: status\r\n\t\t\t\t} = this.tabList[this.activeIndex]\r\n\t\t\t\tlet param = Object.assign({}, this.param, {\r\n\t\t\t\t\tstatus\r\n\t\t\t\t})\r\n\t\t\t\tif (status == -1) {\r\n\t\t\t\t\tparam.is_update = 1\r\n\t\t\t\t\tdelete param.status\r\n\t\t\t\t}\r\n\t\t\t\tlet newList = await this.$api.agent.coachList(param)\r\n\t\t\t\tif (this.param.page == 1) {\r\n\t\t\t\t\tthis.list = newList\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnewList.data = oldList.data.concat(newList.data)\r\n\t\t\t\t\tthis.list = newList\r\n\t\t\t\t}\r\n\t\t\t\tthis.tabList[1].number = newList.ing\r\n\t\t\t\tthis.isLoad = true\r\n\t\t\t\tthis.loading = false\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t},\r\n\t\t\ttoReject(index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tsh_text,\r\n\t\t\t\t\tsh_time\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tthis.sh_info = {\r\n\t\t\t\t\tsh_text,\r\n\t\t\t\t\tsh_time\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.reject_item.open()\r\n\t\t\t},\r\n\t\t\tgoDetail(index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tis_update,\r\n\t\t\t\t\tadmin_id\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tadmin_id: aid\r\n\t\t\t\t} = this.list\r\n\t\t\t\tlet view = is_update || (admin_id != aid) ? 1 : 0\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl: `/agent/pages/technician/apply?id=${id}&view=${view}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.agent-technician-list {\r\n\t\t.search-info {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.dynamic-btn {\r\n\t\t\t\twidth: 134rpx;\r\n\t\t\t\theight: 56rpx;\r\n\t\t\t\ttransform: rotateZ(360deg);\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list-item {\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 124rpx;\r\n\t\t\t\theight: 124rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item-tag {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tcolor: #4A4A4A;\r\n\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\tmargin-top: 19rpx;\r\n\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.have-user {\r\n\t\t\t\tcolor: #EBDDB1;\r\n\t\t\t\tbackground: linear-gradient(270deg, #4C545A 0%, #282B34 100%);\r\n\t\t\t}\r\n\r\n\t\t\t.status-btn {\r\n\t\t\t\twidth: 110rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\ttransform: rotateZ(360deg);\r\n\t\t\t}\r\n\r\n\t\t\t.edit-btn {\r\n\t\t\t\tmin-width: 120rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\ttransform: rotateZ(360deg);\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110369771\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}