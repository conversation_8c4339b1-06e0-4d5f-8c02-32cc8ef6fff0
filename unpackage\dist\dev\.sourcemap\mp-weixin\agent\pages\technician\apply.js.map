{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?1750", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?681a", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?0c1b", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?7581", "uni-app:///agent/pages/technician/apply.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?f501", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/technician/apply.vue?f91c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "wPicker", "data", "isLoad", "options", "edit_base_info", "cityList", "cityIndex", "storeList", "storeIndex", "toDay", "toDayTime", "showKey", "showTime", "sexList", "id", "title", "workList", "fdd_agreement", "form", "user_id", "nick<PERSON><PERSON>", "coach_name", "mobile", "sex", "birthday", "work_time", "city_id", "store_id", "store_name", "free_fare_distance", "lng", "lat", "address", "text", "id_code", "id_card", "id_card_fan", "id_card_people", "license", "work_img", "self_img", "rule", "name", "checkType", "errorMsg", "regType", "maxNum", "unit", "have_user_id", "lockTap", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "location", "locaRefuse", "useChooseLocation", "userCoachStatus", "changeOnAddr", "noChangeLoca", "onLoad", "view", "coach_status", "admin_id", "uni", "onShow", "key", "val", "watch", "noloca", "setTimeout", "unix", "methods", "initIndex", "refresh", "bg", "cur_time", "privacyCheck", "Promise", "imgArr", "path", "imgArrs", "initRefresh", "toResetUtilLoca", "toOpenLocation", "type", "checkApp", "getStoreList", "store", "getCityList", "coach_position", "cur_unix", "loca", "is_util_loca", "getUtilLocation", "initUtilLocaData", "city", "toFddSign", "viewpdf_url", "url", "picker<PERSON><PERSON><PERSON>", "imgUpload", "imagelist", "e", "imgtype", "toSetItem", "toShowTime", "toCheckEdit", "edit", "onConfirm", "toChooseLocation", "initChooseLoca", "param", "err", "loca_data", "latitude", "longitude", "toChooseUser", "validate", "free_fare_bear", "item", "submit", "arr", "msg", "is_work", "start", "start_time", "end", "end_time", "work", "is_edit", "methodModel", "openType", "filters", "handleStartEndTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAwxB,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC4P5yB;AAEA;AAIA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAF;QACAC;MACA;QACAD;QACAC;MACA;MACAE;MACAC;QACAJ;QACAK;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MAAA,mDACA,mDACA,uDACA,oDACA,uDACA,2DACA,gBACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAL;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAI;MACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,cAIA1D,QAFAW,mEAEAX,QADA2D;cAEA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAEAC,eACA,MADAA;cAGAC,WACA,WADAA;cAEAtB;cACAuB;gBACAlD;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAmD;IACA;MACA;QACAC;QACAC;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACAb;MACA;QACA;MACA;IACA;IACAG;MACA,IACAW,SACA,kBADAA;MAEA;QACA;MACA;IACA;IACAV;MAAA;MACAW;QACA,0BAIA;UAHAxC;UACAD;UAAA,4CACA0C;UAAAA;QAEA,IACAF,SACA,oBADAA;QAEA;QACA;UACA;QACA;MACA;IACA;EACA;EACAG,uDACA,0DACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MASA,2DACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;kBACAC;gBACA;gBACAC;gBACA;gBACA;gBAGAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIAC;cAAA;gBAAA,oBAGA,eADAjE;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACAA;gBACA;cAAA;gBAFAb;gBAGAA;gBAGA+E;gBACAA;kBACA/E;oBACA;sBACAgF;oBACA;kBACA;gBACA;gBACA;kBACAhF;kBACAA;kBACAA;gBACA;gBACAiF;gBACAA;kBACAjF;oBACAgF;kBACA;gBACA;gBACA;kBACA;gBACA;gBACA;kBACA;gBACA;gBACAhF;gBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkF;MACA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAoBA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAP;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACAQ;kBACAC;gBACA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,wBAGA,2BADAC;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAxF;gBACAA;kBACAa;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAnC,WACA,OADAA;gBAAA,wBAMA,wBAFAQ,6CACA4B;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,MACA5B;kBAAA;kBAAA;gBAAA;gBAAA,sBAKA,iEAHAhC,6HACAD,8HACA0C;gBAEAoB;gBACAtB;gBACA;kBACAuB;oBACA9D;oBACAD;oBACAgE;kBACA;kBACA;oBACA3B;oBACAC;kBACA;gBACA;gBACA;kBACAD;kBACAC;oBACAE;kBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAEA;kBACAH;kBACAC;gBACA;gBAAA;cAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OA2BA;cAAA;gBAAA;gBAAA,+CAFAhE;gBAAAA;gBAAA,+CACAD;gBAAAA;gBAEAsC;kBACArC;kBACAD;kBACAgE;gBACA;gBACA;kBACA3B;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA7B;kBACAC;oBACAE;kBACA;gBACA;gBAAA,kBAIA,uDAFAxC,2GACAC;gBAAA;gBAAA,OAEA;kBACAD;kBACAC;gBACA;cAAA;gBAHAkE;gBAIA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA/B;kBACAC;gBACA;gBAAA,wBAGA,qBADA+B;gBAEA;kBACAhC;kBACAC;gBACA;gBAKA;kBACAgC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;MACA;QACA;QACA;UACA;YACAtF;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAuF;MACA,IACAC,YAEAC,EAFAD;QACAE,UACAD,EADAC;MAEA;IACA;IACAC;MACA;MACA;MACA,IACA5F,KACA,kBADAA;MAEA;IACA;IACA6F;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA,IACAxG,iBACA,KADAA;MAEA;QACAyG;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAF;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;kBACA1C;kBACAC;gBACA;gBAAA,IAkBA;kBAAA;kBAAA;gBAAA;gBACA;kBACAD;kBACAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAaA;kBACAkB;gBACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA0B;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAAA,OACAhD;cAAA;gBAAA;gBAAA;gBAAAiD;gBAAAC;gBAAA,OAMAD;kBACAxE;kBACAV;kBACAoF;kBACAC;gBACA,gCATA3E,wEACAV,qFACAoF,qFACAC;gBAOArF;gBACA;kBACAmC;kBACAC;gBACA;gBAAA,IACApC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAsF;MACA;MACA;MACA,IACAtE,eACA,KADAA;MAEA;MACA;QACAoD;MACA;IACA;IACA;IACAmB;MACA,uBAGA;QAFAC;QACA3F;MAEA;MACA;QACA,IACAa,OACA+E,KADA/E;QAEA;UACA;YACA;cACA+E;YACA;YACAF;UACA;QACA;UACAA;QACA;MACA;MACA;MACA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;gBACAU;gBACAA;kBACAV;gBACA;gBACAA;kBACA;gBACA;gBACAA;kBACA;gBACA;gBACAW;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACA7G;gBACA;gBAAA;cAAA;gBAAA,iBAQAkG,MAHAY,gEACAC,QAEAb,MAFAc,YACAC,MACAf,MADAgB;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACA;kBACAnH;gBACA;gBAAA;cAAA;gBAIAkG;gBACAA;gBACAA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,wBAIA,gBADAkB;gBAEAC;gBAAA;gBAAA,OACA;cAAA;gBACA;gBACA;kBACArH;gBACA;gBACAwD;kBACA;kBACA;oBACA6B;oBACAiC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9D;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;EAAA,EACA;EACA+D;IACAC;MACA;MACA,IACAR,aAEA3D,IAFA2D;QACAE,WACA7D,IADA6D;MAEA;MACA;MACA;QACAhG;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACl+BA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,26CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/technician/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/technician/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=6f9ac274&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/technician/apply.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=6f9ac274&\"", "var components\ntry {\n  components = {\n    wPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/w-picker/w-picker\" */ \"@/components/w-picker/w-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoad\n    ? _vm.configInfo.plugAuth.store && _vm.storeList.length > 0\n    : null\n  var m0 = _vm.isLoad ? _vm.$t(\"action.attendantName\") : null\n  var g1 = _vm.isLoad ? _vm.form.text.length : null\n  var g2 = _vm.isLoad && !(g1 > 300) ? _vm.form.text.length : null\n  var f0 =\n    _vm.isLoad && _vm.form.is_work === 1\n      ? _vm._f(\"handleStartEndTime\")(_vm.form, _vm.toDay)\n      : null\n  var g3 = _vm.isLoad\n    ? _vm.fdd_agreement &&\n      _vm.fdd_agreement.hasOwnProperty(\"viewpdf_url\") &&\n      _vm.edit_base_info\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n        g2: g2,\n        f0: f0,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"apply-pages\">\r\n\t\t<block v-if=\"isLoad\">\r\n\t\t\t<view class=\"apply-form\">\r\n\t\t\t\t<view class=\"fill-base radius-16\">\r\n\t\t\t\t\t<view @tap.stop=\"toChooseUser\" class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text\">关联用户</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 text\">\r\n\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t{{form.user_id ? form.nickName :'请选择'}}\r\n\t\t\t\t\t\t\t\t<i class=\"iconfont icon-right ml-sm\" style=\"font-size: 28rpx;\"></i>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>姓名</view>\r\n\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.coach_name\" type=\"text\"\r\n\t\t\t\t\t\t\tclass=\"item-input flex-1\" maxlength=\"20\" :placeholder=\"rule[0].errorMsg\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>性别</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 flex-y-center\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"toSetItem(index,'sex','sexList')\" class=\"flex-y-center\"\r\n\t\t\t\t\t\t\t\t:class=\"[{'mr-lg':item.id==0}]\" :style=\"{color:form.sex == item.id ? primaryColor:''}\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in sexList\" :key=\"index\"><i class=\"iconfont icon-xuanze mr-sm\"\r\n\t\t\t\t\t\t\t\t\t:class=\"[{'icon-xuanze-fill':form.sex == item.id}]\"></i>{{item.title}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between ml-lg mr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>生日</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 text\">\r\n\t\t\t\t\t\t\t<picker :disabled=\"!edit_base_info ? true :false\" @change=\"pickerChange($event,'birthday')\"\r\n\t\t\t\t\t\t\t\tmode=\"date\" :end=\"endYear\" :value=\"form.birthday\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t{{form.birthday||'请选择'}}\r\n\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-right ml-sm\" style=\"font-size: 28rpx;\"></i>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>手机号</view>\r\n\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.mobile\" type=\"text\"\r\n\t\t\t\t\t\t\tclass=\"item-input flex-1\" maxlength=\"11\" :placeholder=\"rule[2].errorMsg\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>从业年份\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.work_time\" type=\"number\"\r\n\t\t\t\t\t\t\tclass=\"item-input flex-1\" :placeholder=\"rule[3].errorMsg\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>意向工作城市\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 text\">\r\n\t\t\t\t\t\t\t<picker :disabled=\"!edit_base_info ? true :false\" @change=\"pickerChange($event,'city')\"\r\n\t\t\t\t\t\t\t\t:value=\"cityIndex\" :range=\"cityList\" range-key=\"title\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t{{cityIndex!=-1?cityList[cityIndex].title:'请选择'}}\r\n\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-right ml-sm\" style=\"font-size: 28rpx;\"></i>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\"\r\n\t\t\t\t\t\tv-if=\"configInfo.plugAuth.store && storeList.length>0\">\r\n\t\t\t\t\t\t<view class=\"item-text\">挂靠门店</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 text\">\r\n\t\t\t\t\t\t\t<picker :disabled=\"!edit_base_info ? true :false\" @change=\"pickerChange($event,'store')\"\r\n\t\t\t\t\t\t\t\t:value=\"storeIndex\" :range=\"storeList\" range-key=\"title\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"max-400 ellipsis\">\r\n\t\t\t\t\t\t\t\t\t\t{{!edit_base_info?form.store_name:storeIndex!=-1?storeList[storeIndex].title:'请选择'}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<i class=\"iconfont icon-right ml-sm\" style=\"font-size: 28rpx;\"></i>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-if=\"configInfo.free_fare_bear\">\r\n\t\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t\t<view class=\"item-text\" style=\"height:auto\">免出行费半径距离</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-1 flex-center\">\r\n\t\t\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.free_fare_distance\"\r\n\t\t\t\t\t\t\t\t\ttype=\"digit\" class=\"item-input flex-1\" :placeholder=\"rule[5].errorMsg\" />\r\n\t\t\t\t\t\t\t\t<view class=\"c-title\">km</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>所在地址\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input text flex-1\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"toChooseLocation\" class=\"flex-between\">\r\n\t\t\t\t\t\t\t\t<view>{{form.address || `点击右边图标设置`}}</view>\r\n\t\t\t\t\t\t\t\t<i class=\"iconfont iconjuli ml-sm\" :style=\"{color: primaryColor}\"></i>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\" style=\"width:auto\"><i\r\n\t\t\t\t\t\t\t\tclass=\"iconfont icon-required c-warning\"></i>{{$t('action.attendantName')}}简介</view>\r\n\t\t\t\t\t\t<input :disabled=\"true\" type=\"text\" class=\"item-input flex-1\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<textarea :disabled=\"!edit_base_info ? true :false\" v-model=\"form.text\" class=\"item-textarea pd-lg\"\r\n\t\t\t\t\t\tmaxlength=\"300\" :placeholder=\"rule[7].errorMsg\" />\r\n\t\t\t\t\t<view class=\"text-right pb-lg pr-lg\">\r\n\t\t\t\t\t\t{{form.text.length>300?300:form.text.length}}/300\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>身份证号\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.id_code\" type=\"text\"\r\n\t\t\t\t\t\t\tclass=\"item-input flex-1\" :placeholder=\"rule[8].errorMsg\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text tips flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>身份证照片\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\">图片大小不超过10M</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.id_card\" imgtype=\"id_card\" imgclass=\"md\"\r\n\t\t\t\t\t\t\ttext=\"身份证人像面\" :imgsize=\"1\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.id_card_fan\" imgtype=\"id_card_fan\" imgclass=\"md\"\r\n\t\t\t\t\t\t\ttext=\"身份证国徽面\" :imgsize=\"1\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.id_card_people\" imgtype=\"id_card_people\"\r\n\t\t\t\t\t\t\timgclass=\"md\" text=\"手持身份证照片\" :imgsize=\"1\" :isEdit=\"!edit_base_info?false:true\"></upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text tips flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>资格证书\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\">图片大小不超过10M</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\" :imagelist=\"form.license\" imgtype=\"license\"\r\n\t\t\t\t\t\t\ttext=\"上传图片\" :imgsize=\"15\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text tips flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>工作形象照\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\">图片建议尺寸: 750 * 750，大小不超过10M</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" :imagelist=\"form.work_img\" imgtype=\"work_img\" text=\"上传图片\"\r\n\t\t\t\t\t\t\t:imgsize=\"1\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text tips flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>个人生活照\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\">图片建议尺寸: 750 * n，大小不超过10M</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\" :imagelist=\"form.self_img\" filetype=\"picture\"\r\n\t\t\t\t\t\t\timgtype=\"self_img\" text=\"上传图片\" :imgsize=\"9\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg\">\r\n\t\t\t\t\t\t<view class=\"item-text tips\">个人视频介绍</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\">视频大小不超过50M</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg pb-md\">\r\n\t\t\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\" :imagelist=\"form.video\" filetype=\"video\"\r\n\t\t\t\t\t\t\timgtype=\"video\" text=\"上传视频\" :imgsize=\"1\" :isEdit=\"!edit_base_info?false:true\">\r\n\t\t\t\t\t\t</upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fill-base mt-md radius-16\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>虚拟订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-1 flex-center\">\r\n\t\t\t\t\t\t\t<input :disabled=\"!edit_base_info ? true :false\" v-model=\"form.order_num\" type=\"number\"\r\n\t\t\t\t\t\t\t\tclass=\"item-input flex-1\" :placeholder=\"rule[15].errorMsg\" />\r\n\t\t\t\t\t\t\t<view class=\"c-title\">单</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text flex-y-center\"><i class=\"iconfont icon-required c-warning\"></i>是否接单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1 flex-y-center\">\r\n\t\t\t\t\t\t\t<view @tap.stop=\"toSetItem(index,'is_work','workList')\" class=\"flex-y-center\"\r\n\t\t\t\t\t\t\t\t:class=\"[{'mr-lg':item.id==1}]\"\r\n\t\t\t\t\t\t\t\t:style=\"{color:form.is_work == item.id ? primaryColor:''}\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in workList\" :key=\"index\"><i class=\"iconfont mr-sm\"\r\n\t\t\t\t\t\t\t\t\t:class=\"[{'icon-xuanze':form.is_work != item.id},{'icon-radio-fill':form.is_work == item.id}]\"></i>{{item.title}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between pd-lg\" v-if=\"form.is_work===1\">\r\n\t\t\t\t\t\t<view @tap.stop=\"toShowTime('start_time')\" class=\"item-time flex-center flex-column\">\r\n\t\t\t\t\t\t\t<view>开始时间</view>\r\n\t\t\t\t\t\t\t<view class=\"mt-sm\" :style=\"{color:form.start_time ? primaryColor : '#999'}\">\r\n\t\t\t\t\t\t\t\t{{form.start_time || '选择时间'}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @tap.stop=\"toShowTime('end_time')\" class=\"item-time flex-center flex-column  b-1px-l\">\r\n\t\t\t\t\t\t\t<view>结束时间</view>\r\n\t\t\t\t\t\t\t<view class=\"mt-sm\" :style=\"{color:form.end_time ? primaryColor : '#999'}\">\r\n\t\t\t\t\t\t\t\t{{form | handleStartEndTime(toDay)}}{{form.end_time || '选择时间'}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap.stop=\"toFddSign\" class=\"fill-base mt-md radius-16\"\r\n\t\t\t\t\tv-if=\"fdd_agreement && fdd_agreement.hasOwnProperty('viewpdf_url') && edit_base_info\">\r\n\t\t\t\t\t<view class=\"flex-between pl-lg pr-lg b-1px-b\">\r\n\t\t\t\t\t\t<view class=\"item-text\">电子签约</view>\r\n\t\t\t\t\t\t<view class=\"item-input flex-1\" :style=\"{color:primaryColor}\">\r\n\t\t\t\t\t\t\t查看签约合同\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"space-max-footer\"></view>\r\n\r\n\t\t\t<block v-if=\"!edit_base_info\"></block>\r\n\t\t\t<fix-bottom-button @confirm=\"submit\" :text=\"[{text:'确定提交',type:'confirm'}]\" bgColor=\"#fff\" v-else>\r\n\t\t\t</fix-bottom-button>\r\n\r\n\r\n\t\t\t<w-picker :visible.sync=\"showTime\" mode=\"time\" :value=\"toDayTime\" :current=\"false\" :second=\"false\"\r\n\t\t\t\t:themeColor=\"primaryColor\" @confirm=\"onConfirm\" ref=\"time\"></w-picker>\r\n\t\t</block>\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport $util from \"@/utils/index.js\"\r\n\timport wPicker from \"@/components/w-picker/w-picker.vue\";\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\twPicker\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tedit_base_info: 1,\r\n\t\t\t\tcityList: [],\r\n\t\t\t\tcityIndex: -1,\r\n\t\t\t\tstoreList: [],\r\n\t\t\t\tstoreIndex: -1,\r\n\t\t\t\ttoDay: '',\r\n\t\t\t\ttoDayTime: '',\r\n\t\t\t\tshowKey: '',\r\n\t\t\t\tshowTime: false,\r\n\t\t\t\tsexList: [{\r\n\t\t\t\t\tid: 0,\r\n\t\t\t\t\ttitle: '男'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '女'\r\n\t\t\t\t}],\r\n\t\t\t\tworkList: [{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '接单'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 0,\r\n\t\t\t\t\ttitle: '休息'\r\n\t\t\t\t}],\r\n\t\t\t\tfdd_agreement: {},\r\n\t\t\t\tform: {\r\n\t\t\t\t\tid: 0,\r\n\t\t\t\t\tuser_id: '',\r\n\t\t\t\t\tnickName: '',\r\n\t\t\t\t\tcoach_name: '', //姓名 \r\n\t\t\t\t\tmobile: '', //手机号 \r\n\t\t\t\t\tsex: 0, //性别 \r\n\t\t\t\t\tbirthday: '',\r\n\t\t\t\t\twork_time: '', //从业年份 \r\n\t\t\t\t\tcity_id: '', // 城市id\r\n\t\t\t\t\tstore_id: '', // 门店id\r\n\t\t\t\t\tstore_name: '',\r\n\t\t\t\t\tfree_fare_distance: '',\r\n\t\t\t\t\tlng: '',\r\n\t\t\t\t\tlat: '',\r\n\t\t\t\t\taddress: '', //详细地址 \r\n\t\t\t\t\ttext: '', //备注 \r\n\t\t\t\t\tid_code: '', //身份证号\r\n\t\t\t\t\tid_card: [], //身份证\r\n\t\t\t\t\tid_card_fan: [], // 身份证反面\r\n\t\t\t\t\tid_card_people: [], //手持身份证\r\n\t\t\t\t\tlicense: [], //资格证书\r\n\t\t\t\t\twork_img: [], // 工作照\r\n\t\t\t\t\tself_img: [], // 生活照\r\n\t\t\t\t\tcity_id: '', // 城市 id\r\n\t\t\t\t\tvideo: [],\r\n\t\t\t\t\torder_num: 0,\r\n\t\t\t\t\tis_work: 1,\r\n\t\t\t\t\tstart_time: '00:00',\r\n\t\t\t\t\tend_time: '23:59'\r\n\t\t\t\t},\r\n\t\t\t\trule: [{\r\n\t\t\t\t\t\tname: \"coach_name\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"输入您的姓名\",\r\n\t\t\t\t\t\tregType: 2\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"birthday\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请选择您的生日\",\r\n\t\t\t\t\t\tregType: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"mobile\",\r\n\t\t\t\t\t\tcheckType: \"isMobile\",\r\n\t\t\t\t\t\terrorMsg: \"输入手机号\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"work_time\",\r\n\t\t\t\t\t\tcheckType: \"isNumber\",\r\n\t\t\t\t\t\terrorMsg: \"请输入从业年份，例如：5\",\r\n\t\t\t\t\t\tregType: 2\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"city_id\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请选择意向工作城市\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"free_fare_distance\",\r\n\t\t\t\t\t\tcheckType: \"isFloatNum\",\r\n\t\t\t\t\t\terrorMsg: \"请输入公里数\",\r\n\t\t\t\t\t\tregType: 2,\r\n\t\t\t\t\t\tmaxNum: '',\r\n\t\t\t\t\t\tunit: '公里'\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"address\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请选择所在地址\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"text\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请输入\" + this.$t('action.attendantName') + \"简介\",\r\n\t\t\t\t\t\tregType: 2\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: \"id_code\",\r\n\t\t\t\t\t\tcheckType: \"isIdCard\",\r\n\t\t\t\t\t\terrorMsg: \"输入您的身份证号码\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"id_card\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传身份证人像面\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"id_card_fan\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传身份证国徽面\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"id_card_people\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传手持身份证照片\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"license\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传资格证书\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"work_img\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传工作形象照\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"self_img\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请上传个人生活照\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"order_num\",\r\n\t\t\t\t\t\tcheckType: \"isNumber\",\r\n\t\t\t\t\t\terrorMsg: \"请输入虚拟订单量\"\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\thave_user_id: false,\r\n\t\t\t\tlockTap: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\tlocation: state => state.user.location,\r\n\t\t\tlocaRefuse: state => state.user.locaRefuse,\r\n\t\t\tuseChooseLocation: state => state.user.useChooseLocation,\r\n\t\t\tuserCoachStatus: state => state.user.userCoachStatus,\r\n\t\t\tchangeOnAddr: state => state.user.changeOnAddr,\r\n\t\t\tnoChangeLoca: state => state.user.noChangeLoca,\r\n\t\t}),\r\n\t\tasync onLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tid = 0,\r\n\t\t\t\t\tview = 0,\r\n\t\t\t} = options\r\n\t\t\tthis.edit_base_info = view * 1 === 1 ? 0 : 1\r\n\t\t\tthis.options = options\r\n\t\t\tthis.$util.showLoading()\r\n\t\t\tawait this.initIndex()\r\n\t\t\tlet {\r\n\t\t\t\tcoach_status\r\n\t\t\t} = this\r\n\t\t\tlet {\r\n\t\t\t\tadmin_id\r\n\t\t\t} = this.form\r\n\t\t\tlet name = this.$t('action.attendantName')\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: `${!this.edit_base_info ? '查看':id?'编辑':'新增'}${name}`\r\n\t\t\t})\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (this.useChooseLocation) {\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'useChooseLocation',\r\n\t\t\t\t\tval: false\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (!this.location.lat && this.locaRefuse) {\r\n\t\t\t\tthis.toResetUtilLoca()\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tlocaRefuse(newval, oldval) {\r\n\t\t\t\tif (!newval) {\r\n\t\t\t\t\tthis.toResetUtilLoca()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeOnAddr(newval, oldval) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tnoloca,\r\n\t\t\t\t} = this.noChangeLoca\r\n\t\t\t\tif (newval && noloca) {\r\n\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnoChangeLoca(newval, oldval) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\tunix = 0\r\n\t\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tnoloca\r\n\t\t\t\t\t} = this.noChangeLoca\r\n\t\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\t\tif (noloca && ((!lat && !lng) || !unix || (unix && (cur_unix - unix >= 1)))) {\r\n\t\t\t\t\t\tthis.getUtilLocation()\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 800)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getUserInfo']),\r\n\t\t\t...mapMutations(['updateUserItem', 'updateMapItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!this.configInfo.id || !this.configInfo.hasOwnProperty(\r\n\t\t\t\t\t\t'free_fare_bear') || refresh) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t}\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tlet cur_time = new Date(Math.ceil(new Date().getTime()))\r\n\t\t\t\tthis.toDay = this.$util.formatTime(cur_time, 'YY-M-D')\r\n\t\t\t\tthis.toDayTime = this.$util.formatTime(cur_time, 'h:m')\r\n\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck && !this.location.lat) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tawait Promise.all([this.getCityList(), this.getStoreList()])\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid = 0\r\n\t\t\t\t} = this.options\r\n\t\t\t\tif (!id) {\r\n\t\t\t\t\tthis.isLoad = true\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet data = await this.$api.agent.coachInfo({\r\n\t\t\t\t\tid\r\n\t\t\t\t})\r\n\t\t\t\tdata.free_fare_distance = data.free_fare_distance == null ? '' : data.free_fare_distance\r\n\r\n\r\n\t\t\t\tlet imgArr = ['id_card', 'license', 'self_img']\r\n\t\t\t\timgArr.map(item => {\r\n\t\t\t\t\tdata[item] = data[item] && data[item].length > 0 ? data[item].map(aitem => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpath: aitem\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}) : []\r\n\t\t\t\t})\r\n\t\t\t\tif (data.id_card && data.id_card.length > 1) {\r\n\t\t\t\t\tdata.id_card_fan = [data.id_card[1]]\r\n\t\t\t\t\tdata.id_card_people = [data.id_card[2]]\r\n\t\t\t\t\tdata.id_card.splice(1, 3)\r\n\t\t\t\t}\r\n\t\t\t\tlet imgArrs = ['work_img', 'video']\r\n\t\t\t\timgArrs.map(item => {\r\n\t\t\t\t\tdata[item] = data[item] && data[item].length > 0 ? [{\r\n\t\t\t\t\t\tpath: data[item]\r\n\t\t\t\t\t}] : []\r\n\t\t\t\t})\r\n\t\t\t\tthis.cityIndex = this.cityList.findIndex(item => {\r\n\t\t\t\t\treturn item.id == data.city_id\r\n\t\t\t\t})\r\n\t\t\t\tthis.storeIndex = this.storeList.findIndex(item => {\r\n\t\t\t\t\treturn item.id == data.store_id\r\n\t\t\t\t})\r\n\t\t\t\tdata.birthday = data.birthday ? this.$util.formatTime(data.birthday * 1000, 'YY-M-D') : ''\r\n\t\t\t\tfor (let key in this.form) {\r\n\t\t\t\t\tthis.form[key] = data[key]\r\n\t\t\t\t}\r\n\t\t\t\tthis.have_user_id = data.id && data.user_id\r\n\t\t\t\tthis.fdd_agreement = data.fdd_agreement\r\n\t\t\t\tthis.isLoad = true\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\tasync toResetUtilLoca() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlat: locaLat = 0\r\n\t\t\t\t} = this.location\r\n\t\t\t\tif (!locaLat) {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlng = 0,\r\n\t\t\t\t\t\t\tlat = 0\r\n\t\t\t\t\t} = await this.$util.getUtilLocation()\r\n\t\t\t\t\tif (!lat && !lng) return\r\n\t\t\t\t\tawait this.getCityList()\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tcity_id\r\n\t\t\t\t\t} = this.form\r\n\t\t\t\t\tthis.cityIndex = this.cityList.findIndex(item => {\r\n\t\t\t\t\t\treturn item.id == city_id\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync toOpenLocation() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\ttype: 'userLocation',\r\n\t\t\t\t\tcheckApp: true\r\n\t\t\t\t})\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.initRefresh()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync getStoreList() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstore = false\r\n\t\t\t\t} = this.configInfo.plugAuth\r\n\t\t\t\tif (store) {\r\n\t\t\t\t\tlet data = await this.$api.agent.storeSelect()\r\n\t\t\t\t\tdata.unshift({\r\n\t\t\t\t\t\tid: 0,\r\n\t\t\t\t\t\ttitle: '不挂靠门店'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.storeList = data\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getCityList() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlocation\r\n\t\t\t\t} = this\r\n\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstatus: coach_status,\r\n\t\t\t\t\tcoach_position\r\n\t\t\t\t} = this.userCoachStatus\r\n\r\n\t\t\t\tif (!location.lat || (location.lat && location.address == '暂未获取到位置信息')) {\r\n\t\t\t\t\tif (coach_status == 2 && coach_position) {\r\n\t\t\t\t\t\tlet {\r\n\t\t\t\t\t\t\tlat: change_lat = 0,\r\n\t\t\t\t\t\t\tlng: change_lng = 0,\r\n\t\t\t\t\t\t\tunix = 0\r\n\t\t\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\t\t\tlet noloca = change_lat && change_lng && (unix && (cur_unix - unix < 3)) ? false : true\r\n\t\t\t\t\t\tif (!noloca) {\r\n\t\t\t\t\t\t\tlet loca = Object.assign({}, this.location, {\r\n\t\t\t\t\t\t\t\tlat: change_lat,\r\n\t\t\t\t\t\t\t\tlng: change_lng,\r\n\t\t\t\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\t\t\t\tval: loca\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\tkey: 'noChangeLoca',\r\n\t\t\t\t\t\t\tval: {\r\n\t\t\t\t\t\t\t\tnoloca\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif (this.noChangeLoca.noloca) return\r\n\t\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.getUtilLocation()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.updateMapItem({\r\n\t\t\t\t\t\tkey: 'pageActive',\r\n\t\t\t\t\t\tval: false\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t},\r\n\t\t\tasync getUtilLocation() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (this.$jweixin.isWechat()) {\r\n\t\t\t\t\tlet wxReady = await this.$jweixin.wxReady2();\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlatitude: lat = 0,\r\n\t\t\t\t\t\tlongitude: lng = 0\r\n\t\t\t\t\t} = wxReady ? await this.$jweixin.getWxLocation() : {\r\n\t\t\t\t\t\tlatitude: 0,\r\n\t\t\t\t\t\tlongitude: 0\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet val = Object.assign({}, this.location, {\r\n\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\t\tval\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlat = 0,\r\n\t\t\t\t\t\tlng = 0\r\n\t\t\t\t} = await this.$util.getLocation()\r\n\t\t\t\tlet val = Object.assign({}, this.location, {\r\n\t\t\t\t\tlat,\r\n\t\t\t\t\tlng,\r\n\t\t\t\t\tis_util_loca: 1\r\n\t\t\t\t})\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'location',\r\n\t\t\t\t\tval\r\n\t\t\t\t})\r\n\t\t\t\tthis.initUtilLocaData()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync initUtilLocaData() {\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'noChangeLoca',\r\n\t\t\t\t\tval: {\r\n\t\t\t\t\t\tnoloca: false\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlng = 0,\r\n\t\t\t\t\t\tlat = 0\r\n\t\t\t\t} = this.location\r\n\t\t\t\tlet city = await this.$api.base.getCity({\r\n\t\t\t\t\tlng,\r\n\t\t\t\t\tlat\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\tthis.cityList = city\r\n\t\t\t\tthis.cityIndex = city.length > 0 ? 0 : -1\r\n\t\t\t\tthis.form.city_id = city.length > 0 ? city[0].id : ''\r\n\t\t\t\tif (lat && lng) {\r\n\t\t\t\t\tthis.$util.getMapInfo()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync toFddSign() {\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'fddExtsign',\r\n\t\t\t\t\tval: ''\r\n\t\t\t\t})\r\n\t\t\t\tlet {\r\n\t\t\t\t\tviewpdf_url = ''\r\n\t\t\t\t} = this.fdd_agreement\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'fddExtsign',\r\n\t\t\t\t\tval: viewpdf_url\r\n\t\t\t\t})\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.location.href = viewpdf_url\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl: `/user/pages/common/web?url=fddExtsign`\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tpickerChange(e, key) {\r\n\t\t\t\tlet ind = e.target.value\r\n\t\t\t\tif (key === 'birthday') {\r\n\t\t\t\t\tlet unix = this.$util.DateToUnix(ind)\r\n\t\t\t\t\tif (unix > new Date(Math.ceil(new Date().getTime())) / 1000) {\r\n\t\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\t\ttitle: `不能选择未来时间哦`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.form[key] = ind\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis[`${key}Index`] = ind\r\n\t\t\t\tthis.form[`${key}_id`] = this[`${key}List`][ind].id\r\n\t\t\t},\r\n\t\t\timgUpload(e) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\timagelist,\r\n\t\t\t\t\timgtype\r\n\t\t\t\t} = e;\r\n\t\t\t\tthis.form[imgtype] = imagelist;\r\n\t\t\t},\r\n\t\t\ttoSetItem(index, key, key1) {\r\n\t\t\t\tlet edit = this.toCheckEdit()\r\n\t\t\t\tif (!edit) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid\r\n\t\t\t\t} = this[key1][index]\r\n\t\t\t\tthis.form[key] = id\r\n\t\t\t},\r\n\t\t\ttoShowTime(key) {\r\n\t\t\t\tlet edit = this.toCheckEdit()\r\n\t\t\t\tif (!edit) return\r\n\t\t\t\tthis.showKey = key\r\n\t\t\t\tthis.showTime = true\r\n\t\t\t},\r\n\t\t\ttoCheckEdit() {\r\n\t\t\t\tlet edit = true\r\n\t\t\t\tlet {\r\n\t\t\t\t\tedit_base_info\r\n\t\t\t\t} = this\r\n\t\t\t\tif (!edit_base_info) {\r\n\t\t\t\t\tedit = false\r\n\t\t\t\t}\r\n\t\t\t\treturn edit\r\n\t\t\t},\r\n\t\t\tonConfirm(val) {\r\n\t\t\t\tthis.form[this.showKey] = val.result;\r\n\t\t\t},\r\n\t\t\t// 选择地区\r\n\t\t\tasync toChooseLocation() {\r\n\t\t\t\tlet edit = this.toCheckEdit()\r\n\t\t\t\tif (!edit) return\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'useChooseLocation',\r\n\t\t\t\t\tval: true\r\n\t\t\t\t})\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (this.$jweixin.isWechat()) {\r\n\t\t\t\t\tlet wxReady = await this.$jweixin.wxReady2();\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlatitude,\r\n\t\t\t\t\t\tlongitude\r\n\t\t\t\t\t} = wxReady ? await this.$jweixin.getWxLocation() : {\r\n\t\t\t\t\t\tlatitude: 0,\r\n\t\t\t\t\t\tlongitude: 0\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.initChooseLoca({\r\n\t\t\t\t\t\tlatitude,\r\n\t\t\t\t\t\tlongitude\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tif (!this.location.lat) {\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'useChooseLocation',\r\n\t\t\t\t\t\tval: false\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.toOpenLocation()\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef APP-PLUS  \r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tlat: latitude,\r\n\t\t\t\t\t\tlng: longitude\r\n\t\t\t\t\t} = await this.$util.getLocation()\r\n\t\t\t\t\tthis.initChooseLoca({\r\n\t\t\t\t\t\tlatitude,\r\n\t\t\t\t\t\tlongitude\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif \r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\t\ttype: 'userLocation'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.initChooseLoca()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync initChooseLoca(param = {}) {\r\n\t\t\t\tlet [err, loca_data] = await uni.chooseLocation(param)\r\n\t\t\t\tlet {\r\n\t\t\t\t\tname = '',\r\n\t\t\t\t\t\taddress = '',\r\n\t\t\t\t\t\tlatitude: lat = '',\r\n\t\t\t\t\t\tlongitude: lng = ''\r\n\t\t\t\t} = err ? {\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\taddress: '',\r\n\t\t\t\t\tlatitude: '',\r\n\t\t\t\t\tlongitude: ''\r\n\t\t\t\t} : loca_data\r\n\t\t\t\taddress = address || name\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'useChooseLocation',\r\n\t\t\t\t\tval: false\r\n\t\t\t\t})\r\n\t\t\t\tif (!address) return\r\n\t\t\t\tthis.form.lat = lat\r\n\t\t\t\tthis.form.lng = lng\r\n\t\t\t\tthis.form.address = address\r\n\t\t\t},\r\n\t\t\ttoChooseUser() {\r\n\t\t\t\tlet edit = this.toCheckEdit()\r\n\t\t\t\tif (!edit) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\thave_user_id\r\n\t\t\t\t} = this\r\n\t\t\t\tif (have_user_id) return\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl: `/agent/pages/technician/user`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//表单验证\r\n\t\t\tvalidate(param) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tfree_fare_bear,\r\n\t\t\t\t\tfree_fare_distance\r\n\t\t\t\t} = this.configInfo\r\n\t\t\t\tlet validate = new this.$util.Validate();\r\n\t\t\t\tthis.rule.map(item => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tname,\r\n\t\t\t\t\t} = item\r\n\t\t\t\t\tif (name == 'free_fare_distance') {\r\n\t\t\t\t\t\tif (param[name]) {\r\n\t\t\t\t\t\t\tif (free_fare_bear == 1) {\r\n\t\t\t\t\t\t\t\titem.maxNum = free_fare_distance\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvalidate.add(param[name], item)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvalidate.add(param[name], item)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tlet message = validate.start();\r\n\t\t\t\treturn message;\r\n\t\t\t},\r\n\t\t\tasync submit() {\r\n\t\t\t\tlet param = this.$util.deepCopy(this.form)\r\n\t\t\t\tlet arr = ['id_card', 'id_card_fan', 'id_card_people', 'work_img', 'video']\r\n\t\t\t\tarr.map(item => {\r\n\t\t\t\t\tparam[item] = param[item].length > 0 ? param[item][0].path : ''\r\n\t\t\t\t})\r\n\t\t\t\tparam.license = param.license.map(item => {\r\n\t\t\t\t\treturn item.path\r\n\t\t\t\t})\r\n\t\t\t\tparam.self_img = param.self_img.map(item => {\r\n\t\t\t\t\treturn item.path\r\n\t\t\t\t})\r\n\t\t\t\tlet msg = this.validate(param);\r\n\t\t\t\tif (msg) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: msg\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_work: work = 0,\r\n\t\t\t\t\tstart_time: start,\r\n\t\t\t\t\tend_time: end\r\n\t\t\t\t} = param\r\n\t\t\t\tif (work && (!start || !end)) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: !start ? '请选择开始时间' : '请选择结束时间'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tparam.free_fare_distance = param.free_fare_distance || 0\r\n\t\t\t\tparam.birthday = this.$util.DateToUnix(param.birthday)\r\n\t\t\t\tparam.id_card = [param.id_card, param.id_card_fan, param.id_card_people]\r\n\t\t\t\tdelete param.id_card_fan\r\n\t\t\t\tdelete param.id_card_people\r\n\t\t\t\tdelete param.nickName\r\n\t\t\t\tdelete param.store_name\r\n\t\t\t\tif (param.id) {\r\n\t\t\t\t\tdelete param.admin_id\r\n\t\t\t\t}\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\ttry {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tis_edit = 0\r\n\t\t\t\t\t} = this.options\r\n\t\t\t\t\tlet methodModel = param.id ? 'coachUpdateAdmin' : 'coachApply'\r\n\t\t\t\t\tawait this.$api.agent[methodModel](param)\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `提交成功`\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\turl: 1,\r\n\t\t\t\t\t\t\topenType: `navigateBack`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\thandleStartEndTime(val, toDay) {\r\n\t\t\t\tlet text = ''\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstart_time,\r\n\t\t\t\t\tend_time\r\n\t\t\t\t} = val\r\n\t\t\t\tlet start = `${toDay} ${start_time}`\r\n\t\t\t\tlet end = `${toDay} ${end_time}`\r\n\t\t\t\tif (start_time && end_time && $util.DateToUnix(start) >= $util.DateToUnix(end)) {\r\n\t\t\t\t\ttext = '次日'\r\n\t\t\t\t}\r\n\t\t\t\treturn text\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.apply-pages {\r\n\t\t.apply-form {\r\n\t\t\t.item-text {\r\n\t\t\t\twidth: 250rpx\r\n\t\t\t}\r\n\r\n\t\t\t.item-text.tips {\r\n\t\t\t\twidth: 200rpx\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.item-time {\r\n\t\twidth: 50%;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110369765\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}