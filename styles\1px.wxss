/* 1px方案,改变border的颜色即可 */
.b-1px, .b-1px-t, .b-1px-b, .b-1px-tb, .b-1px-l, .b-1px-r {
  position: relative;
}

.b-1px:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  border: 1px solid #eee;
  color: #eee;
  height: 200%;
  transform-origin: left top;
  transform: scale(0.5);
  box-sizing: border-box;
}

.b-1px-t:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #eee;
  color: #eee;
  transform-origin: 0 0;
  transform: scaley(0.5);
  box-sizing: border-box;
}

.b-1px-b:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #eee;
  color: #eee;
  transform-origin: 0 100%;
  transform: scaley(0.5);
  box-sizing: border-box;
}

.b-1px-tb:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #eee;
  color: #eee;
  transform-origin: 0 0;
  transform: scaley(0.5);
  box-sizing: border-box;
}

.b-1px-tb:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #eee;
  color: #eee;
  transform-origin: 0 100%;
  transform: scaley(0.5);
  box-sizing: border-box;
}

.b-1px-l::before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid #eee;
  color: #eee;
  transform-origin: 0 0;
  transform: scalex(0.5);
  box-sizing: border-box;
}

.b-1px-r::after {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-right: 1px solid #eee;
  color: #eee;
  transform-origin: 100% 0;
  transform: scalex(0.5);
  box-sizing: border-box;
}