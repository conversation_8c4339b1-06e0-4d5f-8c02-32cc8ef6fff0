{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/refund/list.vue?4326", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/refund/list.vue?40d2", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/refund/list.vue?b1e3", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/refund/list.vue?afbc", "uni-app:///agent/pages/refund/list.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "options", "activeIndex", "tabList", "title", "id", "statusType", "param", "page", "list", "loading", "popupInfo", "lockTap", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "onLoad", "agent", "bell", "tab", "uni", "onPullDownRefresh", "onReachBottom", "methods", "initIndex", "refresh", "bg", "initRefresh", "toSearch", "getList", "oldList", "<PERSON><PERSON><PERSON>", "newList", "handerTabChange", "toConfirm", "apply_price", "index", "type", "price", "text", "confirmChangeOrder", "reg", "methodModel", "key", "val", "setTimeout", "goDetail", "url", "toTel", "mobile", "openType"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;;;AAGnD;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAuxB,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuG3yB;AAGA;AAAA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACAC;MACA;MACAC;QACAT;MACA;MACAU;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA,qBAIAjB,QAHAkB;MAAAA;MAAA,gBAGAlB,QAFAmB;MAAAA;MAAA,eAEAnB,QADAoB;MAAAA;IAEApB;IACA;IACA;IACAqB;MACAlB;IACA;IACA;EACA;EACAmB;IAEAD;IAEA;IACAA;EACA;EACAE;IACA;IACA;IACA;IACA;EACA;EACAC,yCACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAAA,OASA;cAAA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC,UAIA,OAJAvB,MACAF,QAGA,OAHAA,OACAJ,UAEA,OAFAA,SACAD,cACA,OADAA;gBAEAK;gBAAA,iBAIA,qDAFAa,uEACAD;gBAEAZ;gBACA0B;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAEA;kBACA;gBACA;kBACAA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,wBAIA,yBAFA/B,+BACAgC;gBAEA;kBACAC;kBACAjC;kBACAkC;kBACAF;kBACAG;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,mBAMA,kBAJAH,8BACAD,gCACAE,gCACAH;gBAEA9B;gBACAoC;gBAAA,MACAJ;kBAAA;kBAAA;gBAAA;gBACA;kBACAnC;gBACA;gBAAA;cAAA;gBAIAF,cACA,OADAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAEAiB,QACA,eADAA;gBAEAc;gBACAW;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;kBACAxC;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACAyC;kBACAC;gBACA;gBACA;gBAAA,MACA5C;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA6C;kBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAC;MACA,IACA3C,KACA,sBADAA;MAEA,IACAc,QACA,aADAA;MAEA;MACA;QACA8B;MACA;IACA;IACA;IACAC;MACA,IACAD,MACA,gBADAE;MAEA;QACAF;QACAG;MACA;IACA;EAAA;AAEA;AAAA,2B", "file": "agent/pages/refund/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/refund/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=2213df62&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/refund/list.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=2213df62&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.$t(\"action.attendantName\")\n    var g0 = [1, 5].includes(item.status)\n    return {\n      $orig: $orig,\n      m0: m0,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.loading\n    ? _vm.list.current_page >= _vm.list.last_page && _vm.list.data.length > 0\n    : null\n  var g2 =\n    !_vm.loading && _vm.list.data.length <= 0 && _vm.list.current_page == 1\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.change_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order-pages\">\r\n\t\t<fixed>\r\n\t\t\t<view class=\"fill-base pt-lg pl-md pr-md pb-md\">\r\n\t\t\t\t<search @input=\"toSearch\" type=\"input\" :padding=\"0\" :radius=\"30\" height=\"70rpx\"\r\n\t\t\t\t\tplaceholder=\"请输入系统订单号查询\">\r\n\t\t\t\t</search>\r\n\t\t\t</view>\r\n\t\t\t<tab @change=\"handerTabChange\" :list=\"tabList\" :activeIndex=\"activeIndex*1\" :activeColor=\"primaryColor\"\r\n\t\t\t\theight=\"100rpx\"></tab>\r\n\t\t\t<view class=\"b-1px-b\"></view>\r\n\t\t</fixed>\r\n\r\n\t\t<view @tap.stop=\"goDetail(index, 'detail')\" class=\"item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16\"\r\n\t\t\tv-for=\"(item,index) in list.data\" :key=\"index\">\r\n\t\t\t<view class=\"flex-between pb-lg b-1px-b\">\r\n\t\t\t\t<view class=\"f-paragraph c-title max-450 ellipsis\">订单号：{{item.order_code}}</view>\r\n\t\t\t\t<view class=\"f-caption text-bold\"\r\n\t\t\t\t\t:style=\"{color:item.status==1 || item.status == 4 ? subColor: item.status == 2 ? '#11C95E' : '#333'}\">\r\n\t\t\t\t\t{{statusType[item.status]}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mb-lg\" :class=\"[{'mt-lg':aindex==0}]\" v-for=\"(aitem,aindex) in item.order_goods\"\r\n\t\t\t\t:key=\"aindex\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"avatar lg radius-16\">\r\n\t\t\t\t\t<view class=\"h5-image avatar lg radius-16\"\r\n\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${aitem.goods_cover}')`}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<image mode=\"aspectFill\" class=\"avatar lg radius-16\" :src=\"aitem.goods_cover\"></image>\r\n\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t<view class=\"f-mini-title c-title text-bold max-380 ellipsis\">\r\n\t\t\t\t\t\t\t{{aitem.goods_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"c-paragraph\">x{{aitem.num}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-md f-caption c-caption max-450 ellipsis\">\r\n\t\t\t\t\t\t服务{{$t('action.attendantName')}}：{{item.coach_info?item.coach_info.coach_name:'-'}}</view>\r\n\t\t\t\t\t<view class=\"f-caption c-caption\" style=\"margin-top: 5rpx;\">服务时间：{{item.start_time}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between pt-lg b-1px-t\">\r\n\t\t\t\t<view class=\"flex-y-center f-desc c-title\">总计：\r\n\t\t\t\t\t<view class=\"f-paragraph text-bold\">¥{{item.apply_price}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-warp\">\r\n\t\t\t\t\t<!-- 退款申请中 -->\r\n\t\t\t\t\t<block v-if=\"[1,5].includes(item.status)\">\r\n\t\t\t\t\t\t<button @tap.stop=\"toConfirm(index,3)\" class=\"clear-btn order\"\r\n\t\t\t\t\t\t\tstyle=\"margin-left: 0;\">拒绝退款</button>\r\n\t\t\t\t\t\t<button @tap.stop=\"goDetail(index, 'refund')\" class=\"clear-btn order\"\r\n\t\t\t\t\t\t\t:style=\"{color:'#fff',background:primaryColor,border:`1rpx solid ${primaryColor}`}\">同意退款</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- 同意/拒绝退款 -->\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<button class=\"clear-btn order\"\r\n\t\t\t\t\t\t\t:style=\"{color:'#fff',background:primaryColor,border:`1rpx solid ${primaryColor}`}\">查看详情</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<load-more :noMore=\"list.current_page>=list.last_page&&list.data.length>0\" :loading=\"loading\" v-if=\"loading\">\r\n\t\t</load-more>\r\n\t\t<abnor v-if=\"!loading&&list.data.length<=0&&list.current_page==1\"></abnor>\r\n\r\n\t\t<view class=\"space-footer\"></view>\r\n\r\n\t\t<uni-popup ref=\"change_item\" type=\"center\" :custom=\"true\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">温馨提示</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t你确认要操作{{statusType[popupInfo.type]}}吗?\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"mt-lg\" v-if=\"popupInfo.type == 2\">\r\n\t\t\t\t\t<input v-model=\"popupInfo.price\" type=\"digit\"\r\n\t\t\t\t\t\tclass=\"input flex-y-center pl-lg pr-lg f-sm-title c-title radius-16\"\r\n\t\t\t\t\t\tplaceholder-class=\"c-placeholder\" placeholder=\"请输入退款金额\" />\r\n\t\t\t\t\t<view class=\"f-desc c-caption mt-md\">\r\n\t\t\t\t\t\t<view class=\"flex-y-center\">实际可退款金额<view class=\"ml-sm c-warning\">¥{{popupInfo.apply_price}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>退款金额不能大于可退款金额</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.change_item.close()\" class=\"item-child\">取消</view>\r\n\t\t\t\t\t<view @tap.stop=\"confirmChangeOrder\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\toptions: {},\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\ttabList: [{\r\n\t\t\t\t\ttitle: '全部',\r\n\t\t\t\t\tid: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '待退款',\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '同意退款',\r\n\t\t\t\t\tid: 2\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '拒绝退款',\r\n\t\t\t\t\tid: 3\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '退款中',\r\n\t\t\t\t\tid: 4\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '退款失败',\r\n\t\t\t\t\tid: 5\r\n\t\t\t\t}],\r\n\t\t\t\tstatusType: {\r\n\t\t\t\t\t'-1': '已取消',\r\n\t\t\t\t\t1: '待退款',\r\n\t\t\t\t\t2: '同意退款',\r\n\t\t\t\t\t3: '拒绝退款',\r\n\t\t\t\t\t4: '退款中',\r\n\t\t\t\t\t5: '退款失败'\r\n\t\t\t\t},\r\n\t\t\t\tparam: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t},\r\n\t\t\t\tlist: {\r\n\t\t\t\t\tdata: []\r\n\t\t\t\t},\r\n\t\t\t\tloading: true,\r\n\t\t\t\tpopupInfo: {},\r\n\t\t\t\tlockTap: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tagent = 0,\r\n\t\t\t\t\tbell = 0,\r\n\t\t\t\t\ttab = 0\r\n\t\t\t} = options\r\n\t\t\toptions.agent = agent * 1\r\n\t\t\tthis.options = options\r\n\t\t\tthis.activeIndex = tab\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: bell == 1 ? '加钟退款' : '服务退款'\r\n\t\t\t})\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.showNavigationBarLoading()\r\n\t\t\t// #endif\r\n\t\t\tthis.initRefresh();\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.list.current_page >= this.list.last_page || this.loading) return;\r\n\t\t\tthis.param.page = this.param.page + 1;\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapMutations(['updateTechnicianItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tawait this.getList()\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\ttoSearch(val) {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.param.order_code = val\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tasync getList() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlist: oldList,\r\n\t\t\t\t\tparam,\r\n\t\t\t\t\ttabList,\r\n\t\t\t\t\tactiveIndex\r\n\t\t\t\t} = this\r\n\t\t\t\tparam.status = tabList[activeIndex].id\r\n\t\t\t\tlet {\r\n\t\t\t\t\tbell = 0,\r\n\t\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tparam.is_add = bell\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tlet newList = await this.$api[methodKey].refundOrderList(param);\r\n\r\n\t\t\t\tif (this.param.page == 1) {\r\n\t\t\t\t\tthis.list = newList\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnewList.data = oldList.data.concat(newList.data)\r\n\t\t\t\t\tthis.list = newList\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = false\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t},\r\n\t\t\thanderTabChange(index) {\r\n\t\t\t\tthis.activeIndex = index;\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tthis.param.page = 1;\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\t// type: 2同意退款；3拒绝退款\r\n\t\t\tasync toConfirm(index, type) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tapply_price\r\n\t\t\t\t} = this.list.data[index] \r\n\t\t\t\tthis.popupInfo = {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tid,\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\tapply_price,\r\n\t\t\t\t\tprice: type == 2 ? apply_price : '',\r\n\t\t\t\t\ttext: ''\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.change_item.open()\r\n\t\t\t},\r\n\t\t\tasync confirmChangeOrder() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tprice,\r\n\t\t\t\t\tapply_price\r\n\t\t\t\t} = this.popupInfo\r\n\t\t\t\tlet param = this.$util.pick(this.popupInfo, ['id', 'price', 'text'])\r\n\t\t\t\tlet reg = /^(([0-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/\r\n\t\t\t\tif (type == 2 && (!price || !reg.test(price) || price * 1 > apply_price * 1)) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: !price ? '请输入退款金额' : !reg.test(price) ? '请输入正确的退款金额，最多保留2位小数' : '退款金额不能大于可退款金额'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tactiveIndex\r\n\t\t\t\t} = this\r\n\t\t\t\tif (this.lockTap) return;\r\n\t\t\t\tthis.lockTap = true;\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tlet methodModel = type == 2 ? 'passRefund' : 'noPassRefund'\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.$api[methodKey][methodModel](param)\r\n\t\t\t\t\tthis.$refs.change_item.close()\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: '操作成功'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (activeIndex == 0) {\r\n\t\t\t\t\t\tthis.list.data[index].status = type\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.list.data.splice(index, 1)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\t\tval: true\r\n\t\t\t\t\t})\n\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\tif (activeIndex == 0) return\r\n\t\t\t\t\tawait this.getList(true)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 订单详情\r\n\t\t\tgoDetail(index, page) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet url = `/agent/pages/refund/${page}?id=${id}&agent=${agent}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 平台\r\n\t\t\ttoTel() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tmobile: url\r\n\t\t\t\t} = this.configInfo\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl,\r\n\t\t\t\t\topenType: `call`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n</style>"], "sourceRoot": ""}