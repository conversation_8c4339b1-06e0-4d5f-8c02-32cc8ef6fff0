{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?addd", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?cbc9", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?43b9", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?a50b", "uni-app:///components/jyf-Parser/index.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?4ef6", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/index.vue?84db"], "names": ["hash", "newSrc", "name", "data", "showAnimation", "controls", "nodes", "components", "trees", "props", "type", "default", "watch", "html", "mounted", "methods", "<PERSON><PERSON><PERSON><PERSON>", "key", "res", "cache", "_imgNum", "_videoNum", "_audioNum", "_domain", "_protocol", "_STACK", "CssHandler", "<PERSON><PERSON><PERSON>", "node", "config", "DFS", "console", "source", "errMsg", "observered", "component", "item", "top", "bottom", "context", "wx", "uni", "title", "getContext", "getText", "text", "child", "navigateTo", "query", "scrollTop", "success", "fail", "<PERSON><PERSON>", "getVideoContext"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAwxB,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+B5yB;AACA;AACA;AACA;AACA;AACA;EACA;IACAA;EAAA;EACA;AACA;;AAEA;AACA,oBACA;AACA;;AAEA;AACA;EACA;EACA;EACA;IACAC;IACA;EACA;EACAA;EACA;AACA;AAAA,gBAEA;EACAC;EACAC;IACA;MASAC;MACAC;MAEAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC;IACA;IACA;MACAD;MACAC;IACA;IAEA;MACAD;MACAC;IACA;IAEA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;IACA;IAEA;MACAD;MACAC;IACA;IAEA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;QACA;MACA;IACA;IACA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;IACA;IACA;MACAD;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;QAKA;QACA;UACA,gEACA;QACA;MAEA;IACA;IACA;EACA;EAMAC;IA4MAC;MAAA;MACA,gDACA;QACAC;UACA;QACA;QACA;MACA;MACA,4BACA;MACA;QACA,0BACA;MACA;QACA;QACA;QACA;UACA;UACA,iBACAC,uBACA;YACAA;YACAC;UACA;QACA;QACA;QACA;MACA;QACA,sCACA;QACA;QACA;UACA;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACAC;UACA;YAAA,2CACArB;cAAA;YAAA;cAAA;gBAAA;gBACA;gBACAsB;gBACA;kBACA,gEACA;gBACA;gBACAC;gBACA,wDACA;gBACA;kBACAF;kBACAG;kBACAH;gBACA;cACA;YAAA;cAAA;YAAA;cAAA;YAAA;UACA;UACAG;UACA;QACA;MACA;QACA;QACAC;MACA;QACA;UACAC;UACAC;QACA;MACA;MAIA;MACA;QACA;QACA;QACA;UAAA,4CACA1B;YAAA;UAAA;YAAA;cAAA;cACA;gBACA2B;gBAAA,4CACAC;kBAAA;gBAAA;kBAAA;oBAAAC;oBACA;oBACA;sBACA;wBAEA,iDACA,kDACA;sBAKA;sBAEA;wBACAF;wBACA;0BACA;0BACAC;0BACAA;4BACAE;4BACAC;0BACA;4BACAH;4BACAA;4BACAA;0BACA;wBACA,OACAA;sBACA;oBAEA,OAEA;sBACAI;sBACAA;sBACA;oBACA,OAGA,iDACAC;;oBAEA;oBAAA,KACA;sBACA,oFACAC;wBACAC;sBACA;oBACA;kBAUA;gBAAA;kBAAA;gBAAA;kBAAA;gBAAA;cACA;cACA,gCACAC;YAAA;YAjEA;cAAA,IAEAT;cAAA,IACAE;cAAA,IAkCAG;cAAA;YA6BA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;QAIAI;QACAF;UACA;QACA;MASA;IACA;IACAG;MAAA;MACA;MACA;QACA,uDACA;UACA,wGACA,0FACAC;UAAA,4CACAjB;YAAA;UAAA;YAAA,uDACA;cAAA,IADAkB;cACAhB;YAAA;UAAA;YAAA;UAAA;YAAA;UAAA;UACA,qHACAF,sDACAiB,kBACA;QACA;MACA;MACA,sHACA;MACA;MAAA,4CACAvC;QAAA;MAAA;QAAA,uDACA;UAAA,IADAsB;UACAE;QAAA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;IACAiB;MAAA;MACA;QACA;QACAC;QACAA;QACAA;UACA,qBACA;YACAf;UACA;UACAQ;YACAQ;YACAC;YACAC;UACA;QACA;MACA;MACA,sCACA;QAEAC;MASA;IACA;IAEAC;MACA,wCACA;QAAA,4CACA;UAAA;QAAA;UAAA;YAAA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC5lBA;AAAA;AAAA;AAAA;AAAonC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACAxoC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/jyf-Parser/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=07fa1153&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/jyf-Parser/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=07fa1153&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.html[0].name && !_vm.html[0].type && !_vm.nodes.length\n  var g1 = _vm.nodes.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<!--\r\n parser 主模块组件\r\n github地址：https://github.com/jin-yufeng/Parser\r\n 文档地址：https://jin-yufeng.github.io/Parser\r\n 插件市场：https://ext.dcloud.net.cn/plugin?id=805\r\n author：JinYufeng\r\n-->\r\n<template>\r\n\t<view>\r\n\t\t<!--#ifdef H5-->\r\n\t\t<slot v-if=\"!html && !nodes.length\"></slot>\r\n\t\t<div :id=\"'rtf' + uid\" :style=\"(selectable ? 'user-select:text;-webkit-user-select:text;' : '') + (showWithAnimation ? ('opacity:0;' + showAnimation) : '')\"></div>\r\n\t\t<!--#endif-->\r\n\t\t<!--#ifndef H5-->\r\n\t\t<slot v-if=\"!html[0].name && !html[0].type && !nodes.length\"></slot>\r\n\t\t<!--#endif-->\r\n\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t<view class=\"_contain\" :style=\"(selectable ? 'user-select:text;-webkit-user-select:text;' : '') + (showWithAnimation ? ('opacity:0;' + showAnimation) : '')\">\r\n\t\t\t<trees :nodes=\"nodes.length ? nodes : (html[0].name || html[0].type ? html : [])\" :imgMode=\"imgMode\" />\r\n\t\t</view>\r\n\t\t<!--#endif-->\r\n\t\t<!--#ifndef MP-ALIPAY || H5-->\r\n\t\t<trees class=\"_contain\" :style=\"'display:block;' + (selectable ? 'user-select:text;-webkit-user-select:text;' : '') + (showWithAnimation ? ('opacity:0;' + showAnimation) : '')\"\r\n\t\t :nodes=\"nodes.length ? nodes : (html[0].name || html[0].type ? html : [])\" :imgMode=\"imgMode\" :loadVideo=\"loadVideo\" />\r\n\t\t<!--#endif-->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifndef H5\r\n\timport trees from \"./trees\"\r\n\tvar document; // document 补丁包，详见 https://jin-yufeng.github.io/Parser/#/instructions?id=document\r\n\tconst parseHtmlSync = require('./libs/MpHtmlParser.js').parseHtmlSync;\r\n\tconst cache = getApp().parserCache = {};\r\n\tconst CssHandler = require(\"./libs/CssHandler.js\");\r\n\t// 散列函数（计算 cache 的 key）\r\n\tconst Hash = (str) => {\r\n\t\tfor (var i = 0, hash = 5381, len = str.length; i < len; i++)\r\n\t\t\thash += (hash << 5) + str.charCodeAt(i);\r\n\t\treturn hash;\r\n\t};\r\n\t// #endif\r\n\t// 动画\r\n\tconst showAnimation =\r\n\t\t\"transition:400ms ease 0ms;transition-property:transform,opacity;transform-origin:50% 50% 0;-webkit-transition:400ms ease 0ms;-webkit-transform:;-webkit-transition-property:transform,opacity;-webkit-transform-origin:50% 50% 0;opacity: 1\"\r\n\tconst config = require('./libs/config.js');\r\n\t// #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU || MP-TOUTIAO\r\n\t// 图片链接去重\r\n\tconst Deduplication = (src) => {\r\n\t\tif (src.indexOf(\"http\") != 0) return src;\r\n\t\tvar newSrc = '';\r\n\t\tfor (var i = 0; i < src.length; i++) {\r\n\t\t\tnewSrc += (Math.random() >= 0.5 ? src[i].toUpperCase() : src[i].toLowerCase());\r\n\t\t\tif (src[i] == '/' && src[i - 1] != '/' && src[i + 1] != '/') break;\r\n\t\t}\r\n\t\tnewSrc += src.substring(i + 1);\r\n\t\treturn newSrc;\r\n\t}\r\n\t// #endif\r\n\texport default {\r\n\t\tname: 'parser',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tloadVideo: false,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tuid: this._uid,\r\n\t\t\t\tshowAnimation: '',\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tshowAnimation: {},\r\n\t\t\t\tcontrols: {},\r\n\t\t\t\t// #endif\r\n\t\t\t\tnodes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifndef H5\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\t// #endif\r\n\t\tprops: {\r\n\t\t\t'html': {\r\n\t\t\t\ttype: null,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t'autocopy': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t'autopause': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t'autopreview': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t'autosetTitle': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t'domain': {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t'imgMode': {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t// #ifdef MP-WEIXIN || MP-QQ || H5 || APP-PLUS\r\n\t\t\t'lazyLoad': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t'selectable': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t'tagStyle': {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'showWithAnimation': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t'useAnchor': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t'useCache': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\thtml(html) {\r\n\t\t\t\tthis.setContent(html, undefined, true);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.imgList = [];\r\n\t\t\tthis.imgList.each = function(f) {\r\n\t\t\t\tfor (var i = 0; i < this.length; i++) {\r\n\t\t\t\t\t// #ifdef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\tthis[i] = f(this[i], i, this) || this[i];\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\tvar newSrc = f(this[i], i, this);\r\n\t\t\t\t\tif (newSrc) {\r\n\t\t\t\t\t\tif (this.includes(newSrc)) this[i] = Deduplication(newSrc);\r\n\t\t\t\t\t\telse this[i] = newSrc;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.setContent(this.html, undefined, true);\r\n\t\t},\r\n\t\t// #ifdef H5\r\n\t\tbeforeDestroy() {\r\n\t\t\tif (this._observer) this._observer.disconnect();\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t// #ifdef H5\r\n\t\t\tsetContent(html, options, observed) {\r\n\t\t\t\tif (typeof options == \"object\")\r\n\t\t\t\t\tfor (var key in options) {\r\n\t\t\t\t\t\tkey = key.replace(/-(\\w)/g, function() {\r\n\t\t\t\t\t\t\treturn arguments[1].toUpperCase();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis[key] = options[key];\r\n\t\t\t\t\t}\r\n\t\t\t\thtml = html || '';\r\n\t\t\t\tif (!html) {\r\n\t\t\t\t\tif (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof html != 'string') html = this.Dom2Str(html.nodes || html);\r\n\t\t\t\t// 处理 rpx\r\n\t\t\t\tif (/[0-9.]*?rpx/.test(html)) {\r\n\t\t\t\t\tconst rpx = uni.getSystemInfoSync().screenWidth / 750;\r\n\t\t\t\t\thtml = html.replace(/([0-9.]*?)rpx/g, function() {\r\n\t\t\t\t\t\treturn parseFloat(arguments[1]) * rpx + \"px\";\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// 处理 tag-style 和 userAgentStyles\r\n\t\t\t\tvar style = \"<style>\";\r\n\t\t\t\tfor (var item in config.userAgentStyles)\r\n\t\t\t\t\tstyle += (item + '{' + config.userAgentStyles[item] + '}');\r\n\t\t\t\tfor (var item in this.tagStyle)\r\n\t\t\t\t\tstyle += (item + '{' + this.tagStyle[item] + '}');\r\n\t\t\t\tstyle += \"</style>\";\r\n\t\t\t\thtml = style + html;\r\n\t\t\t\tif (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\tthis.rtf = document.createElement('div');\r\n\t\t\t\tthis.rtf.innerHTML = html;\r\n\t\t\t\tfor (var style of this.rtf.getElementsByTagName(\"style\")) {\r\n\t\t\t\t\tstyle.innerHTML = style.innerHTML.replace(/\\s*body/g, \"#rtf\" + this._uid);\r\n\t\t\t\t\tstyle.setAttribute(\"scoped\", \"true\");\r\n\t\t\t\t}\r\n\t\t\t\t// 懒加载\r\n\t\t\t\tif (this.lazyLoad && IntersectionObserver) {\r\n\t\t\t\t\tif (this._observer) this._observer.disconnect();\r\n\t\t\t\t\tthis._observer = new IntersectionObserver(changes => {\r\n\t\t\t\t\t\tfor (var change of changes) {\r\n\t\t\t\t\t\t\tif (change.isIntersecting) {\r\n\t\t\t\t\t\t\t\tchange.target.src = change.target.getAttribute(\"data-src\");\r\n\t\t\t\t\t\t\t\tchange.target.removeAttribute(\"data-src\");\r\n\t\t\t\t\t\t\t\tthis._observer.unobserve(change.target);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\trootMargin: \"1000px 0px 1000px 0px\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tvar component = this;\r\n\t\t\t\t// 获取标题\r\n\t\t\t\tvar title = this.rtf.getElementsByTagName(\"title\");\r\n\t\t\t\tif (title.length && this.autosetTitle)\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: title[0].innerText\r\n\t\t\t\t\t})\r\n\t\t\t\t// 图片处理\r\n\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\tvar imgs = this.rtf.getElementsByTagName(\"img\");\r\n\t\t\t\tfor (var i = 0; i < imgs.length; i++) {\r\n\t\t\t\t\tvar img = imgs[i];\r\n\t\t\t\t\timg.style.maxWidth = \"100%\";\r\n\t\t\t\t\timg.i = i;\r\n\t\t\t\t\tif (this.domain && img.getAttribute(\"src\")[0] == \"/\") {\r\n\t\t\t\t\t\tif (img.getAttribute(\"src\")[1] == \"/\")\r\n\t\t\t\t\t\t\timg.src = (this.domain.includes(\"://\") ? this.domain.split(\"://\")[0] : \"http\") + ':' + img.getAttribute(\"src\");\r\n\t\t\t\t\t\telse img.src = this.domain + img.getAttribute(\"src\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcomponent.imgList.push(img.src);\r\n\t\t\t\t\tif (img.parentElement.nodeName != 'A') {\r\n\t\t\t\t\t\timg.onclick = function() {\r\n\t\t\t\t\t\t\tif (!this.hasAttribute('ignore')) {\r\n\t\t\t\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\t\t\t\tthis.ignore = () => preview = false;\r\n\t\t\t\t\t\t\t\tcomponent.$emit('imgtap', this);\r\n\t\t\t\t\t\t\t\tif (preview && component.autopreview) {\r\n\t\t\t\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\t\t\t\tcurrent: this.i,\r\n\t\t\t\t\t\t\t\t\t\turls: component.imgList\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\timg.onerror = function() {\r\n\t\t\t\t\t\tcomponent.$emit('error', {\r\n\t\t\t\t\t\t\tsource: \"img\",\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (component.lazyLoad && this._observer) {\r\n\t\t\t\t\t\timg.setAttribute(\"data-src\", img.src);\r\n\t\t\t\t\t\timg.removeAttribute(\"src\");\r\n\t\t\t\t\t\tthis._observer.observe(img);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 链接处理\r\n\t\t\t\tvar links = this.rtf.getElementsByTagName(\"a\");\r\n\t\t\t\tfor (var link of links) {\r\n\t\t\t\t\tlink.onclick = function(e) {\r\n\t\t\t\t\t\tvar jump = true,\r\n\t\t\t\t\t\t\thref = this.getAttribute(\"href\");\r\n\t\t\t\t\t\tcomponent.$emit('linkpress', {\r\n\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\tignore: () => jump = false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (jump && href) {\r\n\t\t\t\t\t\t\tif (href[0] == '#') {\r\n\t\t\t\t\t\t\t\tif (component.useAnchor) {\r\n\t\t\t\t\t\t\t\t\tcomponent.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\tid: href.substring(1)\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else if (href.indexOf(\"http\") == 0 || href.indexOf(\"//\") == 0)\r\n\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 视频处理\r\n\t\t\t\tvar videos = this.rtf.getElementsByTagName(\"video\");\r\n\t\t\t\tcomponent.videoContexts = videos;\r\n\t\t\t\tfor (var video of videos) {\r\n\t\t\t\t\tvideo.style.maxWidth = \"100%\";\r\n\t\t\t\t\tvideo.onerror = function() {\r\n\t\t\t\t\t\tcomponent.$emit('error', {\r\n\t\t\t\t\t\t\tsource: \"video\",\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvideo.onplay = function() {\r\n\t\t\t\t\t\tif (component.autopause) {\r\n\t\t\t\t\t\t\tfor (var video of component.videoContexts) {\r\n\t\t\t\t\t\t\t\tif (video != this)\r\n\t\t\t\t\t\t\t\t\tvideo.pause();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 音频处理\r\n\t\t\t\tvar audios = this.rtf.getElementsByTagName(\"audios\");\r\n\t\t\t\tfor (var audio of audios) {\r\n\t\t\t\t\taudio.onerror = function(e) {\r\n\t\t\t\t\t\tcomponent.$emit('error', {\r\n\t\t\t\t\t\t\tsource: \"audio\",\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tdocument.getElementById(\"rtf\" + this._uid).appendChild(this.rtf);\r\n\t\t\t\tif (this.showWithAnimation)\r\n\t\t\t\t\tthis.showAnimation = showAnimation;\r\n\t\t\t\tif (!observed) this.nodes = [0];\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$emit(\"ready\", this.rtf.getBoundingClientRect());\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tDom2Str(nodes) {\r\n\t\t\t\tvar str = \"\";\r\n\t\t\t\tfor (var node of nodes) {\r\n\t\t\t\t\tif (node.type == \"text\")\r\n\t\t\t\t\t\tstr += node.text;\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tstr += ('<' + node.name);\r\n\t\t\t\t\t\tfor (var attr in node.attrs || {})\r\n\t\t\t\t\t\t\tstr += (' ' + attr + '=\"' + node.attrs[attr] + '\"');\r\n\t\t\t\t\t\tif (!node.children || !node.children.length) str += \"/>\";\r\n\t\t\t\t\t\telse str += ('>' + this.Dom2Str(node.children) + \"</\" + node.name + '>');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn str;\r\n\t\t\t},\r\n\t\t\tgetText(whiteSpace = true) {\r\n\t\t\t\tif (!whiteSpace) return this.rtf.innerText.replace(/\\s/g, '');\r\n\t\t\t\treturn this.rtf.innerText;\r\n\t\t\t},\r\n\t\t\tnavigateTo(obj) {\r\n\t\t\t\tif (!obj.id) {\r\n\t\t\t\t\twindow.scrollTo(0, this.rtf.offsetTop);\r\n\t\t\t\t\treturn obj.success ? obj.success({\r\n\t\t\t\t\t\terrMsg: \"pageScrollTo:ok\"\r\n\t\t\t\t\t}) : null;\r\n\t\t\t\t}\r\n\t\t\t\tvar target = document.getElementById(obj.id);\r\n\t\t\t\tif (!target) return obj.fail ? obj.fail({\r\n\t\t\t\t\terrMsg: \"Label Not Found\"\r\n\t\t\t\t}) : null;\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: this.rtf.offsetTop + target.offsetTop,\r\n\t\t\t\t\tsuccess: obj.success,\r\n\t\t\t\t\tfail: obj.fail\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef H5\r\n\t\t\tsetContent(html, options, observed) {\r\n\t\t\t\tif (typeof options == \"object\")\r\n\t\t\t\t\tfor (var key in options) {\r\n\t\t\t\t\t\tkey = key.replace(/-(\\w)/g, function() {\r\n\t\t\t\t\t\t\treturn arguments[1].toUpperCase();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis[key] = options[key];\r\n\t\t\t\t\t}\r\n\t\t\t\tif (this.showWithAnimation)\r\n\t\t\t\t\tthis.showAnimation = showAnimation;\r\n\t\t\t\tif (!html) {\r\n\t\t\t\t\tif (observed) return;\r\n\t\t\t\t\telse this.nodes = [];\r\n\t\t\t\t} else if (typeof html == \"string\") {\r\n\t\t\t\t\tvar res;\r\n\t\t\t\t\t// 缓存读取\r\n\t\t\t\t\tif (this.useCache) {\r\n\t\t\t\t\t\tvar hash = Hash(html);\r\n\t\t\t\t\t\tif (cache[hash])\r\n\t\t\t\t\t\t\tres = cache[hash];\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tres = parseHtmlSync(html, this);\r\n\t\t\t\t\t\t\tcache[hash] = res;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else res = parseHtmlSync(html, this);\r\n\t\t\t\t\tthis.nodes = res;\r\n\t\t\t\t\tthis.$emit('parse', res);\r\n\t\t\t\t} else if (html.constructor == Array) {\r\n\t\t\t\t\tif (!observed) this.nodes = html;\r\n\t\t\t\t\telse this.nodes = [];\r\n\t\t\t\t\t// 非本插件产生的 array 需要进行一些转换\r\n\t\t\t\t\tif (html.length && html[0].PoweredBy != \"Parser\") {\r\n\t\t\t\t\t\tconst Parser = {\r\n\t\t\t\t\t\t\t_imgNum: 0,\r\n\t\t\t\t\t\t\t_videoNum: 0,\r\n\t\t\t\t\t\t\t_audioNum: 0,\r\n\t\t\t\t\t\t\t_domain: this.domain,\r\n\t\t\t\t\t\t\t_protocol: this.domain ? (this.domain.includes(\"://\") ? this.domain.split(\"://\")[0] : \"http\") : undefined,\r\n\t\t\t\t\t\t\t_STACK: [],\r\n\t\t\t\t\t\t\tCssHandler: new CssHandler(this.tagStyle)\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tParser.CssHandler.getStyle('');\r\n\t\t\t\t\t\tconst DFS = (nodes) => {\r\n\t\t\t\t\t\t\tfor (var node of nodes) {\r\n\t\t\t\t\t\t\t\tif (node.type == \"text\") continue;\r\n\t\t\t\t\t\t\t\tnode.attrs = node.attrs || {};\r\n\t\t\t\t\t\t\t\tfor (var item in node.attrs) {\r\n\t\t\t\t\t\t\t\t\tif (!config.trustAttrs[item]) node.attrs[item] = undefined;\r\n\t\t\t\t\t\t\t\t\telse if (typeof node.attrs[item] != \"string\") node.attrs[item] = node.attrs[item].toString();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tconfig.LabelAttrsHandler(node, Parser);\r\n\t\t\t\t\t\t\t\tif (config.blockTags[node.name]) node.name = 'div';\r\n\t\t\t\t\t\t\t\telse if (!config.trustTags[node.name]) node.name = 'span';\r\n\t\t\t\t\t\t\t\tif (node.children && node.children.length) {\r\n\t\t\t\t\t\t\t\t\tParser._STACK.push(node);\r\n\t\t\t\t\t\t\t\t\tDFS(node.children);\r\n\t\t\t\t\t\t\t\t\tParser._STACK.pop();\r\n\t\t\t\t\t\t\t\t} else node.children = undefined;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tDFS(html);\r\n\t\t\t\t\t\tthis.nodes = html;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (typeof html == 'object' && html.nodes) {\r\n\t\t\t\t\tthis.nodes = html.nodes;\r\n\t\t\t\t\tconsole.warn(\"Parser 类型错误：object 类型已废弃，请直接将 html 设置为 object.nodes （array 类型）\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.$emit('error', {\r\n\t\t\t\t\t\tsource: \"parse\",\r\n\t\t\t\t\t\terrMsg: \"传入的nodes数组格式不正确！应该传入的类型是array，实际传入的类型是：\" + typeof html.nodes\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tthis.loadVideo = false;\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (document) this.document = new document(\"html\", this.html || html, this);\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\t\tthis.videoContexts = [];\r\n\t\t\t\t\tconst getContext = (components) => {\r\n\t\t\t\t\t\tfor (let component of components) {\r\n\t\t\t\t\t\t\tif (component.$options.name == \"trees\") {\r\n\t\t\t\t\t\t\t\tvar observered = false;\r\n\t\t\t\t\t\t\t\tfor (var item of component.nodes) {\r\n\t\t\t\t\t\t\t\t\tif (item.continue) continue;\r\n\t\t\t\t\t\t\t\t\tif (item.name == 'img') {\r\n\t\t\t\t\t\t\t\t\t\tif (item.attrs.src && item.attrs.i) {\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifndef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\t\tif (this.imgList.indexOf(item.attrs.src) == -1)\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.imgList[item.attrs.i] = item.attrs.src;\r\n\t\t\t\t\t\t\t\t\t\t\telse this.imgList[item.attrs.i] = Deduplication(item.attrs.src);\r\n\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\t\tthis.imgList[item.attrs.i] = item.attrs.src;\r\n\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\t\tif (!observered) {\r\n\t\t\t\t\t\t\t\t\t\t\tobservered = true;\r\n\t\t\t\t\t\t\t\t\t\t\tif (this.lazyLoad && uni.createIntersectionObserver) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (component._observer) component._observer.disconnect();\r\n\t\t\t\t\t\t\t\t\t\t\t\tcomponent._observer = uni.createIntersectionObserver(component);\r\n\t\t\t\t\t\t\t\t\t\t\t\tcomponent._observer.relativeToViewport({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttop: 1000,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbottom: 1000\r\n\t\t\t\t\t\t\t\t\t\t\t\t}).observe('.img', res => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent.imgLoad = true;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent._observer.disconnect();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent._observer = null;\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\t\t\t\t\t\tcomponent.imgLoad = true;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\telse if (item.name == 'video') {\r\n\t\t\t\t\t\t\t\t\t\tvar context = uni.createVideoContext(item.attrs.id, component);\r\n\t\t\t\t\t\t\t\t\t\tcontext.id = item.attrs.id;\r\n\t\t\t\t\t\t\t\t\t\tthis.videoContexts.push(context);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\t\telse if (item.name == 'audio' && item.attrs.autoplay)\r\n\t\t\t\t\t\t\t\t\t\twx.createAudioContext(item.attrs.id, component).play();\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t// 设置标题\r\n\t\t\t\t\t\t\t\t\telse if (item.name == \"title\") {\r\n\t\t\t\t\t\t\t\t\t\tif (item.children[0].type == \"text\" && item.children[0].text && this.autosetTitle)\r\n\t\t\t\t\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: item.children[0].text\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// #ifdef MP-BAIDU || MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\tif (item.attrs && item.attrs.id) {\r\n\t\t\t\t\t\t\t\t\t\tthis.anchors = this.anchors || [];\r\n\t\t\t\t\t\t\t\t\t\tthis.anchors.push({\r\n\t\t\t\t\t\t\t\t\t\t\tid: item.attrs.id,\r\n\t\t\t\t\t\t\t\t\t\t\tnode: component\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (component.$children.length)\r\n\t\t\t\t\t\t\t\tgetContext(component.$children)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tgetContext(this.$children)\r\n\t\t\t\t\t\tuni.createSelectorQuery().in(this).select(\"._contain\").boundingClientRect(res => {\r\n\t\t\t\t\t\t\tthis.$emit(\"ready\", res);\r\n\t\t\t\t\t\t}).exec();\r\n\t\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.loadVideo = true;\r\n\t\t\t\t\t}, 3000);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetText(whiteSpace = true) {\r\n\t\t\t\tvar text = \"\";\r\n\t\t\t\tconst DFS = (node) => {\r\n\t\t\t\t\tif (node.type == \"text\") return text += node.text;\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tif (whiteSpace && (((node.name == 'p' || node.name == \"div\" || node.name == \"tr\" || node.name == \"li\" ||\r\n\t\t\t\t\t\t\t\t/h[1-6]/.test(node.name)) && text && text[text.length - 1] != '\\n') || node.name == \"br\"))\r\n\t\t\t\t\t\t\ttext += '\\n';\r\n\t\t\t\t\t\tfor (var child of node.children || [])\r\n\t\t\t\t\t\t\tDFS(child);\r\n\t\t\t\t\t\tif (whiteSpace && (node.name == 'p' || node.name == \"div\" || node.name == \"tr\" || node.name == \"li\" || /h[1-6]/.test(\r\n\t\t\t\t\t\t\t\tnode.name)) && text && text[text.length - 1] != '\\n')\r\n\t\t\t\t\t\t\ttext += '\\n';\r\n\t\t\t\t\t\telse if (whiteSpace && node.name == \"td\") text += '\\t';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tvar nodes = ((this.nodes && this.nodes.length) ? this.nodes : (this.html[0] && (this.html[0].name || this.html[0].type) ?\r\n\t\t\t\t\tthis.html : []));\r\n\t\t\t\tif (!nodes.length) return \"\";\r\n\t\t\t\tfor (var node of nodes)\r\n\t\t\t\t\tDFS(node);\r\n\t\t\t\treturn text;\r\n\t\t\t},\r\n\t\t\tnavigateTo(obj) {\r\n\t\t\t\tvar Scroll = (selector, component) => {\r\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(component ? component : this);\r\n\t\t\t\t\tquery.select(selector).boundingClientRect();\r\n\t\t\t\t\tquery.selectViewport().scrollOffset();\r\n\t\t\t\t\tquery.exec(res => {\r\n\t\t\t\t\t\tif (!res || !res[0])\r\n\t\t\t\t\t\t\treturn obj.fail ? obj.fail({\r\n\t\t\t\t\t\t\t\terrMsg: \"Label Not Found\"\r\n\t\t\t\t\t\t\t}) : null;\r\n\t\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\t\tscrollTop: res[1].scrollTop + res[0].top,\r\n\t\t\t\t\t\t\tsuccess: obj.success,\r\n\t\t\t\t\t\t\tfail: obj.fail\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (!obj.id) Scroll(\"._contain\");\r\n\t\t\t\telse {\r\n\t\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY\r\n\t\t\t\t\tScroll('._contain >>> #' + obj.id + ', ._contain >>> .' + obj.id);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP-BAIDU || MP-ALIPAY\r\n\t\t\t\t\tfor (var anchor of this.anchors) {\r\n\t\t\t\t\t\tif (anchor.id == obj.id) {\r\n\t\t\t\t\t\t\tScroll(\"#\" + obj.id + \", .\" + obj.id, anchor.node);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tgetVideoContext(id) {\r\n\t\t\t\tif (!id) return this.videoContexts;\r\n\t\t\t\telse {\r\n\t\t\t\t\tfor (var video of this.videoContexts) {\r\n\t\t\t\t\t\tif (video.id == id) return video;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* #ifndef MP-BAIDU */\r\n\t:host {\r\n\t\tdisplay: block;\r\n\t\toverflow: scroll;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110357649\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}