{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?fcd8", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?0531", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?25aa", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?dec5", "uni-app:///components/jyf-Parser/trees.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?2e41", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/trees.vue?b392", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/handler.wxs?80de", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/jyf-Parser/handler.wxs?d673"], "names": ["components", "trees", "name", "data", "controls", "imgLoad", "props", "nodes", "type", "default", "imgMode", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "playEvent", "video", "previewEvent", "id", "src", "ignore", "current", "uni", "urls", "tapEvent", "attrs", "appId", "path", "success", "title", "url", "triggerError", "source", "target", "errMsg", "errCode", "context", "loadSource", "adError", "videoError", "currentTarget", "audioError", "_loadVideo", "play", "index"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsT;AACtT;AACyD;AACL;AACa;;;AAGjE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,oRAAM;AACR,EAAE,6RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wRAAU;AACZ;AACA;;AAEA;AAC2a;AAC3a,WAAW,6bAAM,iBAAiB,qcAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwxB,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCsH5yB;EACAA;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MAEAC;IAEA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IAOAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA,oBACA;EACA;EAEAC;IAEAC;MACA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;YACAC;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;UACAC;UACAC;UACAC;YAAA;UAAA;QACA;QACA;UACA;YACAC;UACAC;YACAD;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;QACAC;MACAA;QAAA;MAAA;MACA;MACA;QAEA;UACA;YACAC;YACAC;UACA;QACA;QAEA;UACA;YACA,yBACA;cACAT;YACA;UACA;YACA;cACAI;gBACAlB;gBACAwB;kBACAN;oBACAO;kBACA;gBACA;cACA;YACA;UACA,OACAP;YACAQ;UACA;QACA;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,oDACA,iGACAC,kBACA;IACA;IACAC;MACA,uCACA;IACA;IACAC;MACA;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9QA;AAAA;AAAA;AAAA;AAAonC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACAxoC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAA+kB,CAAgB,mpBAAG,EAAC,C;;;;;;;;;;;;ACAnmB;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "components/jyf-Parser/trees.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./trees.vue?vue&type=template&id=3b0f4f96&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjU1MjAsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjU1MjB9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./trees.vue?vue&type=script&lang=js&\"\nexport * from \"./trees.vue?vue&type=script&lang=js&\"\nimport style0 from \"./trees.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5C29362%5CDesktop%5C%E5%90%8C%E5%9F%8E%E4%B8%8A%E9%97%A8%E5%AE%B6%E6%94%BF%E6%8C%89%E6%91%A9H5%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%BA%90%E7%A0%81%20%20%E4%B8%8A%E9%97%A8%E9%A2%84%E7%BA%A6%E7%B3%BB%E7%BB%9F%5C%E5%89%8D%E7%AB%AF%E6%9C%AA%E7%BC%96%E8%AF%91%5Cuniapp%5Cwechat%5Ccomponents%5Cjyf-Parser%5Ctrees.vue&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/jyf-Parser/trees.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=template&id=3b0f4f96&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjU1MjAsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjU1MjB9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=script&lang=js&\"", "<!--\r\n trees 递归显示组件\r\n github地址：https://github.com/jin-yufeng/Parser\r\n 文档地址：https://jin-yufeng.github.io/Parser\r\n 插件市场：https://ext.dcloud.net.cn/plugin?id=805\r\n author：Jin<PERSON>ufeng\r\n-->\r\n<template>\r\n\t<view style=\"display: inherit; white-space: inherit;word-break:normal\">\r\n\t\t<block v-for=\"(item, index) in nodes\" v-bind:key=\"index\">\r\n\t\t\t<!-- {{item.name}} {{item}} -->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY || APP-PLUS-->\r\n\t\t\t<block v-if=\"handler.notContinue(item)\">\r\n\t\t\t\t<!--#endif-->\r\n\t\t\t\t<!--#ifdef MP-BAIDU || MP-TOUTIAO-->\r\n\t\t\t\t<block v-if=\"!item.c\">\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--图片-->\r\n\t\t\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY || APP-PLUS-->\r\n\t\t\t\t\t<rich-text v-if=\"item.name=='img'\" :id=\"item.attrs.id\" class=\"img\"\r\n\t\t\t\t\t\t:style=\"'text-indent:0;'+handler.getStyle(item.attrs.style, 'inline-block')\"\r\n\t\t\t\t\t\t:nodes='handler.setImgStyle(item, imgMode, imgLoad)' :data-attrs=\"item.attrs\"\r\n\t\t\t\t\t\t@tap='previewEvent' />\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--#ifdef MP-BAIDU || MP-TOUTIAO-->\r\n\t\t\t\t\t<rich-text v-if=\"item.name=='img'\" :id=\"item.attrs.id\"\r\n\t\t\t\t\t\t:style=\"'text-indent:0;'+item.attrs.containStyle\" :nodes='[item]' :data-attrs=\"item.attrs\"\r\n\t\t\t\t\t\t@tap='previewEvent' />\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--文本-->\r\n\t\t\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || APP-PLUS-->\r\n\t\t\t\t\t<block v-else-if=\"item.type=='text'\">\r\n\t\t\t\t\t\t<text v-if=\"!item.decode\" decode>{{item.text}}</text>\r\n\t\t\t\t\t\t<rich-text v-else style=\"display:inline-block;word-break:normal\" :nodes=\"[item]\"></rich-text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t\t\t\t<text v-else-if=\"item.type=='text'\" decode>{{item.text}}</text>\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<text v-else-if=\"item.name=='br'\">\\n</text>\r\n\t\t\t\t\t<!--视频-->\r\n\t\t\t\t\t<block v-else-if=\"item.name=='video'\">\r\n\t\t\t\t\t\t<!--#ifdef APP-PLUS-->\r\n\t\t\t\t\t\t<view v-if=\"(!loadVideo||item.lazyLoad)&&!controls[item.attrs.id].play\" :id=\"item.attrs.id\"\r\n\t\t\t\t\t\t\t:class=\"'_video '+(item.attrs.class||'')\" :style=\"item.attrs.style||''\" @tap=\"_loadVideo\" />\r\n\t\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t\t<!--#ifndef APP-PLUS-->\r\n\t\t\t\t\t\t<view v-if=\"item.lazyLoad&&!controls[item.attrs.id].play\" :id=\"item.attrs.id\"\r\n\t\t\t\t\t\t\t:class=\"'_video '+(item.attrs.class||'')\" :style=\"item.attrs.style||''\" @tap=\"_loadVideo\" />\r\n\t\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t\t<video v-else\r\n\t\t\t\t\t\t\t:src=\"controls[item.attrs.id]?item.attrs.source[controls[item.attrs.id].index] : item.attrs.src\"\r\n\t\t\t\t\t\t\t:id=\"item.attrs.id\" :loop=\"item.attrs.loop\" :controls=\"item.attrs.controls\"\r\n\t\t\t\t\t\t\t:autoplay=\"item.attrs.autoplay||(controls[item.attrs.id]&&controls[item.attrs.id].play)\"\r\n\t\t\t\t\t\t\t:unit-id=\"item.attrs['unit-id']\" :class=\"item.attrs.class\" :muted=\"item.attrs.muted\"\r\n\t\t\t\t\t\t\t:style=\"item.attrs.style||''\" :data-source=\"item.attrs.source\" @play=\"playEvent\"\r\n\t\t\t\t\t\t\t@error=\"videoError\" />\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!-- iframe 【create_by_xx】-->\r\n\t\t\t\t\t<block v-else-if=\"item.name=='iframe'\">\r\n\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t\t<!-- 腾讯视频 -->\r\n\t\t\t\t\t\t<txv-video :vid=\"item.attrs.src\" :playerid=\"item.attrs.src\" width=\"100%\" height=\"100%\"\r\n\t\t\t\t\t\t\t:controls=\"true\" :autoplay=\"false\" :isHiddenStop=\"true\"\r\n\t\t\t\t\t\t\tv-if=\"item.attrs.lbType=='vid' || item.attrs.lbtype=='vid'\">\r\n\t\t\t\t\t\t</txv-video>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<!--音频-->\r\n\t\t\t\t\t<audio v-else-if=\"item.name=='audio'\"\r\n\t\t\t\t\t\t:src=\"controls[item.attrs.id]?item.attrs.source[controls[item.attrs.id].index] : item.attrs.src\"\r\n\t\t\t\t\t\t:id=\"item.attrs.id\" :loop=\"item.attrs.loop\" :controls=\"item.attrs.controls\"\r\n\t\t\t\t\t\t:poster=\"item.attrs.poster\" :name=\"item.attrs.name\" :author=\"item.attrs.author\"\r\n\t\t\t\t\t\t:class=\"item.attrs.class\" :style=\"item.attrs.style||''\" :data-source=\"item.attrs.source\"\r\n\t\t\t\t\t\t@error=\"audioError\" />\r\n\t\t\t\t\t<!--链接-->\r\n\t\t\t\t\t<view v-else-if=\"item.name=='a'\" :class=\"'_a '+(item.attrs.class||'')\" :style=\"item.attrs.style||''\"\r\n\t\t\t\t\t\t:data-attrs=\"item.attrs\" hover-class=\"navigator-hover\" :hover-start-time=\"25\"\r\n\t\t\t\t\t\t:hover-stay-time=\"300\" @tap=\"tapEvent\">\r\n\t\t\t\t\t\t<trees :nodes=\"item.children\" :imgMode=\"imgMode\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!--广告-->\r\n\t\t\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ-->\r\n\t\t\t\t\t<ad v-else-if=\"item.name=='ad'\" :unit-id=\"item.attrs['unit-id']\" :class=\"item.attrs.class||''\"\r\n\t\t\t\t\t\t:style=\"item.attrs.style||''\" @error=\"adError\"></ad>\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--#ifdef MP-BAIDU-->\r\n\t\t\t\t\t<ad v-else-if=\"item.name=='ad'\" :appid=\"item.attrs.appid\" :apid=\"item.attrs.apid\"\r\n\t\t\t\t\t\t:type=\"item.attrs.type\" :class=\"item.attrs.class||''\" :style=\"item.attrs.style\"\r\n\t\t\t\t\t\t@error=\"adError\"></ad>\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--富文本-->\r\n\t\t\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY || APP-PLUS-->\r\n\t\t\t\t\t<rich-text v-else :id=\"item.attrs.id\" :class=\"'__'+item.name\"\r\n\t\t\t\t\t\t:style=\"'word-break:normal;'+handler.getStyle(item.attrs.style, 'block')\" :nodes=\"handler.setStyle(item)\" />\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t<!--#ifdef MP-BAIDU || MP-TOUTIAO-->\r\n\t\t\t\t\t<rich-text v-else :id=\"item.attrs.id\" :style=\"item.attrs?item.attrs.containStyle:''\"\r\n\t\t\t\t\t\t:nodes=\"[item]\" />\r\n\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t</block>\r\n\t\t\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t\t\t<view v-else :id=\"item.attrs.id\" :class=\"'_'+item.name+' '+(item.attrs.class||'')\"\r\n\t\t\t\t\t:style=\"item.attrs.style||''\">\r\n\t\t\t\t\t<trees :nodes=\"item.children\" :imgMode=\"imgMode\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--#endif-->\r\n\t\t\t\t<!--#ifndef MP-ALIPAY-->\r\n\t\t\t\t<trees v-else :class=\"item.attrs.id+' _'+item.name+' '+(item.attrs.class||'')\"\r\n\t\t\t\t\t:style=\"item.attrs.style||''\" :nodes=\"item.children\" :imgMode=\"imgMode\" :loadVideo=\"loadVideo\" />\r\n\t\t\t\t<!--#endif-->\r\n\t\t\t</block>\r\n\t</view>\r\n</template>\r\n<script module=\"handler\" lang=\"wxs\" src=\"./handler.wxs\"></script>\r\n<script module=\"handler\" lang=\"sjs\" src=\"./handler.sjs\"></script>\r\n<script>\r\n\timport trees from \"./trees\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\tname: 'trees',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcontrols: {},\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || APP-PLUS\r\n\t\t\t\timgLoad: true\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tnodes: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tloadVideo: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\timgMode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"default\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 获取顶层组件\r\n\t\t\tthis._top = this.$parent;\r\n\t\t\twhile (this._top.$options.name != 'parser') {\r\n\t\t\t\tif (this._top._top) {\r\n\t\t\t\t\tthis._top = this._top._top;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tthis._top = this._top.$parent;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef MP-WEIXIN || MP-QQ || APP-PLUS\r\n\t\tbeforeDestroy() {\r\n\t\t\tif (this._observer)\r\n\t\t\t\tthis._observer.disconnect();\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\tplayEvent(e) {\r\n\t\t\t\tif ((this._top.videoContexts || []).length > 1 && this._top.autopause) {\r\n\t\t\t\t\tfor (var video of this._top.videoContexts) {\r\n\t\t\t\t\t\tif (video.id == e.currentTarget.id) continue;\r\n\t\t\t\t\t\tvideo.pause();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tpreviewEvent(e) {\r\n\t\t\t\tvar attrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tif (!attrs.ignore) {\r\n\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\tthis._top.$emit('imgtap', {\r\n\t\t\t\t\t\tid: e.currentTarget.id,\r\n\t\t\t\t\t\tsrc: attrs.src,\r\n\t\t\t\t\t\tignore: () => preview = false\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (preview && this._top.autopreview) {\r\n\t\t\t\t\t\tvar urls = this._top.imgList || [],\r\n\t\t\t\t\t\t\tcurrent = urls[attrs.i] ? attrs.i : (urls = [attrs.src], 0);\r\n\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\tcurrent,\r\n\t\t\t\t\t\t\turls\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttapEvent(e) {\r\n\t\t\t\tvar jump = true,\r\n\t\t\t\t\tattrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tattrs.ignore = () => jump = false;\r\n\t\t\t\tthis._top.$emit('linkpress', attrs);\r\n\t\t\t\tif (jump) {\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tif (attrs['app-id'] || attrs.appId) {\r\n\t\t\t\t\t\treturn uni.navigateToMiniProgram({\r\n\t\t\t\t\t\t\tappId: attrs['app-id'] || attrs.appId,\r\n\t\t\t\t\t\t\tpath: attrs.path || ''\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tif (attrs.href) {\r\n\t\t\t\t\t\tif (attrs.href[0] == \"#\") {\r\n\t\t\t\t\t\t\tif (this._top.useAnchor)\r\n\t\t\t\t\t\t\t\tthis._top.navigateTo({\r\n\t\t\t\t\t\t\t\t\tid: attrs.href.substring(1)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (attrs.href.indexOf(\"http\") == 0 || attrs.href.indexOf(\"//\") == 0) {\r\n\t\t\t\t\t\t\tif (this._top.autocopy) {\r\n\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\tdata: attrs.href,\r\n\t\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: attrs.href\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttriggerError(source, target, errMsg, errCode, context) {\r\n\t\t\t\tthis._top.$emit('error', {\r\n\t\t\t\t\tsource,\r\n\t\t\t\t\ttarget,\r\n\t\t\t\t\terrMsg,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tcontext\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tloadSource(target) {\r\n\t\t\t\t// console.log(target)\r\n\t\t\t\tvar index = (this.controls[target.id] ? this.controls[target.id].index : 0) + 1;\r\n\t\t\t\tif (index < target.dataset.source.length) {\r\n\t\t\t\t\tthis.$set(this.controls[target.id], \"index\", index);\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t},\r\n\t\t\tadError(e) {\r\n\t\t\t\tthis.triggerError(\"ad\", e.currentTarget, \"\", e.detail.errorCode);\r\n\t\t\t},\r\n\t\t\tvideoError(e) {\r\n\t\t\t\tif (!this.loadSource(e.currentTarget) && this._top)\r\n\t\t\t\t\tthis.triggerError(\"video\", e.currentTarget, e.detail.errMsg, undefined, uni.createVideoContext(e\r\n\t\t\t\t\t\t.currentTarget.id,\r\n\t\t\t\t\t\tthis));\r\n\t\t\t},\r\n\t\t\taudioError(e) {\r\n\t\t\t\tif (!this.loadSource(e.currentTarget))\r\n\t\t\t\t\tthis.triggerError(\"audio\", e.currentTarget, e.detail.errMsg);\r\n\t\t\t},\r\n\t\t\t_loadVideo(e) {\r\n\t\t\t\tthis.$set(this.controls, e.currentTarget.id, {\r\n\t\t\t\t\tplay: true,\r\n\t\t\t\t\tindex: 0\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 可以在这里引入自定义的外部样式 */\r\n\r\n\t/* 链接受到点击的hover-class，可自定义修改 */\r\n\t.navigator-hover {\r\n\t\topacity: 0.7;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\t/* 以下内容不建议修改 */\r\n\t/* #ifndef MP-BAIDU */\r\n\t:host {\r\n\t\tdisplay: inherit;\r\n\t\tfloat: inherit;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t._b,\r\n\t._strong {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t._big {\r\n\t\tfont-size: 1.2em;\r\n\t}\r\n\r\n\t._small {\r\n\t\tfont-size: 0.8em;\r\n\t}\r\n\r\n\t._blockquote,\r\n\t._div,\r\n\t._p {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t._code {\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t._del {\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t._em,\r\n\t._i {\r\n\t\tfont-style: italic;\r\n\t}\r\n\r\n\t._h1 {\r\n\t\tfont-size: 2em;\r\n\t}\r\n\r\n\t._h2 {\r\n\t\tfont-size: 1.5em;\r\n\t}\r\n\r\n\t._h3 {\r\n\t\tfont-size: 1.17em;\r\n\t}\r\n\r\n\t._h5 {\r\n\t\tfont-size: 0.67em;\r\n\t}\r\n\r\n\t._h1,\r\n\t._h2,\r\n\t._h3,\r\n\t._h4,\r\n\t._h5,\r\n\t._h6 {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t._ins {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\t._q::before {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._q::after {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._a,\r\n\t._abbr,\r\n\t._b,\r\n\t._big,\r\n\t._small,\r\n\t._code,\r\n\t._del,\r\n\t._em,\r\n\t._i,\r\n\t._ins,\r\n\t._label,\r\n\t._q,\r\n\t._span,\r\n\t._strong {\r\n\t\tdisplay: inline;\r\n\t}\r\n\r\n\t/* #ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY */\r\n\t.__sub,\r\n\t.__sup,\r\n\t.__bdo,\r\n\t.__bdi,\r\n\t.__ruby,\r\n\t.__rt {\r\n\t\tdisplay: inline-block !important;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t._video {\r\n\t\tbackground-color: black;\r\n\t\twidth: 300px;\r\n\t\theight: 225px;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t._video::after {\r\n\t\tcontent: '';\r\n\t\tborder-width: 15px 0 15px 30px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: transparent transparent transparent white;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t\tmargin: -15px 0 0 -15px;\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110360077\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5C29362%5CDesktop%5C%E5%90%8C%E5%9F%8E%E4%B8%8A%E9%97%A8%E5%AE%B6%E6%94%BF%E6%8C%89%E6%91%A9H5%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%BA%90%E7%A0%81%20%20%E4%B8%8A%E9%97%A8%E9%A2%84%E7%BA%A6%E7%B3%BB%E7%BB%9F%5C%E5%89%8D%E7%AB%AF%E6%9C%AA%E7%BC%96%E8%AF%91%5Cuniapp%5Cwechat%5Ccomponents%5Cjyf-Parser%5Ctrees.vue&module=handler&lang=wxs\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5C29362%5CDesktop%5C%E5%90%8C%E5%9F%8E%E4%B8%8A%E9%97%A8%E5%AE%B6%E6%94%BF%E6%8C%89%E6%91%A9H5%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%BA%90%E7%A0%81%20%20%E4%B8%8A%E9%97%A8%E9%A2%84%E7%BA%A6%E7%B3%BB%E7%BB%9F%5C%E5%89%8D%E7%AB%AF%E6%9C%AA%E7%BC%96%E8%AF%91%5Cuniapp%5Cwechat%5Ccomponents%5Cjyf-Parser%5Ctrees.vue&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}