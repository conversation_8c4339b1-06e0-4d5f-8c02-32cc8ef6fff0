{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?e3b6", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?5f0c", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?2dad", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?ec96", "uni-app:///components/w-picker/region-picker.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?b47c", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/w-picker/region-picker.vue?d5af"], "names": ["data", "pickVal", "range", "provinces", "citys", "areas", "checkObj", "props", "itemHeight", "type", "default", "value", "defaultType", "hide<PERSON><PERSON>", "watch", "created", "methods", "getData", "provinceIndex", "cityIndex", "areaIndex", "dVal", "province", "city", "area", "obj", "initData", "result", "handler<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgyB,CAAgB,gzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiBpzB;;;;;;;;;;;;;;;;;eACA;EACAA;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACAH;MACA;IACA;EACA;EACAI;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACAC;MACA;MACA;QACA;MACA;MACAC;MACA;MACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;QACAF;QACAC;MACA;QACAD;QACAC;QACAC;MACA;MACA;QACArB;QACAC;QACAiB;QACAI;MACA;QACAtB;QACAC;QACAC;QACAgB;QACAI;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QAAAH;QAAAC;MACA;MACA;MACA;QACArB;QACAC;MACA;QACAD;QACAC;QACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACAsB;QACAhB;QACAc;MACA;IACA;IACAG;MAAA;MACA;MACA;QAAAT;QAAAC;MACA;MACA;MACA;MACA;QACAG;QACAC;MACA;QACAF;QACAC;MACA;QACAD;QACAC;QACAC;MACA;MACA;QACA;QACA;QACA;UACA;QACA;MAEA;MACA;QACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAG;QACAhB;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAA+9C,CAAgB,m7CAAG,EAAC,C;;;;;;;;;;;ACAn/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/w-picker/region-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./region-picker.vue?vue&type=template&id=10040257&\"\nvar renderjs\nimport script from \"./region-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./region-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./region-picker.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/w-picker/region-picker.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./region-picker.vue?vue&type=template&id=10040257&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./region-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./region-picker.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"w-picker-view\">\r\n\t\t<picker-view class=\"d-picker-view\" :indicator-style=\"itemHeight\" :value=\"pickVal\" @change=\"handlerChange\">\r\n\t\t\t<picker-view-column>\r\n\t\t\t\t<view class=\"w-picker-item\" v-for=\"(item,index) in range.provinces\" :key=\"index\">{{item.label}}</view>\r\n\t\t\t</picker-view-column>\r\n\t\t\t<picker-view-column>\r\n\t\t\t\t<view class=\"w-picker-item\" v-for=\"(item,index) in range.citys\" :key=\"index\">{{item.label}}</view>\r\n\t\t\t</picker-view-column>\r\n\t\t\t<picker-view-column v-if=\"!hideArea\">\r\n\t\t\t\t<view class=\"w-picker-item\" v-for=\"(item,index) in range.areas\" :key=\"index\">{{item.label}}</view>\r\n\t\t\t</picker-view-column>\r\n\t\t</picker-view>\r\n\t</view>\n</template>\n\n<script>\r\n\timport areaData from \"./areadata/areadata.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpickVal:[],\r\n\t\t\t\trange:{\r\n\t\t\t\t\tprovinces:[],\r\n\t\t\t\t\tcitys:[],\r\n\t\t\t\t\tareas:[]\r\n\t\t\t\t},\r\n\t\t\t\tcheckObj:{}\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops:{\r\n\t\t\titemHeight:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:\"44px\"\r\n\t\t\t},\r\n\t\t\tvalue:{\r\n\t\t\t\ttype:[Array,String],\r\n\t\t\t\tdefault:\"\"\r\n\t\t\t},\r\n\t\t\tdefaultType:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:\"label\"\r\n\t\t\t},\r\n\t\t\thideArea:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tvalue(val){\r\n\t\t\t\tthis.initData();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.initData();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetData(){\r\n\t\t\t\t//用来处理初始化数据\r\n\t\t\t\tlet provinces=areaData;\r\n\t\t\t\tlet dVal=[];\r\n\t\t\t\tlet value=this.value;\r\n\t\t\t\tlet a1=value[0];//默认值省\r\n\t\t\t\tlet a2=value[1];//默认值市\r\n\t\t\t\tlet a3=value[2];//默认值区、县\r\n\t\t\t\tlet province,city,area;\r\n\t\t\t\tlet provinceIndex=provinces.findIndex((v)=>{\r\n\t\t\t\t\treturn v[this.defaultType]==a1\r\n\t\t\t\t});\r\n\t\t\t\tprovinceIndex=value?(provinceIndex!=-1?provinceIndex:0):0;\r\n\t\t\t\tlet citys=provinces[provinceIndex].children;\r\n\t\t\t\tlet cityIndex=citys.findIndex((v)=>{\r\n\t\t\t\t\treturn v[this.defaultType]==a2\r\n\t\t\t\t});\r\n\t\t\t\tcityIndex=value?(cityIndex!=-1?cityIndex:0):0;\r\n\t\t\t\tlet areas=citys[cityIndex].children;\r\n\t\t\t\tlet areaIndex=areas.findIndex((v)=>{\r\n\t\t\t\t\treturn v[this.defaultType]==a3;\r\n\t\t\t\t});\r\n\t\t\t\tareaIndex=value?(areaIndex!=-1?areaIndex:0):0;\r\n\t\t\t\tdVal=this.hideArea?[provinceIndex,cityIndex]:[provinceIndex,cityIndex,areaIndex];\r\n\t\t\t\tprovince=provinces[provinceIndex];\r\n\t\t\t\tcity=citys[cityIndex];\r\n\t\t\t\tarea=areas[areaIndex];\r\n\t\t\t\tlet obj=this.hideArea?{\r\n\t\t\t\t\tprovince,\r\n\t\t\t\t\tcity\r\n\t\t\t\t}:{\r\n\t\t\t\t\tprovince,\r\n\t\t\t\t\tcity,\r\n\t\t\t\t\tarea\r\n\t\t\t\t}\r\n\t\t\t\treturn this.hideArea?{\r\n\t\t\t\t\tprovinces,\r\n\t\t\t\t\tcitys,\r\n\t\t\t\t\tdVal,\r\n\t\t\t\t\tobj\r\n\t\t\t\t}:{\r\n\t\t\t\t\tprovinces,\r\n\t\t\t\t\tcitys,\r\n\t\t\t\t\tareas,\r\n\t\t\t\t\tdVal,\r\n\t\t\t\t\tobj\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitData(){\r\n\t\t\t\tlet dataData=this.getData();\r\n\t\t\t\tlet provinces=dataData.provinces;\r\n\t\t\t\tlet citys=dataData.citys;\r\n\t\t\t\tlet areas=this.hideArea?[]:dataData.areas;\r\n\t\t\t\tlet obj=dataData.obj;\r\n\t\t\t\tlet province=obj.province,city=obj.city,area=this.hideArea?{}:obj.area;\r\n\t\t\t\tlet value=this.hideArea?[province.value,city.value]:[province.value,city.value,area.value];\r\n\t\t\t\tlet result=this.hideArea?`${province.label+city.label}`:`${province.label+city.label+area.label}`;\r\n\t\t\t\tthis.range=this.hideArea?{\r\n\t\t\t\t\tprovinces,\r\n\t\t\t\t\tcitys,\r\n\t\t\t\t}:{\r\n\t\t\t\t\tprovinces,\r\n\t\t\t\t\tcitys,\r\n\t\t\t\t\tareas\r\n\t\t\t\t};\r\n\t\t\t\tthis.checkObj=obj;\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis.pickVal=dataData.dVal;\r\n\t\t\t\t});\r\n\t\t\t\tthis.$emit(\"change\",{\r\n\t\t\t\t\tresult:result,\r\n\t\t\t\t\tvalue:value,\r\n\t\t\t\t\tobj:obj\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandlerChange(e){\r\n\t\t\t\tlet arr=[...e.detail.value];\r\n\t\t\t\tlet provinceIndex=arr[0],cityIndex=arr[1],areaIndex=this.hideArea?0:arr[2];\r\n\t\t\t\tlet provinces=areaData;\r\n\t\t\t\tlet citys=(provinces[provinceIndex]&&provinces[provinceIndex].children)||provinces[provinces.length-1].children||[];\r\n\t\t\t\tlet areas=this.hideArea?[]:((citys[cityIndex]&&citys[cityIndex].children)||citys[citys.length-1].children||[]);\r\n\t\t\t\tlet province=provinces[provinceIndex]||provinces[provinces.length-1],\r\n\t\t\t\tcity=citys[cityIndex]||[citys.length-1],\r\n\t\t\t\tarea=this.hideArea?{}:(areas[areaIndex]||[areas.length-1]);\r\n\t\t\t\tlet obj=this.hideArea?{\r\n\t\t\t\t\tprovince,\r\n\t\t\t\t\tcity\r\n\t\t\t\t}:{\r\n\t\t\t\t\tprovince,\r\n\t\t\t\t\tcity,\r\n\t\t\t\t\tarea\r\n\t\t\t\t}\r\n\t\t\t\tif(this.checkObj.province.label!=province.label){\r\n\t\t\t\t\t//当省更新的时候需要刷新市、区县的数据;\r\n\t\t\t\t\tthis.range.citys=citys;\r\n\t\t\t\t\tif(!this.hideArea){\r\n\t\t\t\t\t\tthis.range.areas=areas;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t\tif(this.checkObj.city.label!=city.label){\r\n\t\t\t\t\t//当市更新的时候需要刷新区县的数据;\r\n\t\t\t\t\tif(!this.hideArea){\r\n\t\t\t\t\t\tthis.range.areas=areas;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.checkObj=obj;\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis.pickVal=arr;\r\n\t\t\t\t})\r\n\t\t\t\tlet result=this.hideArea?`${province.label+city.label}`:`${province.label+city.label+area.label}`;\r\n\t\t\t\tlet value=this.hideArea?[province.value,city.value]:[province.value,city.value,area.value];\r\n\t\t\t\tthis.$emit(\"change\",{\r\n\t\t\t\t\tresult:result,\r\n\t\t\t\t\tvalue:value,\r\n\t\t\t\t\tobj:obj\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</script>\n\n<style lang=\"scss\">\n\t@import \"./w-picker.css\";\t\n</style>\n\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./region-picker.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./region-picker.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110369249\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}