{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?7752", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?0e84", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?acb7", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?ace8", "uni-app:///agent/pages/order/detail.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?dac9", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/detail.vue?6b95"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "timeline", "data", "options", "statusType", "technicianStatusOperType", "carType", "payType", "lineList", "base_service", "pay_type", "title", "time", "icon", "addr", "img", "base_bell", "detail", "check_label", "coach_refund_text", "lockTap", "popupInfo", "type", "param", "imgs", "location", "lat", "lng", "address", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "haveOperItem", "over_time_text", "onLoad", "agent", "onShow", "key", "val", "methods", "initIndex", "refresh", "id", "<PERSON><PERSON><PERSON>", "bg", "time_long", "true_time_long", "receiving_time", "start_service_time", "is_add", "store_id", "start_service_time_unix", "ind", "initRefresh", "countEnd", "setTimeout", "toConfirm", "order_id", "can_refund_price", "add_flow_path", "text_type", "confirmChangeOrder", "add_service", "text", "uni", "content", "confirmText", "res_del", "confirm", "msg", "url", "openType", "toTel", "toMap", "privacyCheck", "address_info", "latitude", "longitude", "name", "scale", "toCopy", "filters", "handlePayOrder"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrJA;AAAA;AAAA;AAAA;AAAyxB,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC8T7yB;AAIA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;QACAE;QACAD;MACA;QACAH;QACAC;QACAC;QACAG;QACAD;QACAD;MACA;QACAH;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;QACAG;QACAD;QACAD;MACA;QACAH;QACAC;QACAC;QACAG;QACAF;MACA;MACAG;QACAN;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;QACAC;MACA;QACAH;QACAC;QACAC;QACAG;QACAD;QACAD;MACA;MACAI;QACAP;MACA;MACAQ;MACAC;MACAC;MACAC;QACAV;QACAW;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MACA;IACA;EACA;EACAC;IACA,qBAEAjC,QADAkC;MAAAA;IAEAlC;IACA;IACA;EACA;EACAmC;IACA;IACA;IACA;MACAC;MACAC;IACA;EACA;EACAC,uDACA,2DACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MASA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,gBAKA,eAFAC,uBACAP;gBAEAQ;gBAAA;gBAAA,OACA;kBACAD;gBACA;cAAA;gBAFA1C;gBAGA;kBACA4C;gBACA;gBACA5C;gBAEAQ,WAMAR,KANAQ,UACAqC,YAKA7C,KALA8C,uCAKA9C,KAJA+C,sHAIA/C,KAHAgD,uHAGAhD,KAFAiD,8EAEAjD,KADAkD;gBAEA;kBACA;oBACAlD;kBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;kBACAmD;kBACAnD;gBACA;gBACAM;gBACA8C;kBACA;gBACA;gBACA;kBACA9C;oBACAE;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA0C;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,gBAQA,eANAC,6BACAjD,wEACAyC,mHACAC,mFACAQ,yFACAC;gBAEAvC,sFACA8B;gBACA;kBACAO;kBACArC;kBACAwC;kBACAF;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAxC;gBAEAD,OAEAC,MAFAD,MACAwC,YACAvC,MADAuC;gBAEAvC;gBAAA,gBAIA,eAFA4B,qEACAa;gBAAA,MAEAzC;kBAAA;kBAAA;gBAAA;gBACA0C;gBACAD;kBACAC;gBACA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBALAC;gBACAC;gBAAA,IAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAEAjE,2BACA,OADAA,0BAEA;gBACAkE;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAEAlC,QACA,eADAA;gBAEAQ;gBAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBACA;kBACAlC;gBACA;gBACA;kBACA4B;kBACAC;gBACA;gBACA;gBACA;gBACAiB;kBACA;gBACA;gBACA;kBACAA;oBACA;sBACAe;sBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBACAlC;kBACAC;gBACA;gBACAiB;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,gBAIA,eAFAf,6BACAjD;gBAGA2B,QACA,eADAA;gBAEAQ;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAc;gBACA;cAAA;gBAFAa;gBAAA,IAGAA;kBAAA;kBAAA;gBAAA;gBACA;kBACA7D;gBACA;gBAAA;cAAA;gBAGA;kBACA6D;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAEAF;gBACA;kBACA5D;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAgE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,oBASA,oBAJAhD,+EACAiD,4FACAnD,6BACAC;gBAAA;gBAAA,OAEA;kBACAL;gBACA;cAAA;gBAAA;gBAAA,OACA4C;kBACA5C;gBACA;cAAA;gBAAA;gBAAA,OACA4C;kBACAY;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAEA;MACA;QACA;QACA;MACA;MAEA;QACAV;QACAC;MACA;IACA;EAAA,EACA;EACAU;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrsBA;AAAA;AAAA;AAAA;AAAw9C,CAAgB,46CAAG,EAAC,C;;;;;;;;;;;ACA5+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/order/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/order/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=40a37816&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/order/detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=40a37816&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.detail.id &&\n    (_vm.detail.pay_type == -1 || _vm.detail.pay_type == 8) &&\n    _vm.detail.coach_refund_time\n      ? _vm.$t(\"action.attendantName\")\n      : null\n  var l0 = _vm.detail.id\n    ? _vm.__map(_vm.detail.order_goods, function (aitem, aindex) {\n        var $orig = _vm.__get_orig(aitem)\n        var g0 = _vm.detail.order_goods.length\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var f0 =\n    _vm.detail.id && _vm.detail.pay_order_times\n      ? _vm._f(\"handlePayOrder\")(_vm.detail.pay_order_times)\n      : null\n  var g1 =\n    _vm.detail.id && !_vm.detail.phone_encryption\n      ? _vm.detail.address_info.mobile.substring(0, 3)\n      : null\n  var g2 =\n    _vm.detail.id && !_vm.detail.phone_encryption\n      ? _vm.detail.address_info.mobile.substring(7, 11)\n      : null\n  var m1 =\n    _vm.detail.id &&\n    !_vm.detail.is_add &&\n    !_vm.detail.store_id &&\n    _vm.detail.car_type == 1 &&\n    _vm.detail.free_fare &&\n    !(_vm.detail.free_fare == 1)\n      ? _vm.$t(\"action.attendantName\")\n      : null\n  var g3 = _vm.detail.id\n    ? ([2, 3, 4, 5, 6, 8].includes(_vm.detail.pay_type) &&\n        !_vm.detail.is_add) ||\n      ((([2, 3, 6].includes(_vm.detail.pay_type) &&\n        _vm.detail.add_flow_path === 1) ||\n        (_vm.detail.pay_type == 2 && _vm.detail.add_flow_path == 2)) &&\n        _vm.detail.is_add)\n    : null\n  var g4 =\n    _vm.detail.id && g3\n      ? ([2, 3, 4, 5, 6, 8].includes(_vm.detail.pay_type) &&\n          !_vm.detail.is_add) ||\n        ((([2, 3, 6].includes(_vm.detail.pay_type) &&\n          _vm.detail.add_flow_path === 1) ||\n          (_vm.detail.pay_type == 2 && _vm.detail.add_flow_path == 2)) &&\n          _vm.detail.is_add)\n      : null\n  var m2 =\n    _vm.detail.id && g3 && g4 && !_vm.detail.is_add\n      ? _vm.$t(\"action.transferOrder\")\n      : null\n  var m3 =\n    _vm.detail.id && g3 && g4\n      ? _vm.$t(\n          \"action.\" +\n            _vm.technicianStatusOperType[\n              _vm.detail.pay_type === 3 &&\n              (_vm.detail.store_id || _vm.detail.is_add)\n                ? 5\n                : _vm.detail.pay_type == 8\n                ? -1\n                : _vm.detail.pay_type\n            ]\n        )\n      : null\n  var m4 = _vm.detail.id\n    ? _vm.$t(\"action.\" + _vm.technicianStatusOperType[_vm.popupInfo.text_type])\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.refuse_item.open()\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url:\n          \"/agent/pages/order/abnormal?id=\" +\n          _vm.detail.abn_order_id +\n          \"&agent=\" +\n          _vm.options.agent,\n      })\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: _vm.detail.store_info.phone,\n        openType: \"call\",\n      })\n    }\n    _vm.e3 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: _vm.detail.coach_info.mobile,\n        openType: \"call\",\n      })\n    }\n    _vm.e4 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url:\n          \"/agent/pages/order/change?id=\" +\n          _vm.options.id +\n          \"&agent=\" +\n          _vm.options.agent,\n      })\n    }\n    _vm.e5 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.refuse_item.close()\n    }\n    _vm.e6 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.change_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        f0: f0,\n        g1: g1,\n        g2: g2,\n        m1: m1,\n        g3: g3,\n        g4: g4,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order-pages\" v-if=\"detail.id\">\r\n\t\t<view class=\"item-child pd-lg fill-base f-paragraph c-base\" :style=\"{background:primaryColor}\">\r\n\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t<view class=\"flex-y-baseline\">\r\n\t\t\t\t\t<view class=\"text-bold\">{{statusType[detail.pay_type]}}</view>\r\n\t\t\t\t\t<view @tap.stop=\"$refs.refuse_item.open()\" class=\"ml-md\"\r\n\t\t\t\t\t\tv-if=\"(detail.pay_type == -1 || detail.pay_type == 8) && detail.coach_refund_time\">\r\n\t\t\t\t\t\t{{$t('action.attendantName')}}拒单，查看原因\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view\r\n\t\t\t\t\**********=\"$util.goUrl({url:`/agent/pages/order/abnormal?id=${detail.abn_order_id}&agent=${options.agent}`})\"\r\n\t\t\t\t\tclass=\"flex-y-center f-paragraph c-base\" v-if=\"detail.abn_order_id\">此单被平台标记为异常\r\n\t\t\t\t\t<view class=\"view-btn flex-center fill-base f-desc c-warning ml-md\">查看</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"space-lg\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- // pay_type 1待支付，2待服务，3技-师接单，4技-师出发，5技-师到达，6服务中，7已完成，8待评价 -->\r\n\t\t<view\r\n\t\t\tclass=\"menu-list flex-warp rel ml-lg mr-lg pt-lg pb-lg pl-md pr-md fill-base f-paragraph c-caption radius-16\"\r\n\t\t\t:class=\"[{'add-bell':detail.is_add || detail.store_id}]\">\r\n\t\t\t<view class=\"menu-line abs b-1px-b\"></view>\r\n\t\t\t<block v-for=\"(item,index) in lineList\" :key=\"index\">\r\n\t\t\t\t<view class=\"item-child flex-center flex-column f-icontext c-paragraph\"\r\n\t\t\t\t\t:style=\"{color:(detail.pay_type > item.pay_type -1) && detail.pay_type != 8 ?primaryColor:''}\"\r\n\t\t\t\t\tv-if=\"item.icon\">\r\n\t\t\t\t\t<view class=\"item-img fill-base flex-center mb-sm radius\"\r\n\t\t\t\t\t\t:style=\"{borderColor:(detail.pay_type > item.pay_type -1) && detail.pay_type != 8?primaryColor:''}\">\r\n\t\t\t\t\t\t<i class=\"iconfont\" :class=\"item.icon\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"ellipsis\" style=\"max-width:100%\">{{item.title}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view class=\"item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16\" v-if=\"detail.pay_type == 6\">\r\n\t\t\t<view class=\"flex-y-center\">\r\n\t\t\t\t服务还有<min-countdown :type=\"2\" color=\"#ea3626\" bgColor=\"#ea3626\"\r\n\t\t\t\t\t:targetTime=\"detail.start_service_time_unix*1000\" @callback=\"countEnd\"></min-countdown>\r\n\t\t\t\t<view class=\"ml-sm\">结束</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view class=\"item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16\">\r\n\t\t\t<view class=\"flex-between pb-lg\">\r\n\t\t\t\t<view class=\"f-paragraph c-title max-380 ellipsis\">服务内容</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp\" :class=\"[{'mb-lg':aindex != detail.order_goods.length -1}]\"\r\n\t\t\t\tv-for=\"(aitem,aindex) in detail.order_goods\" :key=\"aindex\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"avatar lg radius-16\">\r\n\t\t\t\t\t<view class=\"h5-image avatar lg radius-16\"\r\n\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${aitem.goods_cover}')`}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<image mode=\"aspectFill\" class=\"avatar lg radius-16\" :src=\"aitem.goods_cover\"></image>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t<view class=\"f-mini-title c-title text-bold max-380 ellipsis\">\r\n\t\t\t\t\t\t\t{{aitem.goods_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"c-paragraph\">x{{aitem.num}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f-caption mt-sm\" style=\"color:#777\" v-if=\"aitem.init_material_price*1>0\">\r\n\t\t\t\t\t\t物料费：¥{{aitem.init_material_price}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between mt-sm\">\r\n\t\t\t\t\t\t<view class=\"f-caption\" style=\"color:#777\">服务时长：{{aitem.time_long}}分钟</view>\r\n\t\t\t\t\t\t<view class=\"f-caption c-warning\" v-if=\"aitem.refund_num>0\">已退x{{aitem.refund_num}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f-paragraph c-warning text-bold mt-sm\">¥{{aitem.price}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"store-info mt-md ml-lg mr-lg pd-lg  fill-base radius-16\" v-if=\"detail.store_id\">\r\n\t\t\t<view class=\"f-mini-title c-title text-bold pb-md\">\r\n\t\t\t\t{{detail.store_info.title}}\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t<view class=\"flex-y-center\" style=\"color: #303030;\">\r\n\t\t\t\t\t<i class=\"iconfont icondizhi1 mr-sm\"></i>\r\n\t\t\t\t\t<view class=\"c-title flex-1 mr-md\">\r\n\t\t\t\t\t\t<span>{{detail.store_info.address || `暂未设置门店地址`}}</span>\r\n\t\t\t\t\t\t<span @tap.stop=\"toCopy(detail.store_info.address)\"\r\n\t\t\t\t\t\t\tclass=\"copy-btn span f-icontext radius-5 ml-sm\"\r\n\t\t\t\t\t\t\t:style=\"{color:primaryColor,borderColor:primaryColor}\"\r\n\t\t\t\t\t\t\tv-if=\"detail.store_info.address\">复制</span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t<view @tap.stop=\"$util.goUrl({url:detail.store_info.phone,openType:'call'})\"\r\n\t\t\t\t\t\tclass=\"item-icon rel flex-center radius-16\">\r\n\t\t\t\t\t\t<view class=\"item-icon radius-16 abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont icondadianhua_1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap.stop=\"toMap('store_info')\" class=\"item-icon rel flex-center radius-16 ml-md\"\r\n\t\t\t\t\t\tv-if=\"detail.store_info.address\">\r\n\t\t\t\t\t\t<view class=\"item-icon radius-16 abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont icondizhi_1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"order-agent-info store-info mt-md ml-lg mr-lg pd-lg fill-base f-paragraph radius-16\">\r\n\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t<image mode=\"aspectFill\" class=\"coach-img radius\"\r\n\t\t\t\t\t:src=\"detail.coach_info ? detail.coach_info.work_img : `https://lbqny.migugu.com/admin/farm/default-user.png`\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<view class=\"flex-1 ml-lg f-title text-bold\"\r\n\t\t\t\t\t:class=\"[{'flex-between':detail.coach_info&&detail.coach_info.mobile}]\" style=\"max-width: 506rpx;\">\r\n\t\t\t\t\t<view class=\"max-400 ellipsis\">{{detail.coach_info ? detail.coach_info.coach_name : '-'}}</view>\r\n\t\t\t\t\t<view @tap.stop=\"$util.goUrl({url:detail.coach_info.mobile,openType:`call`})\"\r\n\t\t\t\t\t\tclass=\"item-icon rel flex-center radius-16\"\r\n\t\t\t\t\t\tv-if=\"detail.coach_info && detail.coach_info.mobile\">\r\n\t\t\t\t\t\t<view class=\"item-icon radius-16 abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont icondadianhua_1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\" v-if=\"detail.admin_name\">\r\n\t\t\t\t<view class=\"title\">代理商</view>\r\n\t\t\t\t<view class=\"text flex-1 ellipsis\">{{detail.admin_name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">下单人</view>\r\n\t\t\t\t<view class=\"text flex-1 flex-warp\">\r\n\t\t\t\t\t<view class=\"ellipsis\"\r\n\t\t\t\t\t\t:class=\"[{'max-400':!detail.pay_order_times},{'max-250':detail.pay_order_times}]\">\r\n\t\t\t\t\t\t{{detail.address_info.user_name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"c-warning\" v-if=\"detail.pay_order_times\">{{detail.pay_order_times | handlePayOrder}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">联系方式</view>\r\n\t\t\t\t<view class=\"text flex-1 flex-between\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<block v-if=\"!detail.phone_encryption\">\r\n\t\t\t\t\t\t\t{{detail.address_info.mobile.substring(0,3)}}****{{detail.address_info.mobile.substring(7,11)}}\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-else>{{detail.address_info.mobile}}</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap.stop=\"toTel\" class=\"item-icon rel flex-center radius-16\">\r\n\t\t\t\t\t\t<view class=\"item-icon radius-16 abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont icondadianhua_1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp mt-lg\" v-if=\"!detail.store_id\">\r\n\t\t\t\t<view class=\"title\">服务地址</view>\r\n\t\t\t\t<view class=\"text flex-1 flex-between\">\r\n\t\t\t\t\t<view style=\"max-width: 350rpx;\">\r\n\t\t\t\t\t\t<span>{{`${detail.address_info.address}${detail.address_info.address_info}`}}</span>\r\n\t\t\t\t\t\t<span @tap=\"toCopy(`${detail.address_info.address}${detail.address_info.address_info}`)\"\r\n\t\t\t\t\t\t\tclass=\"copy-btn fill radius-5 f-icontext ml-sm\">复制</span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap.stop=\"toMap('address_info')\" class=\"item-icon rel flex-center radius-16\">\r\n\t\t\t\t\t\t<view class=\"item-icon radius-16 abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont icondizhi_1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"mt-lg\" v-if=\"detail.text\">\r\n\t\t\t\t<view class=\"title\">订单备注</view>\r\n\t\t\t\t<view class=\"text mt-sm\">\r\n\t\t\t\t\t<text decode=\"emsp\" style=\"word-break:break-all;\">{{detail.text}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg pt-lg b-1px-t\">\r\n\t\t\t\t<view class=\"title\">下单时间</view>\r\n\t\t\t\t<view class=\"text flex-1\">{{detail.create_time}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">服务时间</view>\r\n\t\t\t\t<view class=\"text flex-1\">{{detail.start_time}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">服务时长</view>\r\n\t\t\t\t<view class=\"text flex-1\">{{detail.time_long}}分钟</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"!detail.is_add && !detail.store_id\">\r\n\t\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t\t<view class=\"title\">车费详情</view>\r\n\t\t\t\t\t<view class=\"text flex-1 flex-y-center\">{{carType[detail.car_type]}}\r\n\t\t\t\t\t\t<view class=\"ml-md\" v-if=\"detail.car_type == 1\">全程{{detail.distance}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-center mt-lg\" v-if=\"detail.car_type == 1\">\r\n\t\t\t\t\t<view class=\"title\">出行费用</view>\r\n\t\t\t\t\t<view class=\"text flex-1 flex-warp\">\r\n\t\t\t\t\t\t¥{{detail.car_price}}\r\n\t\t\t\t\t\t<view class=\"ml-md c-warning\" v-if=\"detail.free_fare\">\r\n\t\t\t\t\t\t\t{{detail.free_fare==1?'平台补贴':$t('action.attendantName')+'补贴'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">服务项目费用</view>\r\n\t\t\t\t<view class=\"text flex-1\">¥{{detail.init_service_price}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-md\" v-if=\"detail.init_material_price*1>0\">\r\n\t\t\t\t<view class=\"title\">物料费</view>\r\n\t\t\t\t<view class=\"text flex-1\">¥{{detail.init_material_price}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\" v-if=\"detail.discount*1 > 0\">\r\n\t\t\t\t<view class=\"title\">卡券优惠</view>\r\n\t\t\t\t<view class=\"text c-warning flex-1\">-¥{{detail.discount}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mt-lg\">\r\n\t\t\t\t<view class=\"title\">支付方式</view>\r\n\t\t\t\t<view class=\"text flex-1\">{{payType[detail.pay_model]}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between mt-lg pt-lg b-1px-t\">\r\n\t\t\t\t<view class=\"title\"></view>\r\n\t\t\t\t<view class=\"flex-y-baseline f-paragraph c-black text-bold\">总计：<view class=\"c-warning\">\r\n\t\t\t\t\t\t¥{{detail.pay_price}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mt-md ml-lg mr-lg pd-lg fill-base radius-16\">\r\n\t\t\t<view class=\"flex-y-center pb-lg f-mini-title c-title flex-warp b-1px-b\">\r\n\t\t\t\t<view class=\"flex-between text-bold\">订单编号：</view>\r\n\t\t\t\t<view class=\"flex-between flex-1 \">\r\n\t\t\t\t\t<view class=\"text-bold max-350 ellipsis\">{{detail.order_code}}</view>\r\n\t\t\t\t\t<view class=\"copy-btn flex-center radius-5 f-icontext\" @tap.stop=\"toCopy(detail.order_code)\"\r\n\t\t\t\t\t\t:style=\"{borderColor:primaryColor ,color:primaryColor}\">复制</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"space-lg\"></view>\r\n\t\t\t<timeline :list=\"lineList\" :info=\"detail\"></timeline>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"space-max-footer\"></view>\r\n\t\t<view class=\"footer-info fix fill-base\"\r\n\t\t\tv-if=\"([2, 3, 4, 5, 6, 8].includes(detail.pay_type) && !detail.is_add) || ((([2, 3, 6].includes(detail.pay_type) && detail.add_flow_path === 1) || (detail.pay_type==2&&detail.add_flow_path==2)) && detail.is_add)\">\r\n\t\t\t<!-- v-if=\"detail.admin_apply_refund || ([2, 3, 4, 5, 6, 8].includes(detail.pay_type) && !detail.is_add) || ((([2, 3, 6].includes(detail.pay_type) && detail.add_flow_path === 1) || (detail.pay_type==2&&detail.add_flow_path==2)) && detail.is_add)\"> -->\r\n\r\n\t\t\t<view class=\"flex-between pd-lg\">\r\n\t\t\t\t<view></view>\r\n\t\t\t\t<view class=\"flex-center f-desc c-title\">\r\n\t\t\t\t\t<!-- \t<view\r\n\t\t\t\t\t\**********=\"$util.goUrl({url:`/agent/pages/order/refund?id=${options.id}&agent=${options.agent}`})\"\r\n\t\t\t\t\t\tclass=\"item-btn flex-center ml-md radius\" v-if=\"detail.admin_apply_refund\">\r\n\t\t\t\t\t\t退款\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<block\r\n\t\t\t\t\t\tv-if=\"([2, 3, 4, 5, 6, 8].includes(detail.pay_type) && !detail.is_add) || ((([2, 3, 6].includes(detail.pay_type) && detail.add_flow_path === 1) || (detail.pay_type==2&&detail.add_flow_path==2)) && detail.is_add)\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\**********=\"$util.goUrl({url:`/agent/pages/order/change?id=${options.id}&agent=${options.agent}`})\"\r\n\t\t\t\t\t\t\tclass=\"item-btn flex-center ml-md radius\" v-if=\"!detail.is_add\">\r\n\t\t\t\t\t\t\t{{$t('action.transferOrder')}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @tap.stop=\"toConfirm\" class=\"item-btn flex-center ml-md c-base radius\"\r\n\t\t\t\t\t\t\t:style=\"{background:primaryColor}\">\r\n\t\t\t\t\t\t\t{{$t( `action.${ technicianStatusOperType[ (detail.pay_type === 3 &&(detail.store_id || detail.is_add)) ? 5  : detail.pay_type == 8 ? -1 : detail.pay_type ]}`) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"space-safe\"></view>\r\n\t\t</view>\r\n\r\n\t\t<uni-popup ref=\"refuse_item\" type=\"center\" :custom=\"true\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">拒单原因</view>\r\n\t\t\t\t<scroll-view scroll-y @touchmove.stop.prevent class=\"refund-text mt-lg\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text decode=\"emsp\"\r\n\t\t\t\t\t\t\tstyle=\"word-break:break-all;\">{{detail.coach_refund_text || '没有填写原因哦'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-md f-caption c-caption\">拒单时间：{{detail.coach_refund_time}}</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.refuse_item.close()\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\r\n\t\t<uni-popup ref=\"change_item\" type=\"center\" :custom=\"true\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">温馨提示</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t你确认要操作{{$t(`action.${technicianStatusOperType[popupInfo.text_type]}`)}}吗?\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f-caption c-warning\" v-if=\"popupInfo.type == -1\">\r\n\t\t\t\t\t退款金额：¥{{popupInfo.can_refund_price}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.change_item.close()\" class=\"item-child\">取消</view>\r\n\t\t\t\t\t<view @tap.stop=\"confirmChangeOrder\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\timport timeline from '@/components/timeline.vue'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttimeline\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\toptions: {},\r\n\t\t\t\tstatusType: {\r\n\t\t\t\t\t'-1': '已取消',\r\n\t\t\t\t\t1: '待支付',\r\n\t\t\t\t\t2: '待接单',\r\n\t\t\t\t\t3: '已接单',\r\n\t\t\t\t\t4: '已出发',\r\n\t\t\t\t\t5: '已到达',\r\n\t\t\t\t\t6: '服务中',\r\n\t\t\t\t\t7: '已完成',\r\n\t\t\t\t\t8: '待转单'\r\n\t\t\t\t},\r\n\t\t\t\ttechnicianStatusOperType: {\r\n\t\t\t\t\t'-1': 'agreeRefund',\r\n\t\t\t\t\t2: 'orderTaking',\r\n\t\t\t\t\t3: 'setOut',\r\n\t\t\t\t\t4: 'arrive',\r\n\t\t\t\t\t5: 'startService',\r\n\t\t\t\t\t6: 'serviceCompletion'\r\n\t\t\t\t},\r\n\t\t\t\tcarType: {\r\n\t\t\t\t\t0: '公交/地铁',\r\n\t\t\t\t\t1: '出租车'\r\n\t\t\t\t},\r\n\t\t\t\tpayType: {\r\n\t\t\t\t\t1: '微信支付',\r\n\t\t\t\t\t2: '余额支付',\r\n\t\t\t\t\t3: '支付宝支付'\r\n\t\t\t\t},\r\n\t\t\t\tlineList: [],\r\n\t\t\t\tbase_service: [{\r\n\t\t\t\t\tpay_type: 3,\r\n\t\t\t\t\ttitle: this.$t('action.attendantName') + '接单',\r\n\t\t\t\t\ttime: 'receiving_time',\r\n\t\t\t\t\ticon: 'iconjishijiedan'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 4,\r\n\t\t\t\t\ttitle: this.$t('action.attendantName') + '出发',\r\n\t\t\t\t\ttime: 'serout_time',\r\n\t\t\t\t\taddr: 'serout_address',\r\n\t\t\t\t\ticon: 'iconjishichufa'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 5,\r\n\t\t\t\t\ttitle: this.$t('action.attendantName') + '到达',\r\n\t\t\t\t\ttime: 'arrive_time',\r\n\t\t\t\t\timg: 'arrive_img',\r\n\t\t\t\t\taddr: 'arr_address',\r\n\t\t\t\t\ticon: 'iconjishidaoda'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 6,\r\n\t\t\t\t\ttitle: '开始服务',\r\n\t\t\t\t\ttime: 'start_service_time',\r\n\t\t\t\t\ticon: 'iconjishifuwu'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 7,\r\n\t\t\t\t\ttitle: '服务完成',\r\n\t\t\t\t\ttime: 'order_end_time',\r\n\t\t\t\t\timg: 'end_img',\r\n\t\t\t\t\taddr: 'end_address',\r\n\t\t\t\t\ticon: 'iconjishiwancheng'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 7,\r\n\t\t\t\t\ttitle: '签字确认',\r\n\t\t\t\t\ttime: 'sign_time',\r\n\t\t\t\t\timg: 'sign_img',\r\n\t\t\t\t\ticon: ''\r\n\t\t\t\t}],\r\n\t\t\t\tbase_bell: [{\r\n\t\t\t\t\tpay_type: 3,\r\n\t\t\t\t\ttitle: this.$t('action.attendantName') + '接单',\r\n\t\t\t\t\ttime: 'receiving_time',\r\n\t\t\t\t\ticon: 'iconjishijiedan'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 6,\r\n\t\t\t\t\ttitle: '开始服务',\r\n\t\t\t\t\ttime: 'start_service_time',\r\n\t\t\t\t\ticon: 'iconjishifuwu'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpay_type: 7,\r\n\t\t\t\t\ttitle: '服务完成',\r\n\t\t\t\t\ttime: 'order_end_time',\r\n\t\t\t\t\timg: 'end_img',\r\n\t\t\t\t\taddr: 'end_address',\r\n\t\t\t\t\ticon: 'iconjishiwancheng'\r\n\t\t\t\t}],\r\n\t\t\t\tdetail: {\r\n\t\t\t\t\tpay_type: 0\r\n\t\t\t\t},\r\n\t\t\t\tcheck_label: [],\r\n\t\t\t\tcoach_refund_text: '',\r\n\t\t\t\tlockTap: false,\r\n\t\t\t\tpopupInfo: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\ttype: '',\r\n\t\t\t\t\tparam: {},\r\n\t\t\t\t\timgs: [],\r\n\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\tlat: 0,\r\n\t\t\t\t\t\tlng: 0,\r\n\t\t\t\t\t\taddress: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\thaveOperItem: state => state.technician.haveOperItem,\r\n\t\t\tover_time_text() {\r\n\t\t\t\treturn new Date().getTime() + this.detail.end_time * 1000\r\n\t\t\t}\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tagent = 0\r\n\t\t\t} = options\r\n\t\t\toptions.agent = agent * 1\r\n\t\t\tthis.options = options\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (!this.haveOperItem) return\r\n\t\t\tthis.$util.back()\r\n\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\tval: false\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getCoachInfo']),\r\n\t\t\t...mapMutations(['updateTechnicianItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!this.configInfo.id || refresh) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid,\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tlet data = await this.$api[methodKey].orderInfo({\r\n\t\t\t\t\tid\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tdata.is_balance = data.balance * 1 > 0 ? 1 : 0\r\n\t\t\t\tlet {\r\n\t\t\t\t\tpay_type,\r\n\t\t\t\t\ttrue_time_long: time_long,\r\n\t\t\t\t\treceiving_time = '',\r\n\t\t\t\t\tstart_service_time = '',\r\n\t\t\t\t\tis_add = 0,\r\n\t\t\t\t\tstore_id = 0\r\n\t\t\t\t} = data\r\n\t\t\t\tif (is_add) {\r\n\t\t\t\t\tif (!receiving_time && pay_type > 3) {\r\n\t\t\t\t\t\tdata.receiving_time = start_service_time\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!start_service_time && pay_type > 5) {\r\n\t\t\t\t\t\tdata.start_service_time = receiving_time\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (pay_type == 6) {\r\n\t\t\t\t\tlet start_service_time_unix = this.$util.DateToUnix(start_service_time) + time_long * 60\r\n\t\t\t\t\tdata.start_service_time_unix = start_service_time_unix\r\n\t\t\t\t}\r\n\t\t\t\tlet lineList = this.$util.deepCopy(is_add || store_id ? this.base_bell : this.base_service)\r\n\t\t\t\tlet ind = lineList.findIndex(item => {\r\n\t\t\t\t\treturn item.title == '签字确认'\r\n\t\t\t\t})\r\n\t\t\t\tif (store_id && ind == -1) {\r\n\t\t\t\t\tlineList.push({\r\n\t\t\t\t\t\tpay_type: 7,\r\n\t\t\t\t\t\ttitle: '签字确认',\r\n\t\t\t\t\t\ttime: 'sign_time',\r\n\t\t\t\t\t\ticon: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.lineList = lineList\r\n\t\t\t\tthis.detail = data\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\tcountEnd() {\r\n\t\t\t\tthis.$util.log(\"倒计时完了\")\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.initRefresh()\r\n\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\t// type: -1拒绝接单，3确定接单，4已出发，5已到达，6开始服务，7已完成\r\n\t\t\tasync toConfirm() {\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: order_id,\r\n\t\t\t\t\tpay_type,\r\n\t\t\t\t\tis_add = 0,\r\n\t\t\t\t\tstore_id = 0,\r\n\t\t\t\t\tcan_refund_price,\r\n\t\t\t\t\tadd_flow_path = 1\r\n\t\t\t\t} = this.detail\r\n\t\t\t\tlet type = (is_add && pay_type === 2 && add_flow_path == 2) || (pay_type === 3 && (is_add ||\r\n\t\t\t\t\tstore_id)) ? 5 : pay_type == 8 ? -1 : pay_type\r\n\t\t\t\tthis.popupInfo = {\r\n\t\t\t\t\torder_id,\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\ttext_type: is_add && pay_type == 2 ? 2 : type,\r\n\t\t\t\t\tcan_refund_price\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.change_item.open()\r\n\t\t\t},\r\n\t\t\tasync confirmChangeOrder() {\r\n\t\t\t\tlet param = this.$util.deepCopy(this.popupInfo)\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\ttext_type\r\n\t\t\t\t} = param\r\n\t\t\t\tparam.type = type == -1 ? type : type + 1\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_add,\r\n\t\t\t\t\tadd_service = []\r\n\t\t\t\t} = this.detail\r\n\t\t\t\tif (param.type == 7 && !is_add && add_service.length > 0) {\r\n\t\t\t\t\tlet text = ``\r\n\t\t\t\t\tadd_service.map(item => {\r\n\t\t\t\t\t\ttext += `【${item}】`\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet [res_del, {\r\n\t\t\t\t\t\tconfirm\r\n\t\t\t\t\t}] = await uni.showModal({\r\n\t\t\t\t\t\tcontent: `该订单还有${text}${add_service.length}项加钟服务，完成主订单会将加钟订单一并结束，确认是否结束？`,\r\n\t\t\t\t\t\tconfirmText: `确认`\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (!confirm) {\r\n\t\t\t\t\t\tthis.$refs.change_item.close()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tdelete param.index\r\n\t\t\t\tdelete param.can_refund_price\r\n\t\t\t\tdelete param.text_type\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttechnicianStatusOperType\r\n\t\t\t\t} = this\r\n\t\t\t\t// let msg = text_type == -1 ? '退款成功' : this.$t(`action.${technicianStatusOperType[text_type]}`)\r\n\t\t\t\tlet msg = text_type == -1 ? '退款成功' : `操作成功`\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.$refs.change_item.close()\r\n\t\t\t\t\tawait this.$api[methodKey].adminUpdateOrder(param)\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: msg\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\t\tval: true\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.initRefresh()\r\n\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t\tif (type == -1) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\t\turl: 1,\r\n\t\t\t\t\t\t\t\topenType: 'navigateBack'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\t\tval: true\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.initRefresh()\r\n\t\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 咨询\r\n\t\t\tasync toTel() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: order_id,\r\n\t\t\t\t\tpay_type\r\n\t\t\t\t} = this.detail\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tif ([2, 3, 4, 5, 6, 8].includes(pay_type)) {\r\n\t\t\t\t\tlet url = await this.$api[methodKey].getVirtualPhone({\r\n\t\t\t\t\t\torder_id\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (!url) {\r\n\t\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\t\ttitle: `稍后会有电话打入，请注意接听哦`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\turl,\r\n\t\t\t\t\t\topenType: `call`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet msg = pay_type == 7 ? '服务结束' : '服务取消'\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `${msg}不能联系客户哦`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 查看定位\r\n\t\t\tasync toMap(key) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet {\r\n\t\t\t\t\taddress,\r\n\t\t\t\t\taddress_info = '',\r\n\t\t\t\t\tlat,\r\n\t\t\t\t\tlng\r\n\t\t\t\t} = this.detail[key]\r\n\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\ttype: 'userLocation'\r\n\t\t\t\t})\r\n\t\t\t\tawait uni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t})\r\n\t\t\t\tawait uni.openLocation({\r\n\t\t\t\t\tlatitude: lat * 1,\r\n\t\t\t\t\tlongitude: lng * 1,\r\n\t\t\t\t\tname: address_info ? `${address} ${address_info}` : address,\r\n\t\t\t\t\tscale: 28\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoCopy(url) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl,\r\n\t\t\t\t\topenType: 'copy'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\thandlePayOrder(num) {\r\n\t\t\t\treturn num === 1 ? '（首次下单）' : `（第${num}次下单）`\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.view-btn {\r\n\t\twidth: 102rpx;\r\n\t\theight: 52rpx;\r\n\t\tborder-radius: 8rpx\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110363478\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}