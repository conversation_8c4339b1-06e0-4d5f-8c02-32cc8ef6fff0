## 1.3.2（2022-06-16）
修复Tabbar窗口无法选择文件的问题
## 1.3.1（2022-06-15）
修复因上个版本更新取消强制cuWebview参数，导致传入该参数时直接传给了接口的问题
## 1.3.0（2022-05-19）
取消app端上传函数cuwebview参数，只需传入接口相关属性，另外在新版上传组件做了更多优化，建议需要文件选择上传的同学移步 全文件选择上传2.0版
## 1.2.9（2022-01-25）
QQ1群已满，已新增2群：469580165
## 1.2.8（2022-01-20）
修复小程序下载传入header无效问题
## 1.2.7（2021-11-02）
修复上一版本APP端额外参数获取不到的问题
## 1.2.6（2021-10-11）
新增addName、addSize、maxSize字段，使用方式看下方js备注
## 1.2.5（2021-08-26）
因部分伙伴找不到下载的文件在哪里，现更改插件保存文件默认路径为系统文件管理器目录第一层lFile目录
## 1.2.4（2021-04-14）
1.修复下载进度条未显示问题；
2.增加取消下载
## 1.2.3（2021-04-12）
修复H5端第二次打开文件管理器时未清除上一次加载dom的问题
## 1.2.2（2021-04-02）
1.已不再维护非uniModules版本，需要的伙伴请导入uniModules版本插件。
2.本次升级为修复OSS直传时签名无效问题，普通接口不受影响
## 1.2.1（2021-04-01）
已支持uniModules版本；不清楚怎么使用可以导入示例项目
## 1.2.0（2021-04-01）
1.修复h5端上传附件为空文件的BUG
2.统一APP端与H5端返回参数
## 1.1.6（2021-03-31）
如uni_modules版本无法导入插件，可导入非uni_modules版或完整示例
## 1.1.5（2021-03-31）
如uni_modules版本无法导入插件，可导入非uni_modules版或完整示例
## 1.1.4（2021-03-31）
附件选择上传已兼容App、微信小程序、H5
