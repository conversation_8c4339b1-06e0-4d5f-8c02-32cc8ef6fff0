{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?8856", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?c0d2", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?3e07", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?fbf1", "uni-app:///agent/pages/index.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?4ddb", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/index.vue?e850"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "isLoad", "options", "detail", "noticeType", "title", "text", "shopOrder", "icon", "url", "key", "number", "shopBellOrder", "shopRefuseOrder", "shopRefund", "shopBellRefund", "toolList", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "haveOperItem", "onLoad", "agent", "uni", "onShow", "onPullDownRefresh", "methods", "initIndex", "refresh", "bg", "val", "<PERSON><PERSON><PERSON>", "notice", "order_id", "refund_id", "refuse_id", "type", "arr", "item", "initRefresh", "toJump", "goOrder", "id", "have_look", "is_pop", "page"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAwxB,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyO5yB;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACA;UACAC;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;MACA;MACAC;QACAC;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;MACA;MACAG;QACAJ;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;MACA;MACAI;QACAL;QACAF;QACAG;QACAC;QACAC;MACA;MACAG;QACAN;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;MACAI;QACAP;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;QACAH;QACAF;QACAG;QACAC;QACAC;MACA;MACAK;QACAR;QACAF;QACAG;MACA;QACAD;QACAF;QACAG;MACA,GACA;QACAD;QACAF;QACAG;MACA,GACA;QACAD;QACAF;QACAG;MACA;IAEA;EACA;EACAQ;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA,qBAEArB,QADAsB;MAAAA;IAEAtB;IACA;IACAuB;MACApB;IACA;IACA;EACA;EACAqB;IACA;IACA;EACA;EACAC;IAEAF;IAEA;IACAA;EACA;EACAG,yCACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBASA;kBACAC;gBACA;gBACA;gBACA;kBACArB;kBACAsB;gBACA;gBAEAR,QACA,cADAA;gBAEAS;gBAAA;gBAAA,OACA;cAAA;gBAAAjC;gBACA;kBACAA;gBACA;gBAEAkC,SACAlC,KADAkC;gBAGAC,WAGAD,OAHAC,UACAC,YAEAF,OAFAE,WACAC,YACAH,OADAG;gBAEA;kBACAF;oBACAG;kBACA;gBACA;gBACA;kBACAF;oBACAE;kBACA;gBACA;gBACA;kBACAD;oBACAC;kBACA;gBACA;gBACAC;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACAvC;gBACA;kBACA;oBACAwC;kBACA;gBACA;gBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA,IACAjC,MACA,iBADAA;MAEA,IACAe,QACA,aADAA;MAEA;MACAf;MACA;QACAA;MACA;IACA;IACAkC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,wBAKA,4EAHAC,wHACAT,qFACAG;gBAAA,IAEAH;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAX,QACA,eADAA;gBAEAS;gBAAA;gBAAA,OACA;kBACAW;kBACAC;kBACAC;gBACA;cAAA;gBACA;gBACAC;gBACAtC,sHACAsC;gBACA;kBACAtC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACliBA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,26CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0384f09f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0384f09f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.isLoad && _vm.options.agent ? _vm.$t(\"action.attendantName\") : null\n  var m1 =\n    _vm.isLoad && _vm.options.agent ? _vm.$t(\"action.attendantName\") : null\n  var m2 =\n    _vm.isLoad && _vm.options.agent && _vm.detail.channel_auth\n      ? _vm.$t(\"action.channelName\")\n      : null\n  var g0 = _vm.isLoad ? _vm.detail.node.includes(\"shopOrder\") : null\n  var g1 = _vm.isLoad ? _vm.detail.node.includes(\"shopBellOrder\") : null\n  var g2 = _vm.isLoad ? _vm.detail.node.includes(\"shopRefuseOrder\") : null\n  var g3 = _vm.isLoad ? _vm.detail.node.includes(\"shopRefund\") : null\n  var g4 = _vm.isLoad ? _vm.detail.node.includes(\"shopBellRefund\") : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/user/pages/cash-out?type=agent\",\n      })\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.show_rule_item.open()\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/poster/technician\",\n      })\n    }\n    _vm.e3 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/poster/salesman\",\n      })\n    }\n    _vm.e4 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/poster/channel\",\n      })\n    }\n    _vm.e5 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/poster/distribution\",\n      })\n    }\n    _vm.e6 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.show_rule_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"agent-index\" v-if=\"isLoad\">\r\n\t\t<block v-if=\"options.agent\">\r\n\t\t\t<view class=\"mine-count-list c-base pd-lg radius-16\" :style=\"{background:primaryColor}\">\r\n\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"text f-caption\">可提现金额</view>\r\n\t\t\t\t\t\t<view class=\"f-sm-title\">¥{{detail.cash}} </view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @tap.stop=\"$util.goUrl({url:`/user/pages/cash-out?type=agent`})\"\r\n\t\t\t\t\t\tclass=\"cash-out-btn fill-base flex-center f-desc text-bold radius\"\r\n\t\t\t\t\t\t:style=\"{color: primaryColor}\">\r\n\t\t\t\t\t\t我要提现\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"count-data-list flex-x-center mt-lg pt-lg b-1px-t\">\r\n\t\t\t\t\t<view class=\"list-item flex-center flex-column\">\r\n\t\t\t\t\t\t<view class=\"f-sm-title\">¥{{detail.total_cash}}</view>\r\n\t\t\t\t\t\t<view class=\"text f-caption c-caption\">总金额 (不含手续费)</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"list-item flex-center flex-column\">\r\n\t\t\t\t\t\t<view class=\"f-sm-title\">¥{{detail.unrecorded_cash}}</view>\r\n\t\t\t\t\t\t<view @tap.stop=\"$refs.show_rule_item.open()\" class=\"text flex-center f-caption c-caption\">未入账<i\r\n\t\t\t\t\t\t\t\tclass=\"iconfont iconwentifankui3 ml-sm\" style=\"font-size: 28rpx;\"></i></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view @tap.stop=\"$util.goUrl({url:`/agent/pages/poster/technician`})\"\r\n\t\t\t\tclass=\"mine-menu-list share flex-center fill-base radius-16\">\r\n\t\t\t\t<view class=\"icon-info flex-center\">\r\n\t\t\t\t\t<i class=\"iconfont iconjishi1\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"f-mini-title c-title text-bold\">绑定{{$t('action.attendantName')}}</view>\r\n\t\t\t\t\t<view class=\"f-desc c-caption mt-sm ellipsis\">整合自己的{{$t('action.attendantName')}}资源,获取分润</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share-btn flex-center f-desc c-base text-bold\" :style=\"{background:primaryColor}\">邀请Ta\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap.stop=\"$util.goUrl({url:`/agent/pages/poster/salesman`})\"\r\n\t\t\t\tclass=\"mine-menu-list share flex-center fill-base radius-16\" v-if=\"detail.salesman_auth\">\r\n\t\t\t\t<view class=\"icon-info flex-center\">\r\n\t\t\t\t\t<i class=\"iconfont iconbangdingyewuyuan\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"f-mini-title c-title text-bold\">绑定业务员</view>\r\n\t\t\t\t\t<view class=\"f-desc c-caption mt-sm ellipsis\">招揽人才,为自己拓宽渠道</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share-btn flex-center f-desc c-base text-bold\" :style=\"{background:primaryColor}\">邀请Ta\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap.stop=\"$util.goUrl({url:`/agent/pages/poster/channel`})\"\r\n\t\t\t\tclass=\"mine-menu-list share flex-center fill-base radius-16\" v-if=\"detail.channel_auth\">\r\n\t\t\t\t<view class=\"icon-info flex-center\">\r\n\t\t\t\t\t<i class=\"iconfont iconbangdingqudaoshang\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"f-mini-title c-title text-bold\">绑定{{$t('action.channelName')}}</view>\r\n\t\t\t\t\t<view class=\"f-desc c-caption mt-sm ellipsis\">直招渠道,获利更多</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share-btn flex-center f-desc c-base text-bold\" :style=\"{background:primaryColor}\">邀请Ta\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap.stop=\"$util.goUrl({url:`/agent/pages/poster/distribution`})\"\r\n\t\t\t\tclass=\"mine-menu-list share flex-center fill-base radius-16\" v-if=\"detail.reseller_auth\">\r\n\t\t\t\t<view class=\"icon-info flex-center\">\r\n\t\t\t\t\t<i class=\"iconfont iconbangdingfenxiaoyuan\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"f-mini-title c-title text-bold\">绑定分销员</view>\r\n\t\t\t\t\t<view class=\"f-desc c-caption mt-sm ellipsis\">让专业的人帮你拉新用户</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"share-btn flex-center f-desc c-base text-bold\" :style=\"{background:primaryColor}\">邀请Ta\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\r\n\t\t<view class=\"mine-menu-list fill-base radius-16\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">智能助手</view>\r\n\t\t\t</view>\r\n\t\t\t<view @tap.stop=\"goOrder(index)\" class=\"notice-info flex-center pl-lg pr-md pb-lg\"\r\n\t\t\t\tv-for=\"(item,index) in detail.notice\" :key=\"index\">\r\n\t\t\t\t<view class=\"title flex-center f-icontext c-base mr-md\"\r\n\t\t\t\t\t:style=\"{background:item.type==1?primaryColor: item.type==2?'#FFA229':'#E82F21'}\">\r\n\t\t\t\t\t{{noticeType[item.type].title}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-1 flex-between f-desc\">\r\n\t\t\t\t\t<view class=\"c-title\">{{item.id ? noticeType[item.type].text : '暂无数据'}}</view>\r\n\t\t\t\t\t<view class=\"flex-y-center c-caption\" v-if=\"item.id\">点击查看<i class=\"iconfont icon-right\"></i></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mine-menu-list box-shadow fill-base radius-16\" v-if=\"detail.node.includes('shopOrder')\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">订单管理</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-lg\">\r\n\t\t\t\t<view @tap.stop=\"toJump('shopOrder', index)\"\r\n\t\t\t\t\tclass=\"item-child flex-center flex-column f-caption c-title\" v-for=\"(item, index) in shopOrder\"\r\n\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t<view class=\"item-img rel flex-center radius\">\r\n\t\t\t\t\t\t<view class=\"abs dot-unread-number flex-center\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number>99 ? '44rpx': item.number > 9 ? '34rpx' :'',right: item.number>99 ? '-32rpx': item.number > 9 ? '-22rpx' :'-12rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"item.number > 0\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-img radius abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont c-title\" :class=\"item.icon\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mine-menu-list box-shadow fill-base radius-16\" v-if=\"detail.node.includes('shopBellOrder')\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">加钟管理</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-lg\">\r\n\t\t\t\t<view @tap.stop=\"toJump('shopBellOrder', index)\"\r\n\t\t\t\t\tclass=\"item-child flex-center flex-column f-caption c-title\" v-for=\"(item, index) in shopBellOrder\"\r\n\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t<view class=\"item-img rel flex-center radius\">\r\n\t\t\t\t\t\t<view class=\"abs dot-unread-number flex-center\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number>99 ? '44rpx': item.number > 9 ? '34rpx' :'',right: item.number>99 ? '-32rpx': item.number > 9 ? '-22rpx' :'-12rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"item.number > 0\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-img radius abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<image mode=\"heightFix\" class=\"icon-img\" :src=\"item.icon\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mine-menu-list box-shadow fill-base radius-16\" v-if=\"detail.node.includes('shopRefuseOrder')\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">拒单管理</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-lg\">\r\n\t\t\t\t<view @tap.stop=\"toJump('shopRefuseOrder', index)\"\r\n\t\t\t\t\tclass=\"item-child flex-center flex-column f-caption c-title\"\r\n\t\t\t\t\tv-for=\"(item, index) in shopRefuseOrder\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"item-img rel flex-center radius\">\r\n\t\t\t\t\t\t<view class=\"abs dot-unread-number flex-center\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number>99 ? '44rpx': item.number > 9 ? '34rpx' :'',right: item.number>99 ? '-32rpx': item.number > 9 ? '-22rpx' :'-12rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"item.number > 0\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-img radius abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont c-title\" :class=\"item.icon\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mine-menu-list box-shadow fill-base radius-16\" v-if=\"detail.node.includes('shopRefund')\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">服务退款</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-lg\">\r\n\t\t\t\t<view @tap.stop=\"toJump('shopRefund', index)\"\r\n\t\t\t\t\tclass=\"item-child flex-center flex-column f-caption c-title\" v-for=\"(item, index) in shopRefund\"\r\n\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t<view class=\"item-img rel flex-center radius\">\r\n\t\t\t\t\t\t<view class=\"abs dot-unread-number flex-center\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number>99 ? '44rpx': item.number > 9 ? '34rpx' :'',right: item.number>99 ? '-32rpx': item.number > 9 ? '-22rpx' :'-12rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"item.number > 0\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-img radius abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<i class=\"iconfont c-title\" :class=\"item.icon\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mine-menu-list box-shadow fill-base radius-16\" v-if=\"detail.node.includes('shopBellRefund')\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">加钟退款</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-lg\">\r\n\t\t\t\t<view @tap.stop=\"toJump('shopBellRefund', index)\"\r\n\t\t\t\t\tclass=\"item-child flex-center flex-column f-caption c-title\" v-for=\"(item, index) in shopBellRefund\"\r\n\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t<view class=\"item-img rel flex-center radius\">\r\n\t\t\t\t\t\t<view class=\"abs dot-unread-number flex-center\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number>99 ? '44rpx': item.number > 9 ? '34rpx' :'',right: item.number>99 ? '-32rpx': item.number > 9 ? '-22rpx' :'-12rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"item.number > 0\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item-img radius abs\" :style=\"{background:primaryColor}\"></view>\r\n\t\t\t\t\t\t<image mode=\"heightFix\" class=\"icon-img\" :src=\"item.icon\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view class=\"mine-menu-list fill-base radius-16\" v-if=\"options.agent\">\r\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-sm\">\r\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">其他功能</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-warp pb-sm\">\r\n\t\t\t\t<view @tap.stop=\"toJump('toolList', index)\" class=\"item-child flex-center flex-column f-caption c-title\"\r\n\t\t\t\t\tstyle=\"width: 25%;margin:10rpx 0 20rpx 0\" v-for=\"(item, index) in toolList\" :key=\"index\">\r\n\t\t\t\t\t<i class=\"iconfont c-title\" :class=\"item.icon\" :style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"space-footer\"></view>\r\n\r\n\r\n\t\t<uni-popup ref=\"show_rule_item\" type=\"center\" :maskClick=\"false\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">未入账</view>\r\n\t\t\t\t<view class=\"f-desc c-title mt-lg\">\r\n\t\t\t\t\t平台未到账的服务订单金额，不含手续费\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.show_rule_item.close()\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\timport tabbar from \"@/components/tabbar.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttabbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tdetail: {},\r\n\t\t\t\tnoticeType: {\r\n\t\t\t\t\t1: {\r\n\t\t\t\t\t\ttitle: '订单通知',\r\n\t\t\t\t\t\ttext: '您有新的订单来啦!'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t2: {\r\n\t\t\t\t\t\ttitle: '退款通知',\r\n\t\t\t\t\t\ttext: '您有新的客户退款通知!'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t3: {\r\n\t\t\t\t\t\ttitle: '拒单通知',\r\n\t\t\t\t\t\ttext: '有' + this.$t('action.attendantName') + '拒单, 请尽快处理!'\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t\tshopOrder: [{\r\n\t\t\t\t\ticon: 'icondaifuwu4',\r\n\t\t\t\t\ttext: '待接单',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=2',\r\n\t\t\t\t\tkey: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconjishijiedan1',\r\n\t\t\t\t\ttext: '已接单',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=3',\r\n\t\t\t\t\tkey: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconjishichufa1',\r\n\t\t\t\t\ttext: '已出发',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=4',\r\n\t\t\t\t\tkey: 4,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconjishidaoda1',\r\n\t\t\t\t\ttext: '已到达',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=5',\r\n\t\t\t\t\tkey: 5,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconanmo2',\r\n\t\t\t\t\ttext: '服务中',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=6',\r\n\t\t\t\t\tkey: 6,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconyiwancheng',\r\n\t\t\t\t\ttext: '已完成',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=7'\r\n\t\t\t\t}],\r\n\t\t\t\tshopBellOrder: [{\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/daifuwu.png',\r\n\t\t\t\t\ttext: '待接单',\r\n\t\t\t\t\turl: '/agent/pages/order/list?bell=1&tab=2',\r\n\t\t\t\t\tkey: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/jiedan.png',\r\n\t\t\t\t\ttext: '已接单',\r\n\t\t\t\t\turl: '/agent/pages/order/list?bell=1&tab=3',\r\n\t\t\t\t\tkey: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/fuwuzhong.png',\r\n\t\t\t\t\ttext: '服务中',\r\n\t\t\t\t\turl: '/agent/pages/order/list?bell=1&tab=4',\r\n\t\t\t\t\tkey: 6,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/wancheng.png',\r\n\t\t\t\t\ttext: '已完成',\r\n\t\t\t\t\turl: '/agent/pages/order/list?bell=1&tab=5',\r\n\t\t\t\t}],\r\n\t\t\t\tshopRefuseOrder: [{\r\n\t\t\t\t\ticon: 'icondaizhuandan',\r\n\t\t\t\t\ttext: '待转单',\r\n\t\t\t\t\turl: '/agent/pages/order/list?tab=8',\r\n\t\t\t\t\tkey: 8,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\tshopRefund: [{\r\n\t\t\t\t\ticon: 'iconshenqingzhong',\r\n\t\t\t\t\ttext: '待退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?tab=1',\r\n\t\t\t\t\tkey: 1,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icontongyituikuan',\r\n\t\t\t\t\ttext: '同意退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?tab=2',\r\n\t\t\t\t\tkey: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'iconjujuetuikuan',\r\n\t\t\t\t\ttext: '拒绝退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?tab=3',\r\n\t\t\t\t\tkey: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\tshopBellRefund: [{\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/shenqingzhong.png',\r\n\t\t\t\t\ttext: '待退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?bell=1&tab=1',\r\n\t\t\t\t\tkey: 1,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/tongyi.png',\r\n\t\t\t\t\ttext: '同意退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?bell=1&tab=2',\r\n\t\t\t\t\tkey: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'https://lbqny.migugu.com/admin/anmo/menu/jujue.png',\r\n\t\t\t\t\ttext: '拒绝退款',\r\n\t\t\t\t\turl: '/agent/pages/refund/list?bell=1&tab=3',\r\n\t\t\t\t\tkey: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\ttoolList: [{\r\n\t\t\t\t\t\ticon: 'iconzhanghaoshezhi',\r\n\t\t\t\t\t\ttext: '账号设置',\r\n\t\t\t\t\t\turl: '/agent/pages/account'\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ticon: 'iconshenqingjishi3',\r\n\t\t\t\t\t\ttext: this.$t('action.attendantName') + '管理',\r\n\t\t\t\t\t\turl: '/agent/pages/technician/list'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: 'iconyongjinxinxi1',\r\n\t\t\t\t\t\ttext: '佣金信息',\r\n\t\t\t\t\t\turl: '/agent/pages/income/commission'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: 'icontixianshenqing1',\r\n\t\t\t\t\t\ttext: '提现申请',\r\n\t\t\t\t\t\turl: '/user/pages/distribution/record?type=5'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\thaveOperItem: state => state.technician.haveOperItem,\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tagent = 0\r\n\t\t\t} = options\r\n\t\t\toptions.agent = agent * 1\r\n\t\t\tthis.options = options\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: agent == 1 ? '代理商端' : '管理员端'\r\n\t\t\t})\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (!this.haveOperItem) return\r\n\t\t\tthis.initRefresh()\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.showNavigationBarLoading()\r\n\t\t\t// #endif\r\n\t\t\tthis.initRefresh();\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapMutations(['updateTechnicianItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\tval: false\r\n\t\t\t\t})\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tlet data = await this.$api[methodKey].index()\r\n\t\t\t\tif (agent) {\r\n\t\t\t\t\tdata.node = ['shopOrder', 'shopBellOrder', 'shopRefuseOrder', 'shopRefund', 'shopBellRefund']\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tnotice\r\n\t\t\t\t} = data\r\n\t\t\t\tlet {\r\n\t\t\t\t\torder_id,\r\n\t\t\t\t\trefund_id,\r\n\t\t\t\t\trefuse_id\r\n\t\t\t\t} = notice\r\n\t\t\t\tif (order_id && order_id.length == 0) {\r\n\t\t\t\t\torder_id = {\r\n\t\t\t\t\t\ttype: 1\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (refund_id && refund_id.length == 0) {\r\n\t\t\t\t\trefund_id = {\r\n\t\t\t\t\t\ttype: 2\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (refuse_id && refuse_id.length == 0) {\r\n\t\t\t\t\trefuse_id = {\r\n\t\t\t\t\t\ttype: 3\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tif (data.node.includes('shopOrder') || data.node.includes('shopBellOrder')) {\r\n\t\t\t\t\tarr.push(order_id)\r\n\t\t\t\t}\r\n\t\t\t\tif (data.node.includes('shopRefund') || data.node.includes('shopBellRefund')) {\r\n\t\t\t\t\tarr.push(refund_id)\r\n\t\t\t\t}\r\n\t\t\t\tif (data.node.includes('shopRefuseOrder')) {\r\n\t\t\t\t\tarr.push(refuse_id)\r\n\t\t\t\t}\r\n\t\t\t\tdata.notice = arr\r\n\t\t\t\tthis.shopOrder.map(item => {\r\n\t\t\t\t\tif (item.key) {\r\n\t\t\t\t\t\titem.number = data.order_count[item.key]\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.shopBellOrder.map(item => {\r\n\t\t\t\t\tif (item.key) {\r\n\t\t\t\t\t\titem.number = data.add_count[item.key]\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.shopRefuseOrder.map(item => {\r\n\t\t\t\t\tif (item.key) {\r\n\t\t\t\t\t\titem.number = data.refuse_order[item.key]\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.shopRefund.map(item => {\r\n\t\t\t\t\tif (item.key) {\r\n\t\t\t\t\t\titem.number = data.refund_count[item.key]\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.shopBellRefund.map(item => {\r\n\t\t\t\t\tif (item.key) {\r\n\t\t\t\t\t\titem.number = data.add_refund_count[item.key]\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.detail = data\r\n\t\t\t\tthis.isLoad = true\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\ttoJump(key, index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\turl\r\n\t\t\t\t} = this[key][index]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet joinKey = url.includes('?') ? '&' : '?'\r\n\t\t\t\turl += `${joinKey}agent=${agent}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync goOrder(index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid = 0,\r\n\t\t\t\t\t\torder_id = 0,\r\n\t\t\t\t\t\ttype\r\n\t\t\t\t} = this.detail.notice[index]\r\n\t\t\t\tif (!order_id) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tawait this.$api[methodKey].noticeUpdate({\r\n\t\t\t\t\tid,\r\n\t\t\t\t\thave_look: 1,\r\n\t\t\t\t\tis_pop: 1\r\n\t\t\t\t})\r\n\t\t\t\tthis.initRefresh()\r\n\t\t\t\tlet page = type == 2 ? 'refund' : 'order'\r\n\t\t\t\tlet url = type === 3 ? `/agent/pages/order/change?id=${order_id}&agent=${agent}` :\r\n\t\t\t\t\t`/agent/pages/${page}/detail?id=${order_id}&agent=${agent}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.agent-index {\r\n\r\n\t\t.mine-count-list {\r\n\t\t\twidth: 710rpx;\r\n\t\t\theight: 295rpx;\r\n\t\t\tmargin: 20rpx 20rpx 0 20rpx;\r\n\r\n\t\t\t.text {\r\n\t\t\t\tcolor: rgba(255, 255, 255, 0.6)\r\n\t\t\t}\r\n\r\n\t\t\t.cash-out-btn {\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.count-data-list {\r\n\t\t\t\t.list-item {\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.dot-unread-number {\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\twidth: 24rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tline-height: 24rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 18rpx;\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tbackground-color: #F1381F;\r\n\t\t}\r\n\r\n\t\t// 我的订单/其他\r\n\t\t.mine-menu-list {\r\n\t\t\tmargin: 20rpx 20rpx 0 20rpx;\r\n\r\n\t\t\t.menu-title {\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tcolor: #434343;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.icon-info {\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tbox-shadow: 0 3rpx 31rpx -2rpx rgba(217, 224, 219, 0.5);\r\n\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.share-btn {\r\n\t\t\t\twidth: 130rpx;\r\n\t\t\t\theight: 52rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.notice-info {\r\n\t\t\t\t.title {\r\n\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.item-child {\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\tmargin: 10rpx 0;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 52rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.item-img {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.item-img {\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.icon-img {\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.mine-menu-list.share {\r\n\t\t\tpadding: 30rpx 30rpx 30rpx 20rpx;\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110360387\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}