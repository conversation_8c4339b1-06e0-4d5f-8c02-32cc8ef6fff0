{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?f944", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?4077", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?7a73", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?e6ae", "uni-app:///agent/pages/apply.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?618b", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/apply.vue?5205"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "isLoad", "options", "authTime", "timer", "is_send", "cityList", "id", "title", "form", "user_name", "city_type", "phone", "short_code", "city", "rule", "name", "checkType", "errorMsg", "regType", "lockTap", "computed", "primaryColor", "subColor", "configInfo", "mineInfo", "onLoad", "methods", "initIndex", "refresh", "Promise", "is_admin", "uni", "bg", "initRefresh", "toResetPhone", "toSend", "time", "clearInterval", "setTimeout", "validate", "item", "submit", "param", "msg", "short_code_status", "url", "openType"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAwxB,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoE5yB;AAIA;AAAA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAE;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC,uDACA,yEACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MASA,2DACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA,kBAIA,yDAFAC,2HACAnB;gBAEA;gBACA;gBACAoB;kBACAxB;gBACA;gBACA;kBACAyB;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAjC,WACA,OADAA;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,oBAGA,YADAS;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAJ;gBACA;gBAAA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBACAI;gBACA;cAAA;gBACA;gBACA;gBACA;gBACAyB;gBACA;kBACA;oBACAC;oBACA;kBACA;kBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;QACA,IACAxB,OACAyB,KADAzB;QAEAwB;MACA;MACA;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACApC;gBACA;gBAAA;cAAA;gBAIAqC,oBACA,kBADAA;gBAGAxC,UACA,OADAA;gBAAA,MAEAwC;kBAAA;kBAAA;gBAAA;gBACA;kBACArC;gBACA;gBAAA;cAAA;gBAGA;kBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACA8B;gBACA;gBACAC;kBACA;oBACA;kBACA;kBACA;oBACAO;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,26CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=9022c58a&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/apply.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=template&id=9022c58a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$util.goUrl({\n        url: \"/agent/pages/index?agent=1\",\n        openType: \"reLaunch\",\n      })\n    }\n    _vm.e1 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      $event.stopPropagation()\n      _vm.form.city_type = item.id\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"apply-pages\" v-if=\"isLoad\">\r\n\r\n\t\t<view class=\"page-height\" v-if=\"mineInfo.is_admin\">\r\n\t\t\t<abnor percent=\"150%\" @confirm=\"$util.goUrl({url:`/agent/pages/index?agent=1`,openType: `reLaunch`})\"\r\n\t\t\t\ttitle=\"您已经是代理商了\" :tip=\"[{ text: '快去管理订单吧', color: 0 }]\" :button=\"[{ text: '去管理', type: 'confirm' }]\"\r\n\t\t\t\timage=\"https://lbqny.migugu.com/admin/public/apply_suc.jpg\"></abnor>\r\n\t\t</view>\r\n\r\n\t\t<block v-else>\r\n\r\n\t\t\t<view class=\"apply-info-box rel\" :style=\"{background:primaryColor}\">\r\n\t\t\t\t<image class=\"bg-1 abs\" src=\"https://lbqny.migugu.com/admin/anmo/apply/bg-4.png\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<image class=\"bg-2 abs\" src=\"https://lbqny.migugu.com/admin/anmo/apply/bg-3.png\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<image class=\"join-us abs\" src=\"https://lbqny.migugu.com/admin/anmo/apply/join-us-1.png\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<view class=\"join-us pd-lg abs\" style=\"z-index: 3;\">\r\n\t\t\t\t\t<view style=\"height:90rpx\"></view>\r\n\t\t\t\t\t<view class=\"f-mini-title c-title text-bold\">您的姓名</view>\r\n\t\t\t\t\t<input v-model=\"form.user_name\" type=\"text\"\r\n\t\t\t\t\t\tclass=\"item-input fill-base f-mini-title c-title mt-sm pl-lg pr-lg radius-10\" maxlength=\"20\"\r\n\t\t\t\t\t\tplaceholder-class=\"c-caption\" :placeholder=\"rule[0].errorMsg\"\r\n\t\t\t\t\t\t:style=\"{borderColor:primaryColor}\" />\r\n\t\t\t\t\t<view class=\"mt-md f-mini-title c-title text-bold\">代理类型</view>\r\n\t\t\t\t\t<view class=\"flex-y-center mt-sm\">\r\n\t\t\t\t\t\t<view @tap.stop=\"form.city_type = item.id\" class=\"flex-y-center f-mini-title c-title\"\r\n\t\t\t\t\t\t\t:class=\"[{'mr-lg pr-lg':index!=2}]\"\r\n\t\t\t\t\t\t\t:style=\"{color:form.city_type == item.id ? primaryColor:''}\"\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in cityList\" :key=\"index\"><i class=\"iconfont mr-sm\"\r\n\t\t\t\t\t\t\t\t:class=\"[{'icon-xuanze':form.city_type != item.id},{'icon-radio-fill':form.city_type == item.id}]\"></i>{{item.title}}代理\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-md f-mini-title c-title text-bold\">手机号</view>\r\n\t\t\t\t\t<view class=\"item-input fill-base f-mini-title c-title mt-sm pl-lg pr-lg radius-10\"\r\n\t\t\t\t\t\tstyle=\"height:auto;min-height:82rpx\" :style=\"{borderColor:primaryColor}\">\r\n\t\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t\t<input :disabled=\"!is_send\" v-model=\"form.phone\" type=\"text\" maxlength=\"11\"\r\n\t\t\t\t\t\t\t\tplaceholder-class=\"c-caption\" :placeholder=\"rule[1].errorMsg\" />\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<view @tap.stop=\"toResetPhone\" class=\"f-caption\" :style=\"{color:primaryColor}\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"!is_send\">更换手机号</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-between\" v-if=\"configInfo.short_code_status && is_send\">\r\n\t\t\t\t\t\t\t<input v-model=\"form.short_code\" type=\"text\" maxlength=\"6\" placeholder-class=\"c-caption\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入验证码\" :style=\"{borderColor:primaryColor}\" />\r\n\t\t\t\t\t\t\t<view @tap.stop=\"toSend\" class=\"f-caption\" :style=\"{color:primaryColor}\">\r\n\t\t\t\t\t\t\t\t{{authTime>0?`重新获取(${authTime}s)`:'获取验证码'}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt-md f-mini-title c-title text-bold\">申请加入的区域</view>\r\n\t\t\t\t\t<input v-model=\"form.city\" type=\"text\"\r\n\t\t\t\t\t\tclass=\"item-input fill-base f-mini-title c-title mt-sm pl-lg pr-lg radius-10\" maxlength=\"50\"\r\n\t\t\t\t\t\tplaceholder-class=\"c-caption\" :placeholder=\"rule[2].errorMsg\"\r\n\t\t\t\t\t\t:style=\"{borderColor:primaryColor}\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<image @tap.stop=\"submit\" class=\"submit abs\"\r\n\t\t\t\t\tsrc=\"https://lbqny.migugu.com/admin/anmo/apply/submit-1.png\">\r\n\t\t\t\t</image>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tauthTime: 0,\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tis_send: true,\r\n\t\t\t\tcityList: [{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\ttitle: '省'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '城市'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\ttitle: '区县'\r\n\t\t\t\t}],\r\n\t\t\t\tform: {\r\n\t\t\t\t\tuser_name: '', //姓名 \r\n\t\t\t\t\tcity_type: 3,\r\n\t\t\t\t\tphone: '', //手机号 \r\n\t\t\t\t\tshort_code: '',\r\n\t\t\t\t\tcity: ''\r\n\t\t\t\t},\r\n\t\t\t\trule: [{\r\n\t\t\t\t\t\tname: \"user_name\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请输入您的姓名\",\r\n\t\t\t\t\t\tregType: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"phone\",\r\n\t\t\t\t\t\tcheckType: \"isMobile\",\r\n\t\t\t\t\t\terrorMsg: \"请输入手机号\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"city\",\r\n\t\t\t\t\t\tcheckType: \"isNotNull\",\r\n\t\t\t\t\t\terrorMsg: \"请输入您想代理的区域\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlockTap: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tmineInfo: state => state.user.mineInfo,\r\n\t\t}),\r\n\t\tasync onLoad(options) {\r\n\t\t\tthis.options = options\r\n\t\t\tthis.$util.showLoading()\r\n\t\t\tawait this.initIndex()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getUserInfo', 'getMineInfo']),\r\n\t\t\t...mapMutations(['updateUserItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!this.configInfo.id || !this.configInfo.hasOwnProperty(\r\n\t\t\t\t\t\t'free_fare_bear') || refresh) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t}\r\n\t\t\t\tawait Promise.all([this.getUserInfo(), this.getMineInfo()])\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_admin = 0,\r\n\t\t\t\t\t\tphone = ''\r\n\t\t\t\t} = this.mineInfo\r\n\t\t\t\tthis.form.phone = phone\r\n\t\t\t\tthis.is_send = phone ? false : true\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: is_admin ? '' : '招商加盟'\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\tthis.isLoad = true\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\ttoResetPhone() {\r\n\t\t\t\tthis.form.phone = ''\r\n\t\t\t\tthis.is_send = true\r\n\t\t\t},\r\n\t\t\t// 发送验证码\r\n\t\t\tasync toSend() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tauthTime\r\n\t\t\t\t} = this\r\n\t\t\t\tif (authTime) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tphone = ''\r\n\t\t\t\t} = this.form\r\n\t\t\t\tif (phone == null || !/^(1[0-9]{10})$/.test(phone)) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: phone == null ? `请输入手机号` : `${phone} 手机号无效`\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.$api.user.sendShortMsg({\r\n\t\t\t\t\t\tphone\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\tthis.form.short_code = ''\r\n\t\t\t\t\tlet time = 60\r\n\t\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\t\tif (time === 0) {\r\n\t\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\ttime--\r\n\t\t\t\t\t\tthis.authTime = time\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//表单验证\r\n\t\t\tvalidate(param) {\r\n\t\t\t\tlet validate = new this.$util.Validate();\r\n\t\t\t\tthis.rule.map(item => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tname,\r\n\t\t\t\t\t} = item\r\n\t\t\t\t\tvalidate.add(param[name], item);\r\n\t\t\t\t})\r\n\t\t\t\tlet message = validate.start();\r\n\t\t\t\treturn message;\r\n\t\t\t},\r\n\t\t\tasync submit() {\r\n\t\t\t\tlet param = this.$util.deepCopy(this.form)\r\n\t\t\t\tlet msg = this.validate(param);\r\n\t\t\t\tif (msg) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: msg\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tshort_code_status\r\n\t\t\t\t} = this.configInfo\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_send\r\n\t\t\t\t} = this\r\n\t\t\t\tif (short_code_status && is_send && (param.short_code == null || param.short_code.length != 6)) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `请输入6位数短信验证码`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!short_code_status || !is_send) {\r\n\t\t\t\t\tdelete param.short_code\r\n\t\t\t\t}\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.$api.agent.agentApply(param)\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `提交成功，即将跳转个人中心`,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (this.timer) {\r\n\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (getCurrentPages().length > 1) {\r\n\t\t\t\t\t\t\tthis.$util.back()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\t\turl: '/pages/mine',\r\n\t\t\t\t\t\t\topenType: `reLaunch`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\t.apply-info-box {\r\n\t\twidth: 100%;\r\n\t\theight: 1632rpx;\r\n\r\n\t\t.bg-1 {\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 1632rpx;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.bg-2 {\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 575rpx;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tz-index: 3;\r\n\t\t}\r\n\r\n\t\t.join-us {\r\n\t\t\twidth: 671rpx;\r\n\t\t\theight: 890rpx;\r\n\t\t\ttop: 482rpx;\r\n\t\t\tleft: 44rpx;\r\n\t\t\tz-index: 2;\r\n\r\n\t\t\t.item-input {\r\n\t\t\t\theight: 74rpx;\r\n\t\t\t\tborder: 4rpx solid #069F5E;\r\n\r\n\t\t\t\t.flex-between {\r\n\t\t\t\t\theight: 74rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.item-input.text {\r\n\t\t\t\theight: 82rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.submit {\r\n\t\t\twidth: 662rpx;\r\n\t\t\theight: 93rpx;\r\n\t\t\ttop: 1400rpx;\r\n\t\t\tleft: 47rpx;\r\n\t\t\tz-index: 2;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./apply.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110360957\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}