@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-btn-container {
  padding: 20rpx;
  text-align: center;
}
.login-btn-container .login-btn {
  display: inline-block;
  padding: 20rpx 60rpx;
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.pages-home .search-box {
  width: 710rpx;
  bottom: 0;
  z-index: 9;
  overflow: hidden;
}
.pages-home .recommend-technician {
  white-space: nowrap;
  width: 650rpx;
}
.pages-home .recommend-technician .recommend-item {
  display: inline-block;
}
.pages-home .recommend-technician .recommend-item.type-1 {
  width: 143rpx;
  margin-left: 26rpx;
}
.pages-home .recommend-technician .recommend-item.type-1 .cover {
  width: 143rpx;
  height: 143rpx;
}
.pages-home .recommend-technician .recommend-item.type-1 .ellipsis {
  max-width: 143rpx;
}
.pages-home .recommend-technician .recommend-item.type-2 {
  width: 203rpx;
  height: 151rpx;
  background: #F4F6F7;
  border-radius: 12rpx;
  margin-left: 20rpx;
}
.pages-home .recommend-technician .recommend-item.type-2 .cover {
  width: 70rpx;
  height: 70rpx;
}
.pages-home .recommend-technician .recommend-item.type-2 .ellipsis {
  max-width: 82rpx;
}
.pages-home .recommend-technician .recommend-item.type-2 .iconyduixingxingshixin {
  font-size: 26rpx;
  background-image: -webkit-linear-gradient(270deg, #FAD961 0%, #F76B1C 100%);
}
.pages-home .recommend-technician .recommend-item.type-2 .star-text {
  height: 26rpx;
  color: #FF9519;
  margin-left: 6rpx;
}
.pages-home .recommend-technician .recommend-item.type-2 .new-technician {
  width: 67rpx;
  height: 30rpx;
  border-radius: 8rpx;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.pages-home .recommend-technician .recommend-item:nth-child(1) {
  margin-left: 0;
}
.coupon-auto-popup {
  width: 600rpx;
  height: 737rpx;
  padding: 0 40rpx;
}
.coupon-auto-popup .bg-img {
  top: 0;
  left: 40rpx;
  width: 520rpx;
  height: 607rpx;
}
.coupon-auto-popup .icon-guanbi-fill {
  font-size: 40rpx;
  top: 22rpx;
  right: 0;
  z-index: 999;
}
.coupon-auto-popup .content {
  top: 200rpx;
  left: 40rpx;
  width: 520rpx;
  height: 365rpx;
  padding: 0rpx 60rpx;
}
.coupon-auto-popup .content .title {
  font-size: 52rpx;
  color: #F12E1A;
  margin-bottom: 16rpx;
}
.coupon-auto-popup .content .f-mini-title {
  color: #3A3A3A;
}
.coupon-auto-popup .get-btn {
  top: 657rpx;
  left: 77rpx;
  width: 448rpx;
  height: 81rpx;
  z-index: 1;
}
.coupon-auto-popup .get-btn.flex-center {
  z-index: 2;
}
.coupon-popup {
  width: 658rpx;
  height: 865rpx;
}
.coupon-popup .bg-img {
  width: 100%;
  height: 100%;
}
.coupon-popup .icon-close {
  font-size: 60rpx;
  top: 50rpx;
  right: 60rpx;
  z-index: 999;
}
.coupon-popup .coupon-info {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
}
.coupon-popup .coupon-info .tops {
  width: 480rpx;
  color: #FB4523;
  position: absolute;
  top: 260rpx;
}
.coupon-popup .coupon-info .tops > view:nth-child(1) {
  font-weight: bold;
  font-size: 30rpx;
}
.coupon-popup .coupon-info .lists {
  width: 500rpx;
  height: 300rpx;
  padding: 10rpx;
  overflow-x: hidden;
  position: absolute;
  bottom: 222rpx;
}
.coupon-popup .coupon-info .lists .list {
  width: 420rpx;
  height: 130rpx;
  margin-bottom: 10rpx;
  margin-top: 5rpx;
  position: relative;
}
.coupon-popup .coupon-info .lists .list > image {
  width: 100%;
  height: 100%;
}
.coupon-popup .coupon-info .lists .list > view {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 8rpx;
}
.coupon-popup .coupon-info .lists .list > view > view:nth-child(1) {
  width: 38%;
}
.coupon-popup .coupon-info .lists .list > view > view:nth-child(2) {
  display: flex;
  justify-content: center;
  flex: 1;
  padding: 0 15rpx;
  box-sizing: border-box;
}
.coupon-popup .coupon-info .lists .list > view .price {
  font-size: 30rpx;
  color: #FB4523;
}
.coupon-popup .coupon-info .lists .list > view .title {
  font-size: 30rpx;
  line-height: 36rpx;
  font-weight: bold;
}
.coupon-popup .coupon-info .lists .list > view .price_text {
  color: #ccc;
}
.coupon-popup view.btns {
  width: 100%;
  position: absolute;
  height: 82rpx;
  bottom: 0rpx;
  left: 0;
}
.coupon-popup view.btns > view {
  width: 422rpx;
  height: 82rpx;
  border-radius: 40rpx;
  font-size: 34rpx;
  color: #FFFFFF;
}

