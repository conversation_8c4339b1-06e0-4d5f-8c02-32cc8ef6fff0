## 0.5.2（2022-06-10）
- canvas 默认设置 disable-scroll 属性为true,  当在 canvas 中移动时且有绑定手势事件时，禁止屏幕滚动以及下拉刷新
## 0.5.1（2022-05-27）
- 取消属性 width, height 默认值
## 0.5.0（2022-05-27）
- 新增 v-sgin 组件获取坐标信息数组方法 `getLineData`
- 新增 v-sign 组件事件触发 @clear, @revoke, @end
- 修复撤销操作重设背景色导致线条消失问题
## 0.4.0（2022-05-15）
- 新增属性 bgColor 设置画布背景色，修复导出图片无背景色问题
## 0.3.4（2022-03-30）
修复 v-sign-action 子组件 svg 加载报错
## 0.3.3（2022-03-30）
修复 v-sign-action 子组件 svg 加载报错
## 0.3.2（2022-01-12）
- 修复多组件共存绘制问题
## 0.3.1（2021-12-14）
-  【新增】保存 png 图片方法 saveImage;
- 【修改】 控件子组件保存按钮动作由保存为临时文件路径改为保存图片
## 0.3.0（2021-12-11）
- 添加颜色选择器组件
## 0.2.0（2021-12-10）
- 修改为 uni_modules 规范
## 0.1.7 (2021-12-09)

- 画笔组件优化

## 0.1.6 (2021-12-08)

- 画笔样式优化

## 0.1.5 (2021-12-08)

- 画笔子组件样式修改，支持circle、line 两种样式

## 0.1.4 (2021-12-08)

- 添加画笔子组件

## 0.1.3 (2021-12-02)

- 添加按钮控件子组件

## 0.1.0 (2021-11-28)

- 支持线宽、颜色自定义，自定义画布样式
- 支持画布清空、撤回、保存图片
- 事件 init 暴露清空、撤回、保存图片方法