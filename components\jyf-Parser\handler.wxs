var inlineTags = {
	abbr: true,
	b: true,
	big: true,
	code: true,
	del: true,
	em: true,
	font: true,
	i: true,
	ins: true,
	label: true,
	mark: true,
	q: true,
	s: true,
	small: true,
	span: true,
	strong: true,
	u: true
}
module.exports = {
	getStyle: function(style, display) {
		var res = "";
		var reg = getRegExp("float[^;]+(?![\s\S]*?float)", "i");
		if (reg.test(style)) res += reg.exec(style)[0];
		reg = getRegExp("margin[^;]+", "gi");
		if (reg.test(style)) res += (';' + style.match(reg).join(';'));
		reg = getRegExp("display\s*:\s*([^;]*)(?![\s\S]*?display)", "i");
		if (reg.test(style) && reg.exec(style)[1] != "flex") res += (';' + reg.exec(style)[0]);
		else res += (';display:' + display);
		reg = getRegExp("flex[^;]*:[^;]+", "ig");
		if (reg.test(style)) res += (';' + style.match(reg).join(';'));
		reg = getRegExp("[^;\s]*width[^;]+", "ig");
		if (reg.test(style)) res += (';' + style.match(reg).join(';'));
		return res;
	},
	setImgStyle: function(item, imgMode, imgLoad) {
		if (imgMode == "widthFix") item.attrs.style = (item.attrs.style || '') + ";height:auto !important";
		if (item.attrs.style)
			item.attrs.style = item.attrs.style.replace(getRegExp("width[^;]*?%", "gi"), "width:100%").replace(
				getRegExp(
					'margin[^;]+', "gi"), "");
		if (!imgLoad) {
			delete item.attrs.src;
			item.attrs.style += ";width:20px !important;height:20px !important";
		}
		return [item];
	},
	setStyle: function(item) {
		if (item.attrs.style)
			item.attrs.style = item.attrs.style.replace(getRegExp("width[^;]*?%", "gi"), "width:100%").replace(
				getRegExp(
					'margin[^;]+', "gi"), "");
		return [item];
	},
	notContinue: function(item) {
		return !(item.c || inlineTags[item.name] || item["continue"]);
	}
}
