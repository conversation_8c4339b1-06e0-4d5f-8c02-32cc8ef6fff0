{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/App.vue?6587", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/App.vue?62eb", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/App.vue?2b61"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "component", "banner", "column", "fixed", "search", "auth", "tab", "abnor", "loadMore", "upload", "minCountdown", "uniNavBar", "uniPopup", "commonPopup", "loginInfo", "changeUserType", "OpenLocationInfo", "fixBottomButton", "uniSegmentedControl", "userPrivacy", "prototype", "$api", "api", "$util", "util", "_i18n", "i18n", "config", "productionTip", "ignoredElements", "App", "mpType", "app", "store", "$mount", "data", "timer", "travelTimer", "orderTimer", "lockTap", "resolvePrivacyAuthorization", "mounted", "onLaunch", "that", "uni", "lat", "res", "latitude", "lng", "longitude", "configInfo", "key", "val", "isGzhLogin", "arr", "commonOptions", "coach_id", "onShow", "success", "fail", "console", "coach_travel_order_id", "isConnected", "methodArr", "$store", "onHide", "onUnload", "watch", "resolvePrivacy", "useChooseLocation", "status", "coach_position", "auto_coach_position", "userCoachStatus", "newVal", "setTimeout", "locationChange", "complete", "coach_travel_arrive_id", "order_unix_list", "computed", "old_attendant_name", "old_channel_menu_name", "userInfo", "location", "changeOnAddr", "methods", "timerInit", "unix", "toHidePage", "isHide", "toChangeLocation", "initDistance", "param", "coach_lat", "coach_lng", "coach_status", "distance", "max", "address", "toAddCoachTravel", "coachtravel", "uid", "id", "toConfirmAddCoachTravel", "cur_unix", "loca", "order_id", "text", "toOrderUnix", "getTime", "clearInterval", "mergeLocaleMessage", "zh", "name", "cName", "old<PERSON>ame", "oldCName", "reg", "cReg"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AAEA;AACA;AACA;AAIA;AAAoC;AAAA;AAVpC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AA6B3DC,YAAG,CAACC,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;AAC/BF,YAAG,CAACC,SAAS,CAAC,QAAQ,EAAEE,MAAM,CAAC;AAC/BH,YAAG,CAACC,SAAS,CAAC,OAAO,EAAEG,KAAK,CAAC;AAC7BJ,YAAG,CAACC,SAAS,CAAC,QAAQ,EAAEI,MAAM,CAAC;AAC/BL,YAAG,CAACC,SAAS,CAAC,MAAM,EAAEK,IAAI,CAAC;AAC3BN,YAAG,CAACC,SAAS,CAAC,KAAK,EAAEM,GAAG,CAAC;AACzBP,YAAG,CAACC,SAAS,CAAC,OAAO,EAAEO,KAAK,CAAC;AAC7BR,YAAG,CAACC,SAAS,CAAC,UAAU,EAAEQ,QAAQ,CAAC;AACnCT,YAAG,CAACC,SAAS,CAAC,QAAQ,EAAES,MAAM,CAAC;AAC/BV,YAAG,CAACC,SAAS,CAAC,cAAc,EAAEU,YAAY,CAAC;AAC3CX,YAAG,CAACC,SAAS,CAAC,WAAW,EAAEW,SAAS,CAAC;AACrCZ,YAAG,CAACC,SAAS,CAAC,UAAU,EAAEY,QAAQ,CAAC;AACnCb,YAAG,CAACC,SAAS,CAAC,aAAa,EAAEa,WAAW,CAAC;AACzCd,YAAG,CAACC,SAAS,CAAC,WAAW,EAAEc,SAAS,CAAC;AACrCf,YAAG,CAACC,SAAS,CAAC,gBAAgB,EAAEe,cAAc,CAAC;AAC/ChB,YAAG,CAACC,SAAS,CAAC,kBAAkB,EAAEgB,gBAAgB,CAAC;AACnDjB,YAAG,CAACC,SAAS,CAAC,iBAAiB,EAAEiB,eAAe,CAAC;AACjDlB,YAAG,CAACC,SAAS,CAAC,qBAAqB,EAAEkB,mBAAmB,CAAC;AACzDnB,YAAG,CAACC,SAAS,CAAC,aAAa,EAAEmB,WAAW,CAAC;AAEzCpB,YAAG,CAACqB,SAAS,CAACC,IAAI,GAAGC,cAAG;AACxBvB,YAAG,CAACqB,SAAS,CAACG,KAAK,GAAGC,eAAI;AAC1BzB,YAAG,CAACqB,SAAS,CAACK,KAAK,GAAGC,eAAI;AAI1B3B,YAAG,CAAC4B,MAAM,CAACC,aAAa,GAAG,KAAK;AAChC7B,YAAG,CAAC4B,MAAM,CAACE,eAAe,8CAAQ9B,YAAG,CAAC4B,MAAM,CAACE,eAAe,IAAE,sBAAsB,EAAE;AAEtFC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAIjC,YAAG,iCACf+B,YAAG;EACNG,KAAK,EAALA,eAAK;EACLP,IAAI,EAAJA;AAAI,GACH;AACF,UAAAM,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;ACnEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAsxB,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACC1yB;AAKA;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EASA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACAC;gBACA,IACAC,MAEAC,IAFAC;kBACAC,MACAF,IADAG;gBAEAN;kBACAE;kBACAG;gBACA;cACA;cAEAE;cACA;gBACA;kBACAC;kBACAC;gBACA;cACA;cACAC;cACA;gBACAF;gBACAC;cACA;cACA;gBACAD;gBACAC;cACA;cACAE,mFACA,YACA;cACAA;gBACA;gBACA,0BAOA;gBACA;kBACAH;kBACAC;gBACA;cASA;cAEAG,gBACA,MADAA;cAAA,wBAIAA,cADAC;cAEA;gBACAD;gBACA;kBACAJ;kBACAC;gBACA;cACA;cACA;cAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAK;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAEA7D;gBACA;cACA;cACAA;gBAAA;gBACA8D;gBACAC;cACA;;cAGA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;cAGA;cAGAC,wBACA,OADAA;cAEA;gBACAV;gBACAC;cACA;cACAR;gBACA,IACAkB,cACAhB,IADAgB;gBAEA;gBACA,+EACA,8DACA;gBACAC;kBACAC;oBACAb;oBACAC;kBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAa;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAcA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EASAC;IACA;IACA;IACA;EACA;EAEAC;IACAC;MACA;QAEA;MAEA;IACA;IACA;MACA;QACA;MACA;IACA;IACAC;MACA,4BAIA;QAAA,+CAHAC;QAAAA;QAAA,+CACAC;QAAAA;QAAA,+CACAC;QAAAA;MAEA;QACA;QACA;UACAD;QACA;QACA;UACA;QACA;QACA;UACApB;UACAC;QACA;MACA;IACA;IACAqB;MAAA;MACA,qBAGAC,OAFAJ;QAAAA;QAAA,wBAEAI,OADAH;QAAAA;MAEA;QACA;UACA;QACA;QACA;QACA;UACApB;UACAC;QACA;QACA;UACA;UACA;YACAD;YACAC;UACA;UACAuB;YACA;cACAxB;cACAC;YACA;UACA;QACA;UACAuB;YACA;cACAxB;cACAC;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAwB;MACA;QACAhC;UACAiC;YAAA;UAAA;QACA;MACA;QACAjC;UACAiC;YAAA;UAAA;QACA;MACA;IACA;IACAhB;MACA;QACA;MACA;IACA;IACAiB;MACA;MACA;QACA;UACA3B;UACAC;QACA;QACA;MACA;IACA;IACA2B;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA9B;MAAA;IAAA;IACA+B;MAAA;IAAA;IACAC;MAAA;IAAA;IACA3B;MAAA;IAAA;IACAqB;MAAA;IAAA;IACAO;MAAA;IAAA;IACAC;MAAA;IAAA;IACAX;MAAA;IAAA;IACAJ;MAAA;IAAA;IACAgB;MAAA;IAAA;IACAxB;MAAA;IAAA;IACAkB;MAAA;IAAA;IACAX;MAAA;IAAA;EACA;EACAkB,uDACA,oHACA;IACAC;MAAA;MACA;MACA;QACA,4BAEA,oBADAC;UAAAA;QAEA;QACA;UACA;UACApC;UACA;YACAD;YACAC;UACA;QACA;MACA;IACA;IACAqC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAFA7C;gBACAG;gBAEA;kBACA;oBACAH;oBACAG;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2C;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAEA/C,MAEAgD,MAFAhD,KACAG,MACA6C,MADA7C;gBAAA,wBAOA,uEAJAsB,sIACAC,iGACAuB,uCACAC;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACAC;gBACAZ;kBACAG;kBACAI;kBACAK;gBACA;gBACA;kBACA9C;kBACAC;gBACA;gBACA8C;gBAAA,MACA3B;kBAAA;kBAAA;gBAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;kBACA;kBACA;oBACA1B;oBACAG;oBACAmD;kBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAvC,wBACA,OADAA;gBAGAwC,cACA,2BADAA;gBAGAC,MACA,gBADAC;gBAAA,wBAIA,uBADAjC;gBAEA;gBACA;kBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,sBAKA,iEAHAhB,2EACA3C,+BACAG;gBAEAyD;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA7D;gBACAG;cAAA;gBAAA,MAEAH;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACA8D;kBACAC;oBACA/D;oBACAG;oBACAmD;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAU;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;oBACA,uEACAC;oBACA;kBACA;kBACA;oBACA,uEACAA;oBACA;kBACA;kBACA;oBACA,uEACAA;oBACA;kBACA;kBACA;oBACA;sBACA3D;oBACA;kBACA;kBACA;oBACA;sBACAA;oBACA;kBACA;kBACA;oBACA;sBACAA;sBACAC;oBACA;kBACA;kBACA;oBACA2D;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,qBAIA,oBAFAC,0CACAC;gBAGAC,UAEA,QAFAnC,oBACAoC,WACA,QADAnC;gBAEAoC;gBACAC;gBACA;kBACA;oBACAN;kBACA;kBACA;oBACAA;kBACA;gBACA;gBACA;gBACA;kBACA9D;kBACAC;gBACA;gBACA;kBACAD;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACleA;AAAA;AAAA;AAAA;AAAq9C,CAAgB,y6CAAG,EAAC,C;;;;;;;;;;;ACAz+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\n\r\nimport api from \"api/index.js\"\r\nimport util from \"@/utils/index.js\"\r\nimport i18n from '@/locale/index.js';\r\n\r\n\r\n\r\nimport store from \"@/store/index.js\"\r\nimport banner from '@/components/banner.vue'\r\nimport column from '@/components/column.vue'\r\nimport fixed from '@/components/fixed.vue'\r\nimport search from '@/components/search.vue'\r\nimport auth from '@/components/auth.vue'\r\nimport tab from '@/components/tab.vue'\r\nimport abnor from '@/components/abnor.vue'\r\nimport loadMore from '@/components/load-more.vue'\r\nimport upload from '@/components/upload.vue'\r\nimport minCountdown from '@/components/min-countdown.vue'\r\nimport uniNavBar from '@/components/uni-nav-bar.vue'\r\nimport uniPopup from '@/components/uni-popup.vue'\r\nimport commonPopup from \"@/components/common-popup.vue\"\r\nimport loginInfo from \"@/components/login-info.vue\"\r\nimport changeUserType from \"@/components/change-user-type.vue\"\r\nimport OpenLocationInfo from \"@/components/open-location-info.vue\"\r\nimport fixBottomButton from \"@/components/fix-bottom-button.vue\"\r\nimport uniSegmentedControl from \"@/components/uni-segmented-control.vue\"\r\nimport userPrivacy from \"@/components/user-privacy-setting.vue\"\r\nVue.component('banner', banner)\r\nVue.component('column', column)\r\nVue.component('fixed', fixed)\r\nVue.component('search', search)\r\nVue.component('auth', auth)\r\nVue.component('tab', tab)\r\nVue.component('abnor', abnor)\r\nVue.component('loadMore', loadMore)\r\nVue.component('upload', upload)\r\nVue.component('minCountdown', minCountdown)\r\nVue.component('uniNavBar', uniNavBar)\r\nVue.component('uniPopup', uniPopup)\r\nVue.component('commonPopup', commonPopup)\r\nVue.component('loginInfo', loginInfo)\r\nVue.component('changeUserType', changeUserType)\r\nVue.component('OpenLocationInfo', OpenLocationInfo)\r\nVue.component('fixBottomButton', fixBottomButton)\r\nVue.component('uniSegmentedControl', uniSegmentedControl)\r\nVue.component('userPrivacy', userPrivacy)\r\n\r\nVue.prototype.$api = api\r\nVue.prototype.$util = util\r\nVue.prototype._i18n = i18n;\r\n\r\n\r\n\r\nVue.config.productionTip = false\r\nVue.config.ignoredElements = [ ...Vue.config.ignoredElements, 'wx-open-launch-weapp' ]\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n\t...App,\r\n\tstore,\r\n\ti18n\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\timport $api from \"@/api/index.js\"\r\n\timport $store from \"@/store/index.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttimer: null,\r\n\t\t\t\ttravelTimer: null,\r\n\t\t\t\torderTimer: null,\r\n\t\t\t\tlockTap: false,\r\n\t\t\t\tresolvePrivacyAuthorization: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {\r\n\t\t\t\twindow.entryUrl = window.location.href.split('#')[0]\r\n\t\t\t}\r\n\t\t\tif (window.location.href.indexOf('?#') < 0) {\r\n\t\t\t\twindow.location.href = window.location.href.replace(\"#\", \"?#\")\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tasync onLaunch() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.onLocationChange((res) => {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlatitude: lat,\r\n\t\t\t\t\tlongitude: lng\r\n\t\t\t\t} = res\r\n\t\t\t\tthat.toChangeLocation({\r\n\t\t\t\t\tlat,\r\n\t\t\t\t\tlng\r\n\t\t\t\t})\r\n\t\t\t})\r\n\r\n\t\t\tlet configInfo = uni.getStorageSync('configInfo') || ''\r\n\t\t\tif (configInfo) {\r\n\t\t\t\tthis.updateConfigItem({\r\n\t\t\t\t\tkey: 'configInfo',\r\n\t\t\t\t\tval: configInfo\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet isGzhLogin = uni.getStorageSync('isGzhLogin') || false\r\n\t\t\tthis.updateUserItem({\r\n\t\t\t\tkey: 'isGzhLogin',\r\n\t\t\t\tval: isGzhLogin\r\n\t\t\t})\r\n\t\t\tthis.updateUserItem({\r\n\t\t\t\tkey: 'changeAddr',\r\n\t\t\t\tval: false\r\n\t\t\t})\r\n\t\t\tlet arr = ['userInfo', 'autograph', 'location', 'locaRefuse', 'appLogin', 'loginType',\r\n\t\t\t\t'loginPage'\r\n\t\t\t]\r\n\t\t\tarr.map(key => {\r\n\t\t\t\tlet val = uni.getStorageSync(key) || ''\r\n\t\t\t\tif (key == 'locaRefuse') {\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tval = val || false\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef \t\r\n\t\t\t\t\tval = true\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey,\r\n\t\t\t\t\tval\r\n\t\t\t\t})\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (key == 'userInfo') {\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'isShowLogin',\r\n\t\t\t\t\t\tval: val && val.id ? false : true\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t\tlet {\r\n\t\t\t\tcommonOptions\r\n\t\t\t} = this\r\n\t\t\tlet {\r\n\t\t\t\tcoach_id = 0\r\n\t\t\t} = commonOptions\r\n\t\t\tif (coach_id) {\r\n\t\t\t\tcommonOptions.coach_id = 0\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'commonOptions',\r\n\t\t\t\t\tval: commonOptions\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.getConfigInfo()\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.getPrivacySetting()\r\n\t\t\t// #endif \r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.onNeedPrivacyAuthorization((resolve, eventInfo) => {\r\n\t\t\t\tthis.resolvePrivacyAuthorization = resolve\r\n\t\t\t})\r\n\t\t\twx.requirePrivacyAuthorize({ //该接口请求隐私协议，会自动触发上边的wx.onNeedPrivacyAuthorization\r\n\t\t\t\tsuccess: (suc) => {},\r\n\t\t\t\tfail: (fail) => {}\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\r\n\t\t\t// 只有在已登录时才获取用户教练状态\r\n\t\t\tif (this.userInfo.id) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait this.getUserCoachStatus()\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.log(\"获取用户教练状态失败:\", e)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.timerInit()\r\n\r\n\t\t\tlet {\r\n\t\t\t\tcoach_travel_order_id\r\n\t\t\t} = this\r\n\t\t\tthis.updateOrderItem({\r\n\t\t\t\tkey: 'coach_travel_order_id',\r\n\t\t\t\tval: coach_travel_order_id\r\n\t\t\t})\r\n\t\t\tuni.onNetworkStatusChange((res) => {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tisConnected\r\n\t\t\t\t} = res\r\n\t\t\t\tif (isConnected) return\r\n\t\t\t\tlet methodArr = ['updateServiceItem', 'updateTechnicianItem', 'updateMapItem',\r\n\t\t\t\t\t'updateDynamicItem', 'updateShopstoreItem', 'updateOrderItem'\r\n\t\t\t\t]\r\n\t\t\t\tmethodArr.map(item => {\r\n\t\t\t\t\t$store.commit(item, {\r\n\t\t\t\t\t\tkey: 'pageActive',\r\n\t\t\t\t\t\tval: false\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync onHide() {\r\n\t\t\t// #ifdef H5 \r\n\t\t\tlet {\r\n\t\t\t\tstatus = 0,\r\n\t\t\t\t\tcoach_position = 0\r\n\t\t\t} = this.userCoachStatus\r\n\t\t\tif (coach_position && status == 2) {\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'locationChange',\r\n\t\t\t\t\tval: false\r\n\t\t\t\t})\r\n\t\t\t\tthis.toHidePage()\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tif (this.coach_travel_order_id) {\r\n\t\t\t\tthis.toAddCoachTravel()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef H5\r\n\t\tdestroyed() {\r\n\t\t\tif (this.timer) clearInterval(this.timer)\r\n\t\t\tif (this.travelTimer) clearInterval(this.travelTimer)\r\n\t\t\tif (this.orderTimer) clearInterval(this.orderTimer)\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifndef H5\r\n\t\tonUnload() {\r\n\t\t\tif (this.timer) clearInterval(this.timer)\r\n\t\t\tif (this.travelTimer) clearInterval(this.travelTimer)\r\n\t\t\tif (this.orderTimer) clearInterval(this.orderTimer)\r\n\t\t},\r\n\t\t// #endif   \r\n\t\twatch: {\r\n\t\t\tresolvePrivacy(newval, oldval) {\r\n\t\t\t\tif (newval) {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tthis.resolvePrivacyAuthorization(newval)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'configInfo.attendant_name'(newVal, oldVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.mergeLocaleMessage()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tuseChooseLocation(newVal, oldVal) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstatus = 0,\r\n\t\t\t\t\t\tcoach_position = 0,\r\n\t\t\t\t\t\tauto_coach_position = 0\r\n\t\t\t\t} = this.userCoachStatus\r\n\t\t\t\tif (status == 2 && auto_coach_position) {\r\n\t\t\t\t\tlet cur = newVal ? 0 : 1\r\n\t\t\t\t\tlet userCoachStatus = Object.assign({}, this.$util.deepCopy(this.userCoachStatus), {\r\n\t\t\t\t\t\tcoach_position: cur\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (!newVal) {\r\n\t\t\t\t\t\tthis.timerInit()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'userCoachStatus',\r\n\t\t\t\t\t\tval: userCoachStatus\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tuserCoachStatus(newVal, oldVal) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstatus = 0,\r\n\t\t\t\t\t\tcoach_position = 0,\r\n\t\t\t\t} = newVal\r\n\t\t\t\tif (status == 2) {\r\n\t\t\t\t\tif (!this.timer) {\r\n\t\t\t\t\t\tthis.timerInit()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet locationChange = coach_position ? true : false\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'locationChange',\r\n\t\t\t\t\t\tval: locationChange\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (coach_position) {\r\n\t\t\t\t\t\tthis.timerInit()\r\n\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\tkey: 'locationChange',\r\n\t\t\t\t\t\t\tval: false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\tkey: 'locationChange',\r\n\t\t\t\t\t\t\t\tval: true\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 150)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\t\tkey: 'locationChange',\r\n\t\t\t\t\t\t\t\tval: locationChange\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 150)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.timer) clearInterval(this.timer)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlocationChange(newVal, oldVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tuni.startLocationUpdate({\r\n\t\t\t\t\t\tcomplete: msg => console.log(`startLocationUpdate API complete`, msg)\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.stopLocationUpdate({\r\n\t\t\t\t\t\tcomplete: msg => console.log(`stopLocationUpdate API complete`, msg)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcoach_travel_order_id(newVal, oldVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.toAddCoachTravel()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcoach_travel_arrive_id(newVal, oldVal) {\r\n\t\t\t\tif (this.travelTimer) clearInterval(this.travelTimer)\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.updateOrderItem({\r\n\t\t\t\t\t\tkey: 'coach_travel_order_id',\r\n\t\t\t\t\t\tval: 0\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.toConfirmAddCoachTravel(newVal)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\torder_unix_list(newVal, oldVal) {\r\n\t\t\t\tif (this.orderTimer) clearInterval(this.orderTimer)\r\n\t\t\t\tif (newVal && newVal.length > 0) {\r\n\t\t\t\t\tthis.toOrderUnix(newVal)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\told_attendant_name: state => state.config.old_attendant_name,\r\n\t\t\told_channel_menu_name: state => state.config.old_channel_menu_name,\r\n\t\t\tcommonOptions: state => state.user.commonOptions,\r\n\t\t\tlocationChange: state => state.user.locationChange,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\tlocation: state => state.user.location,\r\n\t\t\tuserCoachStatus: state => state.user.userCoachStatus,\r\n\t\t\tuseChooseLocation: state => state.user.useChooseLocation,\r\n\t\t\tchangeOnAddr: state => state.user.changeOnAddr,\r\n\t\t\tcoach_travel_order_id: state => state.order.coach_travel_order_id,\r\n\t\t\torder_unix_list: state => state.order.order_unix_list,\r\n\t\t\tresolvePrivacy: state => state.user.resolvePrivacy\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo', 'getUserInfo', 'getUserCoachStatus', 'getPrivacySetting', 'toPlayAudio']),\r\n\t\t\t...mapMutations(['updateUserItem', 'updateOrderItem', 'updateConfigItem']),\r\n\t\t\ttimerInit() {\r\n\t\t\t\tif (this.timer) clearInterval(this.timer)\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tunix = 0\r\n\t\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\t\tif (!unix || (unix && (cur_unix - unix >= 3))) {\r\n\t\t\t\t\t\tlet val = this.$util.deepCopy(this.userCoachStatus)\r\n\t\t\t\t\t\tval.unix = cur_unix\r\n\t\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\t\tkey: 'userCoachStatus',\r\n\t\t\t\t\t\t\tval: val\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 30000)\r\n\t\t\t},\r\n\t\t\tasync toHidePage(isHide = 1) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlat,\r\n\t\t\t\t\tlng\r\n\t\t\t\t} = await this.$util.getUtilLocation(false)\r\n\t\t\t\tif (lat && lng) {\r\n\t\t\t\t\tthis.toChangeLocation({\r\n\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\tlng\r\n\t\t\t\t\t}, isHide)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync toChangeLocation(param, initDistance = 0) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlat,\r\n\t\t\t\t\tlng\r\n\t\t\t\t} = param\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstatus: coach_status = 0,\r\n\t\t\t\t\tcoach_position = 0,\r\n\t\t\t\t\tlat: coach_lat,\r\n\t\t\t\t\tlng: coach_lng\r\n\t\t\t\t} = this.userCoachStatus\r\n\t\t\t\tif (coach_status == 2) {\r\n\t\t\t\t\tlet distance = this.$util.getDistance(lat, lng, coach_lat, coach_lng)\r\n\t\t\t\t\tlet changeOnAddr = Object.assign({}, param, {\r\n\t\t\t\t\t\tunix: this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s')),\r\n\t\t\t\t\t\tinitDistance,\r\n\t\t\t\t\t\tdistance\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\t\tkey: 'changeOnAddr',\r\n\t\t\t\t\t\tval: changeOnAddr\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet max = 100\r\n\t\t\t\t\tif (coach_position && (initDistance == 1 || (distance * 1 >= max))) {\r\n\t\t\t\t\t\tif (this.lockTap) return\r\n\t\t\t\t\t\tthis.lockTap = true\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// 优化0112 不解析地址\r\n\t\t\t\t\t\t\tthis.$util.updateCoachAddr({\r\n\t\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\t\t\taddress: ''\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 轨迹\r\n\t\t\tasync toAddCoachTravel() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcoach_travel_order_id\r\n\t\t\t\t} = this\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcoachtravel\r\n\t\t\t\t} = this.configInfo.plugAuth\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: uid\r\n\t\t\t\t} = this.userInfo\r\n\t\t\t\tlet {\r\n\t\t\t\t\tstatus: coach_status = 0,\r\n\t\t\t\t} = this.userCoachStatus\r\n\t\t\t\tif (this.travelTimer) clearInterval(this.travelTimer)\r\n\t\t\t\tif (coachtravel && coach_travel_order_id && uid && coach_status == 2) {\r\n\t\t\t\t\tthis.toConfirmAddCoachTravel(coach_travel_order_id)\r\n\t\t\t\t\tthis.travelTimer = setInterval(() => {\r\n\t\t\t\t\t\tthis.toConfirmAddCoachTravel(coach_travel_order_id)\r\n\t\t\t\t\t}, 5000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync toConfirmAddCoachTravel(order_id) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tunix = 0,\r\n\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\tlng\r\n\t\t\t\t} = this.changeOnAddr\r\n\t\t\t\tlet cur_unix = this.$util.DateToUnix(this.$util.formatTime(new Date(), 'YY-M-D h:m:s'))\r\n\t\t\t\tif (!unix || (unix && (cur_unix - unix > 2))) {\r\n\t\t\t\t\tlet loca = await this.$util.getUtilLocation(false)\r\n\t\t\t\t\tlat = loca.lat\r\n\t\t\t\t\tlng = loca.lng\r\n\t\t\t\t}\r\n\t\t\t\tif (lat && lng) {\r\n\t\t\t\t\tawait this.$api.technician.coachTrajectoryAdd({\r\n\t\t\t\t\t\torder_id,\r\n\t\t\t\t\t\ttext: {\r\n\t\t\t\t\t\t\tlat,\r\n\t\t\t\t\t\t\tlng,\r\n\t\t\t\t\t\t\taddress: ''\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync toOrderUnix(list) {\r\n\t\t\t\tthis.orderTimer = setInterval(() => {\r\n\t\t\t\t\tlet count_down = list.filter(item => {\r\n\t\t\t\t\t\tlet unix = Math.ceil((item.start_service_time_unix * 1000 - new Date()\r\n\t\t\t\t\t\t\t.getTime()) / 1000)\r\n\t\t\t\t\t\treturn unix == 300\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet end = list.filter(item => {\r\n\t\t\t\t\t\tlet unix = Math.ceil((item.start_service_time_unix * 1000 - new Date()\r\n\t\t\t\t\t\t\t.getTime()) / 1000)\r\n\t\t\t\t\t\treturn unix == 0\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet val = list.filter(item => {\r\n\t\t\t\t\t\tlet unix = Math.ceil((item.start_service_time_unix * 1000 - new Date()\r\n\t\t\t\t\t\t\t.getTime()) / 1000)\r\n\t\t\t\t\t\treturn unix > 0\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (count_down.length > 0) {\r\n\t\t\t\t\t\tthis.toPlayAudio({\r\n\t\t\t\t\t\t\tkey: 'countdown_voice'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (end.length > 0) {\r\n\t\t\t\t\t\tthis.toPlayAudio({\r\n\t\t\t\t\t\t\tkey: 'service_end_recording'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (val.length !== list.length) {\r\n\t\t\t\t\t\tthis.updateOrderItem({\r\n\t\t\t\t\t\t\tkey: 'order_unix_list',\r\n\t\t\t\t\t\t\tval\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (val.length === 0) {\r\n\t\t\t\t\t\tclearInterval(this.orderTimer)\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\tasync mergeLocaleMessage() {\r\n\t\t\t\tlet zh = JSON.parse(JSON.stringify(this.$i18n.messages.zh))\r\n\t\t\t\tlet {\r\n\t\t\t\t\tattendant_name: name,\r\n\t\t\t\t\tchannel_menu_name: cName\r\n\t\t\t\t} = this.configInfo\r\n\t\t\t\tlet {\r\n\t\t\t\t\told_attendant_name: oldName,\r\n\t\t\t\t\told_channel_menu_name: oldCName\r\n\t\t\t\t} = this\r\n\t\t\t\tlet reg = new RegExp(oldName, 'g')\r\n\t\t\t\tlet cReg = new RegExp(oldCName, 'g')\r\n\t\t\t\tfor (let i in zh.action) {\r\n\t\t\t\t\tif (zh.action[i].includes(oldName)) {\r\n\t\t\t\t\t\tzh.action[i] = zh.action[i].replace(reg, name)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (zh.action[i].includes(oldCName)) {\r\n\t\t\t\t\t\tzh.action[i] = zh.action[i].replace(cReg, cName)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.$i18n.mergeLocaleMessage('zh', zh)\r\n\t\t\t\tthis.updateConfigItem({\r\n\t\t\t\t\tkey: 'old_attendant_name',\r\n\t\t\t\t\tval: name\r\n\t\t\t\t})\r\n\t\t\t\tthis.updateConfigItem({\r\n\t\t\t\t\tkey: 'old_channel_menu_name',\r\n\t\t\t\t\tval: cName\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"/styles/index.wxss\";\r\n\r\n\t/* #ifdef H5 */\r\n\tuni-page-head {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.map-box .csssprite {\r\n\t\tborder: 2px solid #fff !important;\r\n\t\tbox-sizing: border-box;\r\n\t\tbox-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);\r\n\t\tborder-radius: 50% !important;\r\n\t}\r\n\r\n\t.map-box a div .csssprite {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\tpage {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #222;\r\n\t\tline-height: 1.5;\r\n\t\tbackground: #F6F6F6;\r\n\t\tfont-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif;\r\n\t}\r\n\r\n\tinput {\r\n\t\t// font-family: PingFangSC-Medium, PingFang SC, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;\r\n\t}\r\n\r\n\tinput::-webkit-input-placeholder {\r\n\t\t/* WebKit browsers */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput:-moz-placeholder {\r\n\t\t/* Mozilla Firefox 4 to 18 */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput::-moz-placeholder {\r\n\t\t/* Mozilla Firefox 19+ */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput:-ms-input-placeholder {\r\n\t\t/* Internet Explorer 10+ */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tview {\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\timage {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.h5-image {\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n\t}\r\n\r\n\t/*隐藏滚动条*/\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t}\r\n\r\n\t/* #ifdef MP-BAIDU */\r\n\t.swan-button.swan-button-radius-ios {\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754121666221\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}