<view class="pages-home"><block wx:if="{{isLoad}}"><block><uni-nav-bar vue-id="c84c028c-1" fixed="{{true}}" shadow="{{false}}" statusBar="{{true}}" title="{{configInfo.app_text}}" color="#ffffff" backgroundColor="{{primaryColor}}" bind:__l="__l"></uni-nav-bar><view style="{{'height:'+(configInfo.navBarHeight+'px')+';'}}"></view><block wx:if="{{!userInfo.id}}"><view class="login-btn-container"><view data-event-opts="{{[['tap',[['wxLogin',['$event']]]]]}}" class="login-btn" style="{{'background-color:'+(primaryColor)+';'}}" bindtap="__e">点击登录</view></view></block><block wx:if="{{$root.g0}}"><view class="{{[[($root.g1==0)?'mt-md':''],[($root.g2>0)?'rel':'']]}}" style="{{'height:'+($root.g3>0?banner.banner_height+(banner.service_filter?64:0)+'rpx':'84rpx')+';'}}"><block wx:if="{{$root.g4>0}}"><banner vue-id="c84c028c-2" list="{{banner.list}}" height="{{banner.banner_height}}" margin="{{0}}" autoplay="{{true}}" indicatorActiveColor="{{primaryColor}}" dotWidth="{{20}}" dotBottom="{{30}}" data-event-opts="{{[['^change',[['goBanner']]]]}}" bind:change="__e" bind:__l="__l"></banner></block><block wx:if="{{banner.service_filter}}"><view class="{{['search-box','flex-center','fill-base','ml-md','mr-md','radius',[($root.g5>0)?'abs':'']]}}"><view style="width:92%;"><tab vue-id="c84c028c-3" isLine="{{false}}" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="{{100/$root.g6+'%'}}" height="84rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab></view><view style="width:8%;"></view></view></block></view></block><block wx:if="{{$root.g7>0}}"><view class="fill-base mt-md ml-md mr-md radius-16" style="overflow:hidden;"><column vue-id="c84c028c-4" list="{{service_cate.list}}" indicatorActiveColor="{{primaryColor}}" colNum="{{service_cate.col_num}}" rowNum="{{service_cate.row_num}}" data-event-opts="{{[['^change',[['goCate']]]]}}" bind:change="__e" bind:__l="__l"></column></view></block><block wx:if="{{$root.g8}}"><view class="fill-base mt-md ml-md mr-md pt-lg pl-lg pr-md pb-lg radius-16"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="flex-between pb-lg" catchtap="__e"><view class="f-paragraph c-black text-bold">{{"推荐"+$root.m0}}</view><view class="flex-y-center f-caption c-caption">查看更多<view class="iconfont icon-right _i" style="font-size:24rpx;"></view></view></view><scroll-view class="recommend-technician" scroll-x="{{true}}"><block wx:for="{{recommend_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{recommend_style==1}}"><view data-event-opts="{{[['tap',[['toTechnician',[index]]]]]}}" class="recommend-item type-1" catchtap="__e"><image class="cover radius-16" mode="aspectFill" lazy-load="{{true}}" src="{{item.work_img}}"></image><view class="flex-center f-desc c-title mt-md"><view class="ellipsis">{{item.coach_name}}</view></view></view></block><block wx:if="{{recommend_style==2}}"><view data-event-opts="{{[['tap',[['toTechnician',[index]]]]]}}" class="recommend-item type-2 pd-md" catchtap="__e"><view class="flex-center pb-sm"><image class="cover radius" mode="aspectFill" lazy-load="{{true}}" src="{{item.work_img}}"></image><view class="flex-1 ml-sm"><view class="f-desc ellipsis">{{item.coach_name}}</view><view class="flex-y-baseline" style="margin-top:4rpx;"><view class="iconfont iconyduixingxingshixin icon-font-color _i"></view><view class="star-text flex-y-center f-caption">{{item.star}}</view></view></view></view><view class="flex-center"><block wx:if="{{item.is_new}}"><view class="new-technician flex-center f-icontext" style="{{'color:'+(primaryColor)+';'+('border:'+('1rpx solid '+primaryColor)+';')}}">新人</view></block><block wx:else><view class="f-icontext c-caption">{{"30天接单"+(item.order_count||0)+''}}</view></block></view></view></block></block></block></scroll-view></view></block><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="fill-base mt-md ml-md mr-md pd-lg radius-16"><service-list-item vue-id="{{'c84c028c-5-'+index}}" info="{{item}}" bind:__l="__l"></service-list-item></view></block><block wx:if="{{loading}}"><load-more vue-id="c84c028c-6" noMore="{{$root.g9}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g10}}"><block><block wx:if="{{location.lat&&location.lng}}"><abnor vue-id="c84c028c-7" bind:__l="__l"></abnor></block><block wx:if="{{!location.lat&&!location.lng}}"><block><abnor vue-id="c84c028c-8" type="NOT_LOCATION" title="暂无数据" button="{{[{text:'开启定位',type:'confirm'}]}}" btnSize data-event-opts="{{[['^confirm',[['toOpenLocation']]]]}}" bind:confirm="__e" bind:__l="__l"></abnor></block></block></block></block><view class="space-footer"></view><uni-popup class="vue-ref" vue-id="c84c028c-9" type="center" maskClick="{{false}}" data-ref="coupon_auto_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-auto-popup rel"><image class="bg-img abs" mode="aspectFill" lazy-load="{{true}}" src="https://lbqny.migugu.com/admin/anmo/mine/coupon-bg.png"></image><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="iconfont icon-guanbi-fill c-base abs _i" catchtap="__e"></view><view class="content flex-center flex-column abs"><view class="title text-bold flex-center">恭喜获得</view><view class="f-mini-title text-bold">{{"价值"+(coupon_discount||0)+"元的优惠券,\n\t\t\t\t\t\t已自动发放到您的卡包,点击下方按钮前去查看吧~"}}</view></view><image class="get-btn abs" src="https://lbqny.migugu.com/admin/anmo/mine/coupon-get-btn.png"></image><view data-event-opts="{{[['tap',[['toCouponGetBtn',['$event']]]]]}}" class="get-btn flex-center f-mini-title text-bold c-base abs" catchtap="__e">前往卡包查看</view></view></uni-popup><uni-popup class="vue-ref" vue-id="c84c028c-10" type="center" maskClick="{{false}}" data-ref="coupon_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-popup flex-center rel"><image class="bg-img" mode="aspectFill" lazy-load="{{true}}" src="https://lbqnyv2.migugu.com/bianzu3.png"></image><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="iconfont icon-close c-base abs _i" catchtap="__e"></view><view class="coupon-info flex-center flex-column"><view class="tops flex-center flex-column"><view>成功领取</view><view>卡券将放入“我的-我的卡券”</view></view><view class="lists flex-center"><scroll-view style="width:420rpx;height:100%;" scroll-y="{{true}}"><block wx:for="{{couponList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list flex-between"><image src="https://lbqny.migugu.com/admin/anmo/coupon/coupon.png" mode="aspectFill"></image><view class="flex-between"><view class="flex-center flex-column"><view class="price">{{''+item.discount+''}}</view><view class="price_text">{{''+(item.full*1>0?'满'+item.full+'可用':'立减')+''}}</view></view><view class="title flex-y-center"><view class="ellipsis-3">{{''+item.title+''}}</view></view></view></view></block></scroll-view></view></view><view data-event-opts="{{[['tap',[['userGetCoupon',['$event']]]]]}}" class="btns flex-center" catchtap="__e"><view class="flex-center">领取到卡包</view></view></view></uni-popup><view style="{{'height:'+(configInfo.tabbarHeight+'px')+';'}}"></view><tabbar vue-id="c84c028c-11" cur="{{1}}" bind:__l="__l"></tabbar><change-user-type vue-id="c84c028c-12" bind:__l="__l"></change-user-type></block></block><user-privacy class="vue-ref" vue-id="c84c028c-13" show="{{false}}" data-ref="user_privacy" bind:__l="__l"></user-privacy></view>