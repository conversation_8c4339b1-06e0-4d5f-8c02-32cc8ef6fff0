{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?57ba", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?4bba", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?9522", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?3ffb", "uni-app:///components/tabbar.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?3cef", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tabbar.vue?ca8a"], "names": ["components", "props", "cur", "type", "default", "data", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "userPageType", "activeIndex", "mounted", "that", "sysheight", "navBarHeight", "setTimeout", "query", "key", "val", "methods", "changeTab", "item", "arr", "index", "page", "url", "openType"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAyxB,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACe7yB;AAIA;AAAA;AAAA,gBACA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACAC;cAEAC,eACAF,gBADAE;cAEAC;gBACA;gBACAC;kBACA;kBACA;kBACAT;kBACAA;kBACAK;oBACAK;oBACAC;kBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC,uDACA,2CACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBACAC;kBACA;gBACA;gBACAD;cAAA;gBAAA,QAIAA,MADAE;gBAGAb,cAGA,OAHAA,aACAV,MAEA,OAFAA,KACAS,eACA,OADAA;gBAEAe;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;kBACAE;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAEA;AAAA,4B;;;;;;;;;;;;;ACjGA;AAAA;AAAA;AAAA;AAAg/C,CAAgB,o8CAAG,EAAC,C;;;;;;;;;;;ACApgD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabbar.vue?vue&type=template&id=852a8b4e&scoped=true&\"\nvar renderjs\nimport script from \"./tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"852a8b4e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabbar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=template&id=852a8b4e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.configInfo.tabBar.length\n  var l0 = _vm.__map(_vm.configInfo.tabBar, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = item.default_img.includes(\"https://\")\n    var g2 = item.default_img.includes(\"https://\")\n    var g3 = _vm.cur == item.id && item.default_img.includes(\"https://\")\n    return {\n      $orig: $orig,\n      g1: g1,\n      g2: g2,\n      g3: g3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"custom-tabbar fix flex-center fill-base b-1px-t\">\r\n\t\t<view @tap.stop=\"changeTab(item)\" class=\"flex-center flex-column mt-sm\" :style=\"{width: (100/configInfo.tabBar.length) + '%',color:item.default_img.includes('https://')\r\n            ? '#252324' : cur == item.id ? primaryColor : item.selected_img == item.default_img ? '#BCCAD9':'#666'}\"\r\n\t\t\tv-for=\"(item,index) in configInfo.tabBar\" :key=\"index\">\r\n\t\t\t<image class=\"item-img\" :src=\"cur == item.id ? item.selected_img : item.default_img\"\r\n\t\t\t\tv-if=\"item.default_img.includes('https://')\"></image>\r\n\t\t\t<i class=\"iconfont\" :class=\"cur == item.id ? item.selected_img : item.default_img\" v-else></i>\r\n\t\t\t<view class=\"text\" :class=\"[{ 'text-bold': cur == item.id && item.default_img.includes('https://') }]\">\r\n\t\t\t\t{{item.name}}</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tprops: {\r\n\t\t\tcur: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t\tuserPageType: state => state.user.userPageType,\r\n\t\t\tactiveIndex: state => state.order.activeIndex,\r\n\t\t}),\r\n\t\tasync mounted() {\r\n\t\t\tlet that = this;\r\n\t\t\tlet sysheight = uni.getSystemInfoSync().windowHeight\r\n\t\t\tlet {\r\n\t\t\t\tnavBarHeight\r\n\t\t\t} = that.configInfo\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(that);\r\n\t\t\t\tquery.select('.custom-tabbar').boundingClientRect(data => {\r\n\t\t\t\t\tlet curSysHeight = sysheight - data.height - navBarHeight\r\n\t\t\t\t\tlet configInfo = that.$util.deepCopy(this.configInfo)\r\n\t\t\t\t\tconfigInfo.curSysHeight = curSysHeight\r\n\t\t\t\t\tconfigInfo.tabbarHeight = data.height\r\n\t\t\t\t\tthat.updateConfigItem({\r\n\t\t\t\t\t\tkey: 'configInfo',\r\n\t\t\t\t\t\tval: configInfo\r\n\t\t\t\t\t})\r\n\t\t\t\t}).exec();\r\n\t\t\t}, 500)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo']),\r\n\t\t\t...mapMutations(['updateConfigItem']),\r\n\t\t\t// 点击跳转\r\n\t\t\tasync changeTab(item) {\r\n\t\t\t\tif (!item.id) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t\tlet arr = this.configInfo.tabBar.filter(aitem => {\r\n\t\t\t\t\t\treturn aitem.name == item.name\r\n\t\t\t\t\t})\r\n\t\t\t\t\titem = arr[0]\r\n\t\t\t\t}\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: index\r\n\t\t\t\t} = item\r\n\t\t\t\tlet {\r\n\t\t\t\t\tactiveIndex,\r\n\t\t\t\t\tcur,\r\n\t\t\t\t\tuserPageType\r\n\t\t\t\t} = this\r\n\t\t\t\tlet page = {\r\n\t\t\t\t\t1: `/pages/service`,\r\n\t\t\t\t\t2: `/pages/technician`,\r\n\t\t\t\t\t3: `/pages/dynamic`,\r\n\t\t\t\t\t4: `/pages/order?tab=${activeIndex}`,\r\n\t\t\t\t\t5: `/pages/mine?type=${userPageType}`,\r\n\t\t\t\t\t6: `/pages/shopstore`,\r\n\t\t\t\t\t7: `/pages/map`\r\n\t\t\t\t}\r\n\t\t\t\tif (index == cur) return\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl: page[index],\r\n\t\t\t\t\topenType: `reLaunch`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.custom-tabbar {\r\n\t\theight: 98rpx;\r\n\t\tbottom: 0;\r\n\t\theight: calc(98rpx + env(safe-area-inset-bottom) / 2);\r\n\t\tpadding-bottom: calc(env(safe-area-inset-bottom) / 2);\r\n\r\n\t\t.item-img {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t}\n\t\t\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t}\n\t\t\r\n\t\t.text {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tmargin-top: 5rpx;\r\n\t\t\theight: 32rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110364685\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}