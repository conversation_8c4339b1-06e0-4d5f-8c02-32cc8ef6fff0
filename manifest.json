{
    "name" : "预约",
    "appid" : "__UNI__307A9A7", // 改为自己的
    "description" : "",
    "versionName" : "1.1.0",
    "versionCode" : 110,
    "transformPx" : false,
    "uniStatistics" : {
        "enable" : false //全局关闭  
    },
    "mp-weixin" : {
        "appid" : "wx0ba54dc93331fe90",
        "setting" : {
            "urlCheck" : false,
            "postcss" : true,
            "minified" : true,
            "es6" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "plugins" : {},
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序距离展示"
            }
        },
        "requiredPrivateInfos" : [
            "getLocation",
            "chooseLocation",
            "startLocationUpdate",
            "onLocationChange"
        ],
        "__usePrivacyCheck__" : true,
        "navigateToMiniProgramAppIdList" : [],
        "preloadRule" : {},
        "uniStatistics" : {
            "enable" : false
        }
    },
    "h5" : {
        "title" : "",
        "domain" : "https://7.cab/", // 改为自己的
        "devServer" : {
            "https" : true
        },
        // "devServer" : {
        //     "disableHostCheck" : true,
        //     "proxy" : {
        //         "/api" : {
        //             "target" : "https://massage.cncnconnect.com",
        //             "changeOrigin" : true,
        //             "secure" : false,
        //             "pathRewrite" : {
        //                 "^/api" : ""
        //             }
        //         }
        //     }
        // },
        "router" : {
            "base" : "/h5/"
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "4IJBZ-DLACP-3QBDS-LI4FT-U2MZZ-5KFFA"
                }
            }
        },
        "template" : "index.html"
    },
    "app-plus" : {
        "runmode" : "liberate",
        /* 5+App特有相关 */
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "OAuth" : {},
            "Geolocation" : {},
            "Maps" : {},
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Camera" : {}
        },
        /* 模块配置 */
        "distribute" : {
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx0ba54dc93331fe90", // 改为自己的 微信开放平台应用信息
                        "appsecret" : "86122c9823f78cdd6304695061d2424f", // 改为自己的
                        "UniversalLinks" : "https://7.cabct.com/uni-universallinks/__UNI__307A9A7/" // 改为自己的
                    },
                    "apple" : {}
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "d7600cb7133b50d3d27868984dacea0a",
                        "appkey_android" : "6cbbd69cc7973aba0d1c834accb2ceb3"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "d7600cb7133b50d3d27868984dacea0a",
                        "appkey_android" : "6cbbd69cc7973aba0d1c834accb2ceb3"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx0ba54dc93331fe90", // 改为自己的
                        "UniversalLinks" : "https://7.cabct.com/uni-universallinks/__UNI__307A9A7/" // 改为自己的
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx0ba54dc93331fe90", // 改为自己的
                        "UniversalLinks" : "https://7.cabct.com/uni-universallinks/__UNI__307A9A7/" // 改为自己的
                    }
                },
                "ad" : {}
            },
            "android" : {
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许。"
                },
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CLEAR_APP_USER_DATA\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "autoSdkPermissions" : true
            },
            "icons" : {
                // 改为自己的 logo
                "android" : {
                    "hdpi" : "unpackage/res/logo/logo.png",
                    "xhdpi" : "unpackage/res/logo/logo.png",
                    "xxhdpi" : "unpackage/res/logo/logo.png",
                    "xxxhdpi" : "unpackage/res/logo/logo.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/logo/logo.png",
                    "iphone" : {
                        "app@2x" : "unpackage/res/logo/logo-120.png",
                        "app@3x" : "unpackage/res/logo/logo-180.png",
                        "spotlight@2x" : "unpackage/res/logo/logo-80.png",
                        "spotlight@3x" : "unpackage/res/logo/logo-120.png",
                        "settings@2x" : "unpackage/res/logo/logo-58.png",
                        "settings@3x" : "unpackage/res/logo/logo-87.png",
                        "notification@2x" : "unpackage/res/logo/logo-40.png",
                        "notification@3x" : "unpackage/res/logo/logo-60.png"
                    },
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "proapp@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "notification" : "",
                        "notification@2x" : ""
                    }
                }
            },
            "ios" : {
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:7.cabct.com" ] // 改为自己的
                    }
                },
                "dSYMs" : false,
                "privacyDescription" : {
                    // ios隐私信息访问许可描述
                    "NSPhotoLibraryUsageDescription" : "允许手机存储权限读写相册，注册服务人员/退款时需从相册中选择图片",
                    "NSPhotoLibraryAddUsageDescription" : "允许手机存储权限读写相册，用于保存图片",
                    "NSCameraUsageDescription" : "需要访问相册，以便于上传服务人员资格证书、工作形象照等功能",
                    "NSLocationWhenInUseUsageDescription" : "允许获取您的位置信息，以便获取您附近的服务人员，更好的为您服务",
                    "NSLocationAlwaysUsageDescription" : "允许获取您的位置信息，以便获取您附近的服务人员，更好的为您服务",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "允许获取您的位置信息，以便获取您附近的服务人员，更好的为您服务"
                }
            }
        }
    },
    "nativePlugins" : {},
    "_spaceID" : "8a1db412-049f-4be6-894a-755ffe5ebe5b"
}
