{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?8892", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?0a78", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?2839", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?a1d8", "uni-app:///agent/pages/poster/salesman.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?e0f0", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/poster/salesman.vue?5bfd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "props", "data", "confirmText", "src", "computed", "primaryColor", "configInfo", "userInfo", "onLoad", "bg", "that", "setTimeout", "methods", "renderToCanvas", "type", "qr_code", "salesman_poster", "cover", "nick<PERSON><PERSON>", "agent_name", "avatarUrl", "qr_radius", "poster", "css", "width", "height", "views", "objectFit", "top", "left", "position", "background", "bottom", "borderRadius", "text", "fontSize", "fontWeight", "color", "fileType", "quality", "success", "previewImage", "uni", "current", "urls", "saveImage", "privacyCheck", "filePath", "err", "icon", "title", "toPreviewSave"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2xB,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACoB/yB;AAGA;AAAA;AAAA,eACA;EACAC;EACAC,QAEA;EACAC;IACA;MAKAC;MAEAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cASA;cAAA;cAAA,OACA;YAAA;cACA;gBACAC;cACA;cACAC;cACAC;gBACAD;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAE,yCACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBAAA;gBAAA,OACA;kBACAI;gBACA;cAAA;gBAFAC;gBAAA,wBAMA,kBADAC;gBAEAC;gBAEAC,WACA,gCADAC;gBAGAC,YACA,gBADAA;gBAEAF;gBACAG;gBAEAA;gBAGAC;kBACAC;oBACAC;oBACAC;kBACA;kBACAC;oBACAZ;oBACAX;oBACAoB;sBACAC;sBACAC;sBACAE;sBACAC;sBACAC;sBACAC;oBACA;kBACA,GACA;oBACAhB;oBACAS;sBACAQ;sBACAP;sBACAC;sBACAO;sBACAH;sBACAC;oBACA;oBACAJ;sBACAZ;sBACAX;sBACAoB;wBACAO;wBACAN;wBACAC;wBACAE;wBACAM;wBACAD;wBACAH;sBACA;oBACA,GACA;sBACAf;sBACAoB;sBACAX;wBACAO;wBACAE;wBACAH;wBACAL;wBACAW;wBACAC;wBACAC;sBACA;oBACA,GACA;sBACAvB;sBACAoB;sBACAX;wBACAO;wBACAE;wBACAH;wBACAL;wBACAW;wBACAE;sBACA;oBACA;kBAEA,GACA;oBACAvB;oBACAX;oBACAoB;sBACAO;sBACAN;sBACAC;sBACAO;sBACAH;sBACAE;sBACAE;oBACA;kBACA;gBAGA,GACA;gBACA;gBACA;gBACA;kBACAK;kBACAC;kBACAC;oBACA9B;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+B;MACA;MACAC;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACAhC;gBACA;cAAA;gBACAiC;gBAAA;gBAAA,OACAL;kBACAK;gBACA;cAAA;gBAAA;gBAAA;gBAFAC;gBAAAR;gBAAA,KAGAQ;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAN;kBACAO;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAKA;IAEA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7NA;AAAA;AAAA;AAAA;AAAunC,CAAgB,0mCAAG,EAAC,C;;;;;;;;;;;ACA3oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "agent/pages/poster/salesman.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/poster/salesman.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./salesman.vue?vue&type=template&id=4bf1d446&\"\nvar renderjs\nimport script from \"./salesman.vue?vue&type=script&lang=js&\"\nexport * from \"./salesman.vue?vue&type=script&lang=js&\"\nimport style0 from \"./salesman.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/poster/salesman.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./salesman.vue?vue&type=template&id=4bf1d446&\"", "var components\ntry {\n  components = {\n    lPainter: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/lime-painter/components/l-painter/l-painter\" */ \"@/uni_modules/lime-painter/components/l-painter/l-painter.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./salesman.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./salesman.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"hideCanvasView\">\r\n\t\t\t<l-painter class=\"hideCanvas\" ref=\"painter\" useCORS />\r\n\t\t</view>\r\n\t\t<block v-if=\"src\">\r\n\t\t\t<image :src=\"src\" class=\"code-img\" @tap=\"previewImage\"></image>\r\n\t\t\t<view class=\"space-max-footer\"></view>\r\n\t\t\t<fix-bottom-button @confirm=\"toPreviewSave\" :text=\"[{text: confirmText,type:'confirm'}]\" bgColor=\"#fff\"\r\n\t\t\t\t:classType=\"2\">\r\n\t\t\t</fix-bottom-button>\r\n\t\t</block>\r\n\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions\r\n\t} from 'vuex';\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tprops: {\r\n\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tconfirmText: '长按上图保存图片',\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tconfirmText: '保存图片至相册',\r\n\t\t\t\t// #endif\r\n\t\t\t\tsrc: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t}),\r\n\t\tasync onLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif (this.$jweixin.isWechat()) {\r\n\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tthis.$util.showLoading()\r\n\t\t\tawait this.getConfigInfo()\r\n\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\tbg: this.primaryColor\r\n\t\t\t})\r\n\t\t\tlet that = this\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthat.renderToCanvas()\r\n\t\t\t}, 1000)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo']),\r\n\t\t\tasync renderToCanvas() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet qr_code = await this.$api.agent.agentInviteQr({\r\n\t\t\t\t\ttype: 2\r\n\t\t\t\t})\r\n\r\n\t\t\t\tlet {\r\n\t\t\t\t\tsalesman_poster = ''\r\n\t\t\t\t} = this.configInfo\r\n\t\t\t\tlet cover = salesman_poster || 'https://lbqny.migugu.com/admin/anmo/mine/salesman-share.png'\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent_name: nickName,\r\n\t\t\t\t} = this.$util.getPage(-1).detail\r\n\t\t\t\tlet {\r\n\t\t\t\t\tavatarUrl\r\n\t\t\t\t} = this.userInfo\r\n\t\t\t\tnickName = nickName.length > 15 ? nickName.substring(0, 15) + '...' : nickName\r\n\t\t\t\tlet qr_radius = '0rpx'\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tqr_radius = '145rpx'\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\tlet poster = {\r\n\t\t\t\t\tcss: {\r\n\t\t\t\t\t\twidth: '750rpx',\r\n\t\t\t\t\t\theight: '1280rpx',\r\n\t\t\t\t\t},\r\n\t\t\t\t\tviews: [{\r\n\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\tsrc: cover,\r\n\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\twidth: '750rpx',\r\n\t\t\t\t\t\t\t\theight: '1140rpx',\r\n\t\t\t\t\t\t\t\tobjectFit: \"cover\",\r\n\t\t\t\t\t\t\t\ttop: '0rpx',\r\n\t\t\t\t\t\t\t\tleft: '0rpx',\r\n\t\t\t\t\t\t\t\tposition: 'absolute'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttype: 'view',\r\n\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\tbackground: '#fff',\r\n\t\t\t\t\t\t\t\twidth: '750rpx',\r\n\t\t\t\t\t\t\t\theight: '140rpx',\r\n\t\t\t\t\t\t\t\tbottom: '0rpx',\r\n\t\t\t\t\t\t\t\tleft: '0rpx',\r\n\t\t\t\t\t\t\t\tposition: 'absolute'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tviews: [{\r\n\t\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\t\tsrc: avatarUrl,\r\n\t\t\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\t\t\tposition: 'absolute',\r\n\t\t\t\t\t\t\t\t\t\twidth: '94rpx',\r\n\t\t\t\t\t\t\t\t\t\theight: '94rpx',\r\n\t\t\t\t\t\t\t\t\t\tobjectFit: \"cover\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: '50rpx',\r\n\t\t\t\t\t\t\t\t\t\tbottom: '23rpx',\r\n\t\t\t\t\t\t\t\t\t\tleft: '20rpx'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\t\ttext: nickName,\r\n\t\t\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\t\t\tposition: 'absolute',\r\n\t\t\t\t\t\t\t\t\t\tbottom: '70rpx',\r\n\t\t\t\t\t\t\t\t\t\tleft: '130rpx',\r\n\t\t\t\t\t\t\t\t\t\twidth: '600rpx',\r\n\t\t\t\t\t\t\t\t\t\tfontSize: '32rpx',\r\n\t\t\t\t\t\t\t\t\t\tfontWeight: '400',\r\n\t\t\t\t\t\t\t\t\t\tcolor: '#000'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\t\ttext: '邀请您成为TA的业务员，扫描二维码立即加入吧!',\r\n\t\t\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\t\t\tposition: 'absolute',\r\n\t\t\t\t\t\t\t\t\t\tbottom: '25rpx',\r\n\t\t\t\t\t\t\t\t\t\tleft: '130rpx',\r\n\t\t\t\t\t\t\t\t\t\twidth: '600rpx',\r\n\t\t\t\t\t\t\t\t\t\tfontSize: '26rpx',\r\n\t\t\t\t\t\t\t\t\t\tcolor: '#999999',\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\tsrc: qr_code,\r\n\t\t\t\t\t\t\tcss: {\r\n\t\t\t\t\t\t\t\tposition: 'absolute',\r\n\t\t\t\t\t\t\t\twidth: '290rpx',\r\n\t\t\t\t\t\t\t\theight: '290rpx',\r\n\t\t\t\t\t\t\t\tbottom: '366rpx',\r\n\t\t\t\t\t\t\t\tleft: '228rpx',\r\n\t\t\t\t\t\t\t\tbackground: '#ffffff',\r\n\t\t\t\t\t\t\t\tborderRadius: qr_radius\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t\t// 渲染\r\n\t\t\t\tthis.$refs.painter.render(poster);\r\n\t\t\t\t// 生成图片\r\n\t\t\t\tthis.$refs.painter.canvasToTempFilePathSync({\r\n\t\t\t\t\tfileType: \"jpg\",\r\n\t\t\t\t\tquality: 1,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tthat.$util.hideAll()\r\n\t\t\t\t\t\tthis.src = res.tempFilePath\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tpreviewImage() {\r\n\t\t\t\tlet finalPath = this.src;\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: finalPath,\r\n\t\t\t\t\turls: [finalPath]\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync saveImage() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\ttype: \"writePhotosAlbum\"\r\n\t\t\t\t});\r\n\t\t\t\tlet filePath = this.src;\r\n\t\t\t\tlet [err, success] = await uni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath\r\n\t\t\t\t})\r\n\t\t\t\tif (err) return;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '保存成功'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoPreviewSave() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tthis.previewImage()\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tthis.saveImage()\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.code-img {\r\n\t\twidth: 750rpx;\r\n\t\theight: 1280rpx;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./salesman.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./salesman.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110368973\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}