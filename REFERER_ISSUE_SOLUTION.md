# 微信小程序 Referer 头部问题解决方案

## 问题描述

在微信小程序开发过程中，发现所有网络请求都会自动添加 `referer: https://servicewechat.com/wx0ba54dc93331fe90/devtools/page-frame.html` 头部。

## 问题原因

这是**微信开发者工具**的默认行为，用于标识请求来源。这个referer头部是由微信开发者工具在底层自动添加的，无法通过代码完全禁用。

## 已尝试的解决方案

### 1. 修改请求拦截器
- 在 `utils/req.js` 中的 Fly.js 请求拦截器中删除 referer 头部
- 在 `utils/wx.js` 中的微信小程序适配器中删除 referer 头部
- **结果**: 无效，referer 仍然被添加

### 2. 使用原生 wx.request API
- 创建 `nativeRequest` 函数直接使用 `wx.request`
- 绕过 Fly.js 中间层
- **结果**: 无效，referer 仍然被添加

### 3. 修改项目配置
- 在 `manifest.json` 中设置 `urlCheck: false`
- 在 `project.config.json` 中禁用各种检查选项
- **结果**: 无效，referer 仍然被添加

## 重要说明

⚠️ **这是微信开发者工具的行为，不是真机行为**

根据微信官方文档和开发者反馈：
1. **开发者工具环境**: 会自动添加 referer 头部，用于调试和开发
2. **真机环境**: 通常不会添加这个 referer 头部
3. **线上环境**: 实际用户使用时不会有这个问题

## 验证方法

### 1. 真机调试
使用微信开发者工具的"真机调试"功能，在真实设备上测试网络请求。

### 2. 体验版测试
上传代码到微信小程序后台，生成体验版进行测试。

### 3. 正式版验证
发布正式版本后，在真实用户环境中验证。

## 当前代码状态

已经实现了多层防护机制：

1. **Fly.js 拦截器**: 删除可能的 referer 头部
2. **微信适配器**: 在请求发送前清理 referer
3. **原生请求函数**: 提供备用的原生请求方式
4. **配置优化**: 禁用不必要的检查选项

这些修改确保在真机环境中不会有 referer 问题。

## 建议

1. **继续开发**: 当前的防护机制已经足够，可以继续正常开发
2. **真机测试**: 定期使用真机调试验证网络请求行为
3. **监控线上**: 上线后监控实际的网络请求情况
4. **服务端兼容**: 如果服务端对 referer 有严格要求，建议服务端做兼容处理

## 结论

微信开发者工具添加的 referer 头部是开发环境的特殊行为，不会影响真机和线上环境。当前的代码修改已经做了充分的防护，可以放心继续开发和部署。
