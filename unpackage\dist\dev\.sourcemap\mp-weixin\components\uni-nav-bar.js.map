{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?70d6", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?be4c", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?2274", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?fec0", "uni-app:///components/uni-nav-bar.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?3fbc", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/uni-nav-bar.vue?36d2"], "names": ["components", "uniStatusBar", "props", "title", "type", "default", "leftIcon", "leftText", "rightText", "fixed", "color", "backgroundColor", "onlyLeft", "statusBar", "shadow", "computed", "isFixed", "insertStatusBar", "<PERSON><PERSON><PERSON><PERSON>", "data", "navBarHeight", "methods", "onClickLeft", "url", "openType", "onClickRight"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;gBC2BlzB;EACAA;IACAC;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;AACA;AACA;IACAG;MACAJ;MACAC;IACA;IACA;AACA;AACA;IACAI;MACAL;MACAC;IACA;IACA;AACA;AACA;IACAK;MACAN;MACAC;IACA;IACA;AACA;AACA;IACAM;MACAP;MACAC;IACA;IACA;AACA;AACA;IACAO;MACAR;MACAC;IACA;IACA;AACA;AACA;IACAQ;MACAT;MACAC;IACA;IACA;AACA;AACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;EACA;EACAU;IACAC;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA,qBAEA,KADAhB;QAAAA;MAEA;QACA;UACAiB;UACAC;QACA;MACA;QACA;UACAD;UACAC;QACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvKA;AAAA;AAAA;AAAA;AAA69C,CAAgB,i7CAAG,EAAC,C;;;;;;;;;;;ACAj/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-nav-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-nav-bar.vue?vue&type=template&id=4f3ecca1&\"\nvar renderjs\nimport script from \"./uni-nav-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-nav-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-nav-bar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-nav-bar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=template&id=4f3ecca1&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.onlyLeft ? _vm.title.length : null\n  var g1 = !_vm.onlyLeft ? _vm.rightText.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-navbar\" :class=\"{'uni-navbar-fixed':isFixed,'uni-navbar-shadow':hasShadow}\"\r\n\t\t:style=\"{backgroundColor:backgroundColor,height:navBarHeight+'px'}\">\r\n\t\t<uni-status-bar v-if=\"insertStatusBar\"></uni-status-bar>\r\n\t\t<view class=\"uni-navbar-header\" :style=\"{color:color}\">\r\n\t\t\t<view class=\"uni-navbar-header-btns left\" @tap=\"onClickLeft\">\r\n\t\t\t\t<slot name=\"left\"></slot>\r\n\t\t\t\t<view v-if=\"leftIcon || leftText\" class=\"uni-navbar-btn-text\"><i class=\"iconfont\" :class=\"leftIcon\"\r\n\t\t\t\t\t\tv-if=\"leftIcon\"></i>{{leftText || ''}}</view>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"!onlyLeft\">\r\n\t\t\t\t<view class=\"uni-navbar-container\">\r\n\t\t\t\t\t<view v-if=\"title.length\" class=\"uni-navbar-container-title\">{{title}}</view>\r\n\t\t\t\t\t<!-- 标题插槽 -->\r\n\t\t\t\t\t<slot></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-navbar-header-btns right\" @tap=\"onClickRight\">\r\n\t\t\t\t\t<view v-if=\"rightText.length\" class=\"uni-navbar-btn-text\">{{rightText}}</view>\r\n\t\t\t\t\t<slot name=\"right\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uniStatusBar from '@/components/uni-status-bar.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniStatusBar\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 标题文字\r\n\t\t\t */\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 左侧按钮图标\r\n\t\t\t */\r\n\t\t\tleftIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 左侧按钮文本\r\n\t\t\t */\r\n\t\t\tleftText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 右侧按钮文本\r\n\t\t\t */\r\n\t\t\trightText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 是否固定在顶部\r\n\t\t\t */\r\n\t\t\tfixed: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 按钮图标和文字颜色\r\n\t\t\t */\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#000000'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 背景颜色\r\n\t\t\t */\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FFFFFF'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 是否仅有左侧\r\n\t\t\t */\r\n\t\t\tonlyLeft: {\r\n\t\t\t\ttype: [Boolean],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 是否包含状态栏，默认固定在顶部时包含\r\n\t\t\t */\r\n\t\t\tstatusBar: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 是否使用阴影，默认根据背景色判断\r\n\t\t\t */\r\n\t\t\tshadow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisFixed() {\r\n\t\t\t\treturn String(this.fixed) === 'true'\r\n\t\t\t},\r\n\t\t\tinsertStatusBar() {\r\n\t\t\t\tswitch (String(this.statusBar)) {\r\n\t\t\t\t\tcase 'true':\r\n\t\t\t\t\t\treturn true\r\n\t\t\t\t\tcase 'false':\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn this.isFixed\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thasShadow() {\r\n\t\t\t\tvar backgroundColor = this.backgroundColor\r\n\t\t\t\tswitch (this.shadow) {\r\n\t\t\t\t\tcase true:\r\n\t\t\t\t\t\treturn true\r\n\t\t\t\t\tcase false:\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn backgroundColor !== 'transparent' && backgroundColor.indexOf('rgba') < 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavBarHeight: uni.getSystemInfoSync().statusBarHeight * 1 + 44\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 左侧按钮点击事件\r\n\t\t\t */\r\n\t\t\tonClickLeft() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tleftIcon = ''\r\n\t\t\t\t} = this\r\n\t\t\t\tif (leftIcon == 'icon-left') {\r\n\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\turl: 1,\r\n\t\t\t\t\t\topenType: `navigateBack`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (leftIcon == 'iconshouye11') {\r\n\t\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\t\turl: `/pages/service`,\r\n\t\t\t\t\t\topenType: 'reLaunch'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('clickLeft')\r\n\t\t\t\t\tthis.$emit('click-left')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 右侧按钮点击事件\r\n\t\t\t */\r\n\t\t\tonClickRight() {\r\n\t\t\t\tthis.$emit('clickRight')\r\n\t\t\t\tthis.$emit('click-right')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-navbar {\r\n\t\tdisplay: block;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\t/* background-color: #FFFFFF; */\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-navbar-shadow {\r\n\t\tbox-shadow: 0 1px 6px #ccc;\r\n\t}\r\n\r\n\t.uni-navbar.uni-navbar-fixed {\r\n\t\tposition: fixed;\r\n\t\tz-index: 9999;\r\n\t}\r\n\r\n\t.uni-navbar-header {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\twidth: 100%;\r\n\t\t/* #ifdef MP-BAIDU */\r\n\t\theight: 38px;\r\n\t\tline-height: 38px;\r\n\t\tfont-size: 15px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef MP-BAIDU */\r\n\t\theight: 44px;\r\n\t\tline-height: 44px;\r\n\t\tfont-size: 16px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-navbar-header-btns {\r\n\t\tdisplay: inline-flex;\r\n\t\tflex-wrap: nowrap;\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 100px;\r\n\t}\r\n\r\n\t.uni-navbar-header-btns.left {\r\n\t\tpadding-left: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-navbar-header-btns.right {\r\n\t\tpadding-right: 30rpx;\r\n\t}\r\n\r\n\t.uni-navbar-container {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 5px;\r\n\t}\r\n\r\n\t.uni-navbar-container-title {\r\n\t\t/* #ifdef MP-BAIDU */\r\n\t\theight: 38px;\r\n\t\tline-height: 38px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef MP-BAIDU */\r\n\t\theight: 44px;\r\n\t\tline-height: 44px;\r\n\t\t/* #endif */\r\n\t\tfont-size: 15px;\r\n\t\ttext-align: center;\r\n\t\t/* #ifdef MP-WEIXIN */\r\n\t\tpadding-right: 30px;\r\n\t\t/* #endif */\r\n\t\tmax-width: 360rpx;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110363775\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}