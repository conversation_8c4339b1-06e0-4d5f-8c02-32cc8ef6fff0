{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?58dc", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?c8bb", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?d564", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?7966", "uni-app:///components/timeline.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?1309", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/timeline.vue?f78b"], "names": ["name", "props", "list", "type", "default", "info", "data", "computed", "primaryColor", "subColor", "configInfo", "methods", "toPreviewImage", "curent", "urls", "toMap", "privacyCheck", "arr", "uni", "latitude", "longitude", "scale", "toSign", "id", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA2xB,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyD/yB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAD;MACAA;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA,QAEA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACA;QACAC;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIAC;gBAEAZ,OACA,MADAA;gBAAA;gBAAA,OAEA;kBACAF;gBACA;cAAA;gBAAA;gBAAA,OACAe;kBACAf;gBACA;cAAA;gBAAA;gBAAA,OACAe;kBACAC;kBACAC;kBACApB;kBACAqB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA,IACAC,KACA,UADAA;MAEA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxIA;AAAA;AAAA;AAAA;AAA09C,CAAgB,86CAAG,EAAC,C;;;;;;;;;;;ACA9+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/timeline.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./timeline.vue?vue&type=template&id=11088008&\"\nvar renderjs\nimport script from \"./timeline.vue?vue&type=script&lang=js&\"\nexport * from \"./timeline.vue?vue&type=script&lang=js&\"\nimport style0 from \"./timeline.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/timeline.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./timeline.vue?vue&type=template&id=11088008&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./timeline.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./timeline.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='record-box fill-base'>\r\n\t\t\t<view class='record-item rel ml-sm b-1px-l' :style=\"{padding:index==list.length -1?'0 0 0 30rpx':''}\"\r\n\t\t\t\tv-for=\"(item,index) in list\" :key=\"index\">\r\n\r\n\t\t\t\t<text class=\"item-tag abs\" :class=\"[{'cur':info[item.time]}]\"\r\n\t\t\t\t\t:style=\"{border:`2rpx solid ${primaryColor}`,background: info[item.time] ? primaryColor : ''}\"></text>\r\n\r\n\t\t\t\t<view class='c-title'>\r\n\t\t\t\t\t<view class=\"item-text f-paragraph flex-y-baseline\">\r\n\t\t\t\t\t\t{{item.title}}\r\n\t\t\t\t\t\t<view class=\"ml-md f-caption c-caption\">\r\n\t\t\t\t\t\t\t{{info[item.time]? '' :item.title == '签字确认' && !info.sign_img ? '暂未签字确认' :  '状态未开始' }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"c-caption\" v-if=\"info[item.time]\">\r\n\t\t\t\t\t\t{{info[item.time]}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @tap.stop=\"toMap(item.addr)\" class=\"flex-y-center mt-md f-caption c-title\"\r\n\t\t\t\t\tv-if=\"item.addr && info[item.addr]\">\r\n\t\t\t\t\t<i class=\"iconfont iconjuli mr-sm\" :style=\"{color:primaryColor}\"></i>{{info[item.addr]}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<block\r\n\t\t\t\t\tv-if=\"item.img && info[item.img] && (!type || (type && (item.title != '签字确认' && !configInfo.hide_coach_image) || item.title== '签字确认'))\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view @tap.stop=\"toPreviewImage(item.img)\" class=\"item-img mt-md radius-5\">\r\n\t\t\t\t\t\t<view class=\"h5-image item-img mt-md radius-5\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${info[item.img]}')`}\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<image @tap.stop=\"toPreviewImage(item.img)\" mode=\"widthFix\" class=\"item-img mt-md radius-5\"\r\n\t\t\t\t\t\t:src=\"info[item.img]\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</block> \r\n\t\t\t\t<view class=\"flex-between\"\r\n\t\t\t\t\tv-if=\"type && !info.is_add && item.title== '签字确认' && info.order_end_time && !info.sign_img\">\r\n\t\t\t\t\t<view @tap.stop=\"toSign\" class=\"item-btn flex-center mt-md c-base radius\"\r\n\t\t\t\t\t\t:style=\"{background:primaryColor}\">\r\n\t\t\t\t\t\t签字确认\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tname: 'timeline',\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinfo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttype: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\ttoPreviewImage(key) {\r\n\t\t\t\tlet curent = this.info[key]\r\n\t\t\t\tthis.$util.previewImage({\r\n\t\t\t\t\tcurent,\r\n\t\t\t\t\turls: [curent]\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 查看定位\r\n\t\t\tasync toMap(key) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet arr = key.split('_')[0]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tinfo\r\n\t\t\t\t} = this\r\n\t\t\t\tawait this.$util.checkAuth({\r\n\t\t\t\t\ttype: 'userLocation'\r\n\t\t\t\t})\r\n\t\t\t\tawait uni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t})\r\n\t\t\t\tawait uni.openLocation({\r\n\t\t\t\t\tlatitude: info[`${arr}_lat`] * 1,\r\n\t\t\t\t\tlongitude: info[`${arr}_lng`] * 1,\r\n\t\t\t\t\tname: info[key],\r\n\t\t\t\t\tscale: 28\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoSign() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid\r\n\t\t\t\t} = this.info\r\n\t\t\t\tlet url = `/user/pages/order/sign?id=${id}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.record-item {\r\n\t\tpadding: 0 0 30rpx 30rpx;\r\n\r\n\t\t.item-tag {\r\n\t\t\twidth: 14px;\r\n\t\t\theight: 14px;\r\n\t\t\tdisplay: block;\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: -7px;\r\n\t\t\ttransform: rotateZ(360deg);\r\n\t\t}\r\n\r\n\t\t.item-text {\r\n\t\t\tline-height: 34rpx;\r\n\t\t}\r\n\r\n\t\t.item-img {\r\n\t\t\twidth: 180rpx;\r\n\t\t\tmin-height: 120rpx;\r\n\t\t}\r\n\r\n\t\t.item-btn {\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 64rpx;\r\n\t\t\tbackground: #EEEEEE;\r\n\t\t}\r\n\t}\r\n\r\n\t.record-item.b-1px-l::before {\r\n\t\tborder-left: 2px solid #ccc;\r\n\t}\r\n\r\n\t.record-item:last-child {\r\n\t\tpadding-bottom: 0;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./timeline.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./timeline.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110364870\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}