{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?c865", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?c85b", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?3f51", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?e533", "uni-app:///components/wfalls-flow.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?ca67", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/wfalls-flow.vue?adee"], "names": ["props", "list", "type", "path", "data", "viewList", "everyNum", "mounted", "computed", "primaryColor", "subColor", "have", "methods", "init", "setTimeout", "handleViewRender", "query", "listFlag", "goDetail", "id", "page_url", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmClzB;AAGA;AAAA;AAAA,eACA;EACAA;IACAC;MACAC;IACA;;IACAC;MACAD;IACA;EACA;EACAE;IACA;MACAC;QACAJ;MACA;QACAA;MACA;MACA;MACAK;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC,yCACA;IACAC;MAAA;MACA;QACAZ;MACA;QACAA;MACA;MACAa;QACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;QACA;QACA;QACA;MACA;MAAA;MACA;MACA;MACAC;QACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC,KACA,2BADAA;gBAGAhB,OACA,OADAA;gBAEAiB;gBACAC;gBACA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAA69C,CAAgB,i7CAAG,EAAC,C;;;;;;;;;;;ACAj/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/wfalls-flow.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wfalls-flow.vue?vue&type=template&id=6acf564f&\"\nvar renderjs\nimport script from \"./wfalls-flow.vue?vue&type=script&lang=js&\"\nexport * from \"./wfalls-flow.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wfalls-flow.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/wfalls-flow.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wfalls-flow.vue?vue&type=template&id=6acf564f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wfalls-flow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wfalls-flow.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wf-list-container\">\r\n\t\t<view id=\"wf-list\" class=\"wf-list\" v-for=\"(list,listIndex) of viewList\" :key=\"listIndex\">\r\n\t\t\t<view @tap=\"goDetail(listIndex,index)\" :data-id=\"item.id\" class=\"wf-item-child rel\"\r\n\t\t\t\tv-for=\"(item,index) of list.list\" :key=\"index\">\r\n\t\t\t\t<view class=\"examin-btn flex-center f-icontext c-base radius abs\"\r\n\t\t\t\t\t:style=\"{background:item.status==1?'#FC8218':'#FF6262'}\" v-if=\"path==2&& item.status!=2\">\r\n\t\t\t\t\t{{item.status==1?'审核中':'已驳回'}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<image mode=\"widthFix\" class=\"cover\" :id=\"'id'+item.id\" @load=\"handleViewRender\"\r\n\t\t\t\t\t@error=\"handleViewRender\" :src=\"item.cover\"></image>\r\n\t\t\t\t<view class=\"play-video-info flex-center c-base abs\" v-if=\"item.type == 2\">\r\n\t\t\t\t\t<view class=\"play-video flex-center c-base radius\">\r\n\t\t\t\t\t\t<i class=\"iconfont icon-play-video\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"wf-item\">\r\n\t\t\t\t\t<view class=\"f-desc c-black text-bold\">{{item.title}}</view>\r\n\t\t\t\t\t<view class=\"flex-between mt-sm\">\r\n\t\t\t\t\t\t<view class=\"flex-center\">\r\n\t\t\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar\" :src=\"item.work_img\"></image>\r\n\t\t\t\t\t\t\t<view class=\"coach f-caption c-desc ellipsis\">{{item.coach_name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-y-baseline f-caption c-desc\"> <i class=\"iconfont iconjuli\"\r\n\t\t\t\t\t\t\t\t:style=\"{color:primaryColor}\"></i>\r\n\t\t\t\t\t\t\t{{item.distance}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions\r\n\t} from 'vuex';\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array, //实际请求获取的列表数据\r\n\t\t\t},\r\n\t\t\tpath: {\r\n\t\t\t\ttype: String || Number, //跳转页面类型  1用户端；2技-师端\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tviewList: [{\r\n\t\t\t\t\tlist: []\r\n\t\t\t\t}, {\r\n\t\t\t\t\tlist: []\r\n\t\t\t\t}],\r\n\t\t\t\t//展示到视图的列表数据\r\n\t\t\t\teveryNum: 2\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tif (this.list.length) {\r\n\t\t\t\tthis.init()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\thave: state => state.config.configInfo.subColor,\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\t...mapActions(),\r\n\t\t\tinit() {\r\n\t\t\t\tthis.viewList = [{\r\n\t\t\t\t\tlist: []\r\n\t\t\t\t}, {\r\n\t\t\t\t\tlist: []\r\n\t\t\t\t}];\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.handleViewRender(0, 0)\r\n\t\t\t\t}, 500)\r\n\t\t\t},\r\n\t\t\thandleViewRender(x, y) {\r\n\t\t\t\tconst index = this.viewList.reduce((total, current) => total + current.list.length, 0)\r\n\t\t\t\tif (index > this.list.length - 1) {\r\n\t\t\t\t\t// 加载完成触发事件并返回加载过的图片数\r\n\t\t\t\t\tthis.$emit('finishLoad', index)\r\n\t\t\t\t\treturn\r\n\t\t\t\t};\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tlet listFlag = 0;\r\n\t\t\t\tquery.selectAll('#wf-list').boundingClientRect(data => {\r\n\t\t\t\t\tlistFlag = data[0].bottom - data[1].bottom <= 0 ? 0 : 1;\r\n\t\t\t\t\tthis.viewList[listFlag].list.push(this.list[index])\r\n\t\t\t\t}).exec()\r\n\t\t\t},\r\n\t\t\tasync goDetail(a, b) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid\r\n\t\t\t\t} = this.viewList[a].list[b]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tpath\r\n\t\t\t\t} = this\r\n\t\t\t\tlet page_url = path == 1 ? `` : `technician/`\r\n\t\t\t\tlet url = `/dynamic/pages/${page_url}detail?id=${id}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl,\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.wf-list-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.wf-list {\r\n\t\twidth: calc(50% - 10rpx);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.wf-item-child {\r\n\t\tbackground: white;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.examin-btn {\r\n\t\t\ttop: 20rpx;\r\n\t\t\tleft: 15rpx;\r\n\t\t\twidth: 89rpx;\r\n\t\t\theight: 37rpx;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.play-video-info {\r\n\t\t\ttop: 0rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: calc(100% - 128rpx);\r\n\t\t\tz-index: 9;\r\n\r\n\t\t\t.play-video {\r\n\t\t\t\twidth: 66rpx;\r\n\t\t\t\theight: 66rpx;\r\n\t\t\t\tbackground: rgba(2, 2, 2, 0.5);\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.cover {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t.wf-item {\r\n\t\tpadding: 20rpx;\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tmargin-right: 6rpx;\r\n\t\t}\r\n\r\n\t\t.coach {\r\n\t\t\tmax-width: 100rpx;\r\n\t\t}\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wfalls-flow.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./wfalls-flow.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110369543\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}