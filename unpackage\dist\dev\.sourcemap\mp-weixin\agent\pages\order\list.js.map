{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/list.vue?31a0", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/list.vue?479c", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/list.vue?f9a2", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/agent/pages/order/list.vue?e5c9", "uni-app:///agent/pages/order/list.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "isLoad", "options", "activeIndex", "tabList", "tabOrderList", "title", "id", "number", "tabBellList", "statusType", "technicianStatusOperType", "param", "page", "pay_type", "order_code", "list", "loading", "index", "lockTap", "popupInfo", "type", "imgs", "location", "lat", "lng", "address", "computed", "primaryColor", "subColor", "configInfo", "userInfo", "onLoad", "agent", "bell", "tab", "uni", "onPullDownRefresh", "onReachBottom", "methods", "initIndex", "refresh", "bg", "initRefresh", "toSearch", "getList", "flag", "oldList", "<PERSON><PERSON><PERSON>", "newList", "handerTabChange", "toConfirm", "order_id", "can_refund_price", "is_add", "store_id", "add_flow_path", "text_type", "confirmChangeOrder", "text", "len", "add_service", "content", "confirmText", "res_del", "confirm", "msg", "key", "val", "setTimeout", "goDetail", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;;;AAGnD;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAAuxB,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACoG3yB;AAIA;AAAA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MACAC;QACAH;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MACAE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAhB;MACA;MACAiB;MACAC;MACAC;MACAC;QACAd;QACAY;QACAG;QACAT;QACAU;QACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACA,qBAIA9B,QAHA+B;MAAAA;MAAA,gBAGA/B,QAFAgC;MAAAA;MAAA,eAEAhC,QADAiC;MAAAA;IAEAjC;IACAA;IACA;IACAkC;MACA9B;IACA;IACA;IACA;IACA;EACA;EACA+B;IAEAD;IAEA;IACAA;EACA;EACAE;IACA;IACA;IACA;IACA;EACA;EACAC,uDACA,2CACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MASA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAEAC,UAIA,OAJA/B,MACAJ,QAGA,OAHAA,OACAR,UAEA,OAFAA,SACAD,cACA,OADAA;gBAAA,iBAMA,oDAHAgC,wGACAD,6GACAD;gBAEAe;gBACApC;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAqC;gBAEA;kBACA;oBACA;kBACA;oBACAA;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,wBAQA,yBANAC,qCACAtC,2CACAuC,0GACAC,gIACAC,oIACAC;gBAEAnC,qFACAkC,YACA;gBACA;kBACArC;kBACAkC;kBACA/B;kBACAoC;kBACAJ;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA9C;gBAEAM,QAGAN,MAHAM,OACAG,OAEAT,MAFAS,MACAoC,YACA7C,MADA6C;gBAEA7C;gBAEA0C,SACA,wBADAA;gBAEAK;gBACAC;gBAAA,MACAhD;kBAAA;kBAAA;gBAAA;gBAEAqB,SACA,eADAA;gBAEAe;gBAAA;gBAAA,OAIA;kBACAzC;gBACA;cAAA;gBAAA;gBAJAO;gBAAA,+CACA+C;gBAAAA;gBAIAD;gBACA;kBACAC;oBACAF;kBACA;gBACA;cAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAGAvB;kBACA0B;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBALAC;gBACAC;gBAAA,IAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAEA9D,cAEA,OAFAA,aACAQ,2BACA,OADAA;gBAEAuD;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAEAjC,QACA,eADAA;gBAEAe;gBAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBACA;kBACA1C;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACA6D;kBACAC;gBACA;gBAAA,KACAjE;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEAkE;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;kBACAF;kBACAC;gBACA;gBACAC;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA,IACA/D,KACA,sBADAA;MAEA,IACA0B,QACA,aADAA;MAEA;MACA;QACAsC;MACA;IACA;EAAA;AAEA;AAAA,2B", "file": "agent/pages/order/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './agent/pages/order/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=c4163e7c&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"agent/pages/order/list.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=c4163e7c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.isLoad\n    ? _vm.__map(_vm.list.data, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = !(item.pay_type == 2)\n          ? [3, 4, 5].includes(item.pay_type)\n          : null\n        var m0 = _vm.$t(\"action.attendantName\")\n        var g1 =\n          ([2, 3, 4, 5, 6, 8].includes(item.pay_type) && !item.is_add) ||\n          ((([2, 3, 6].includes(item.pay_type) && item.add_flow_path === 1) ||\n            (item.pay_type == 2 && item.add_flow_path == 2)) &&\n            item.is_add)\n        var m1 = g1\n          ? _vm.$t(\n              \"action.\" +\n                _vm.technicianStatusOperType[\n                  item.pay_type === 3 && (item.store_id || item.is_add)\n                    ? 5\n                    : item.pay_type == 8\n                    ? -1\n                    : item.pay_type\n                ]\n            )\n          : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          m0: m0,\n          g1: g1,\n          m1: m1,\n        }\n      })\n    : null\n  var g2 =\n    _vm.isLoad && _vm.loading\n      ? _vm.list.current_page >= _vm.list.last_page && _vm.list.data.length > 0\n      : null\n  var g3 = _vm.isLoad\n    ? !_vm.loading && _vm.list.data.length <= 0 && _vm.list.current_page == 1\n    : null\n  var m2 = _vm.isLoad\n    ? _vm.$t(\"action.\" + _vm.technicianStatusOperType[_vm.popupInfo.text_type])\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      $event.stopPropagation()\n      return _vm.$util.goUrl({\n        url:\n          \"/agent/pages/order/change?id=\" +\n          item.id +\n          \"&agent=\" +\n          _vm.options.agent,\n      })\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      return _vm.$refs.change_item.close()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order-pages\" v-if=\"isLoad\">\r\n\t\t<fixed>\r\n\t\t\t<view class=\"fill-base pt-lg pl-md pr-md pb-md\">\r\n\t\t\t\t<search @input=\"toSearch\" type=\"input\" :padding=\"0\" :radius=\"30\" height=\"70rpx\"\r\n\t\t\t\t\tplaceholder=\"请输入系统订单号查询\">\r\n\t\t\t\t</search>\r\n\t\t\t</view>\r\n\t\t\t<tab @change=\"handerTabChange\" :list=\"tabList\" :activeIndex=\"activeIndex*1\" :activeColor=\"primaryColor\"\r\n\t\t\t\theight=\"100rpx\" v-if=\"options.tab != 8\"></tab>\r\n\t\t\t<view class=\"b-1px-b\"></view>\r\n\t\t</fixed>\r\n\r\n\t\t<view @tap.stop=\"goDetail(index)\" class=\"item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16 rel\"\r\n\t\t\tv-for=\"(item,index) in list.data\" :key=\"index\">\r\n\t\t\t<view v-if=\"item.is_add\">\r\n\t\t\t\t<view class=\"bell-tag flex-center f-icontext c-base abs\" :style=\"{background:primaryColor}\">加钟服务\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"space-md\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between pb-lg b-1px-b\">\r\n\t\t\t\t<view class=\"f-paragraph c-title max-450 ellipsis\">订单号：{{item.order_code}}</view>\r\n\t\t\t\t<view class=\"f-caption text-bold\"\r\n\t\t\t\t\t:style=\"{color:item.pay_type==2?primaryColor: [3,4,5].includes(item.pay_type)?subColor: item.pay_type == 6 ? '#11C95E' : '#333'}\">\r\n\t\t\t\t\t{{statusType[item.pay_type]}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-center mb-lg\" :class=\"[{'mt-lg':aindex==0}]\" v-for=\"(aitem,aindex) in item.order_goods\"\r\n\t\t\t\t:key=\"aindex\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"avatar lg radius-16\">\r\n\t\t\t\t\t<view class=\"h5-image avatar lg radius-16\"\r\n\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${aitem.goods_cover}')`}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<image mode=\"aspectFill\" class=\"avatar lg radius-16\" :src=\"aitem.goods_cover\"></image>\r\n\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t<view class=\"flex-1 ml-md\">\r\n\t\t\t\t\t<view class=\"flex-between\">\r\n\t\t\t\t\t\t<view class=\"f-mini-title c-title text-bold max-380 ellipsis\">\r\n\t\t\t\t\t\t\t{{aitem.goods_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"c-paragraph\">x{{aitem.num}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-between mt-md\">\r\n\t\t\t\t\t\t<view class=\"f-caption c-caption ellipsis\"\r\n\t\t\t\t\t\t\t:class=\"[{'max-300':aitem.refund_num>0},{'max-450':aitem.refund_num==0}]\">\r\n\t\t\t\t\t\t\t服务{{$t('action.attendantName')}}：{{item.coach_info?item.coach_info.coach_name:'-'}}</view>\r\n\t\t\t\t\t\t<view class=\"f-caption c-warning\" v-if=\"aitem.refund_num>0\">已退x{{aitem.refund_num}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f-caption c-caption\" style=\"margin-top: 5rpx;\">服务时间：{{item.start_time}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex-between pt-lg b-1px-t\">\r\n\t\t\t\t<view class=\"flex-y-center f-desc c-title\">总计：\r\n\t\t\t\t\t<view class=\"f-paragraph text-bold\">¥{{item.pay_price}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-warp\">\r\n\t\t\t\t\t<!-- // pay_type 2待接单，3接单，4出发，5到达，6服务中，7已完成 -->\r\n\t\t\t\t\t<block\r\n\t\t\t\t\t\tv-if=\"([2, 3, 4, 5, 6, 8].includes(item.pay_type) && !item.is_add)|| ((([2, 3, 6].includes(item.pay_type) && item.add_flow_path === 1) || (item.pay_type==2&&item.add_flow_path==2)) && item.is_add)\">\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\**********=\"$util.goUrl({url:`/agent/pages/order/change?id=${item.id}&agent=${options.agent}`})\"\r\n\t\t\t\t\t\t\tclass=\"clear-btn order\" style=\"margin-left: 0;\" v-if=\"!item.is_add\">转单</button>\r\n\t\t\t\t\t\t<button @tap.stop=\"toConfirm(index)\" class=\"clear-btn order\"\r\n\t\t\t\t\t\t\t:style=\"{color:'#fff',background:primaryColor,borderColor:primaryColor}\">{{ $t( `action.${ technicianStatusOperType[ (item.pay_type === 3 && ( item.store_id || item.is_add)) ? 5 : item.pay_type == 8 ? -1 : item.pay_type ] }` ) }}</button>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<load-more :noMore=\"list.current_page>=list.last_page&&list.data.length>0\" :loading=\"loading\" v-if=\"loading\">\r\n\t\t</load-more>\r\n\t\t<abnor v-if=\"!loading&&list.data.length<=0&&list.current_page==1\"></abnor>\r\n\r\n\t\t<view class=\"space-footer\"></view>\r\n\r\n\t\t<uni-popup ref=\"change_item\" type=\"center\" :custom=\"true\">\r\n\t\t\t<view class=\"common-popup-content fill-base pd-lg radius-34\">\r\n\t\t\t\t<view class=\"title\">温馨提示</view>\r\n\t\t\t\t<view class=\"desc\">\r\n\t\t\t\t\t你确认要操作{{$t(`action.${technicianStatusOperType[popupInfo.text_type]}`)}}吗?\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"f-caption c-warning\" v-if=\"popupInfo.type == -1\">退款金额：¥{{popupInfo.can_refund_price}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t<view @tap.stop=\"$refs.change_item.close()\" class=\"item-child\">取消</view>\r\n\t\t\t\t\t<view @tap.stop=\"confirmChangeOrder\" class=\"item-child c-base\"\r\n\t\t\t\t\t\t:style=\"{background: primaryColor,color:'#fff'}\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\toptions: {},\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\ttabList: [],\r\n\t\t\t\ttabOrderList: [{\r\n\t\t\t\t\ttitle: '全部',\r\n\t\t\t\t\tid: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已取消',\r\n\t\t\t\t\tid: -1,\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '待接单',\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已接单',\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已出发',\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已到达',\r\n\t\t\t\t\tid: 5,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '服务中',\r\n\t\t\t\t\tid: 6,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已完成',\r\n\t\t\t\t\tid: 7,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\ttabBellList: [{\r\n\t\t\t\t\ttitle: '全部',\r\n\t\t\t\t\tid: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已取消',\r\n\t\t\t\t\tid: -1,\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '待接单',\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已接单',\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '服务中',\r\n\t\t\t\t\tid: 6,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已完成',\r\n\t\t\t\t\tid: 7,\r\n\t\t\t\t\tnumber: 0\r\n\t\t\t\t}],\r\n\t\t\t\tstatusType: {\r\n\t\t\t\t\t'-1': '已取消',\r\n\t\t\t\t\t1: '待支付',\r\n\t\t\t\t\t2: '待接单',\r\n\t\t\t\t\t3: '已接单',\r\n\t\t\t\t\t4: '已出发',\r\n\t\t\t\t\t5: '已到达',\r\n\t\t\t\t\t6: '服务中',\r\n\t\t\t\t\t7: '已完成',\r\n\t\t\t\t\t8: '待转单'\r\n\t\t\t\t},\r\n\t\t\t\ttechnicianStatusOperType: {\r\n\t\t\t\t\t'-1': 'agreeRefund',\r\n\t\t\t\t\t2: 'orderTaking',\r\n\t\t\t\t\t3: 'setOut',\r\n\t\t\t\t\t4: 'arrive',\r\n\t\t\t\t\t5: 'startService',\r\n\t\t\t\t\t6: 'serviceCompletion',\r\n\t\t\t\t\t8: 'transferOrder'\r\n\t\t\t\t},\r\n\t\t\t\tparam: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tpay_type: 0,\r\n\t\t\t\t\torder_code: ''\r\n\t\t\t\t},\r\n\t\t\t\tlist: {\r\n\t\t\t\t\tdata: []\r\n\t\t\t\t},\r\n\t\t\t\tloading: true,\r\n\t\t\t\tindex: -1,\r\n\t\t\t\tlockTap: false,\r\n\t\t\t\tpopupInfo: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tindex: '',\r\n\t\t\t\t\ttype: '',\r\n\t\t\t\t\tparam: {},\r\n\t\t\t\t\timgs: [],\r\n\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\tlat: 0,\r\n\t\t\t\t\t\tlng: 0,\r\n\t\t\t\t\t\taddress: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tconfigInfo: state => state.config.configInfo,\r\n\t\t\tuserInfo: state => state.user.userInfo,\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tlet {\r\n\t\t\t\tagent = 0,\r\n\t\t\t\t\tbell = 0,\r\n\t\t\t\t\ttab = 0,\r\n\t\t\t} = options\r\n\t\t\toptions.agent = agent * 1\r\n\t\t\toptions.bell = bell * 1\r\n\t\t\tthis.options = options\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: tab == 8 ? '拒单管理' : bell == 1 ? '加钟管理' : '订单管理'\r\n\t\t\t})\r\n\t\t\tthis.tabList = tab == 8 ? [] : bell == 1 ? this.tabBellList : this.tabOrderList\r\n\t\t\tthis.activeIndex = tab\r\n\t\t\tthis.initIndex()\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.showNavigationBarLoading()\r\n\t\t\t// #endif\r\n\t\t\tthis.initRefresh();\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.list.current_page >= this.list.last_page || this.loading) return;\r\n\t\t\tthis.param.page = this.param.page + 1;\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getConfigInfo']),\r\n\t\t\t...mapMutations(['updateTechnicianItem']),\r\n\t\t\tasync initIndex(refresh = false) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!refresh && this.$jweixin.isWechat()) {\r\n\t\t\t\t\tawait this.$jweixin.initJssdk();\r\n\t\t\t\t\tthis.$jweixin.wxReady(() => {\r\n\t\t\t\t\t\tthis.$jweixin.hideOptionMenu()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!this.configInfo.id || refresh) {\r\n\t\t\t\t\tawait this.getConfigInfo()\r\n\t\t\t\t}\r\n\t\t\t\tawait this.getList()\r\n\t\t\t\tthis.isLoad = true\r\n\t\t\t\tthis.$util.setNavigationBarColor({\r\n\t\t\t\t\tbg: this.primaryColor\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinitRefresh() {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.initIndex(true)\r\n\t\t\t},\r\n\t\t\ttoSearch(val) {\r\n\t\t\t\tthis.param.page = 1\r\n\t\t\t\tthis.param.order_code = val\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tasync getList(flag = false) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tlist: oldList,\r\n\t\t\t\t\tparam,\r\n\t\t\t\t\ttabList,\r\n\t\t\t\t\tactiveIndex,\r\n\t\t\t\t} = this\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttab = 0,\r\n\t\t\t\t\t\tbell = 0,\r\n\t\t\t\t\t\tagent = 0\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\tparam.pay_type = tab == 8 ? 8 : tabList[activeIndex].id\r\n\t\t\t\tif (tab != 8) {\r\n\t\t\t\t\tparam.is_add = bell\r\n\t\t\t\t}\r\n\t\t\t\tlet newList = await this.$api[methodKey].orderList(param)\r\n\r\n\t\t\t\tif (!flag) {\r\n\t\t\t\t\tif (this.param.page == 1) {\r\n\t\t\t\t\t\tthis.list = newList\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tnewList.data = oldList.data.concat(newList.data)\r\n\t\t\t\t\t\tthis.list = newList\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = false\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t},\r\n\t\t\thanderTabChange(index) {\r\n\t\t\t\tthis.activeIndex = index;\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tthis.param.page = 1;\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\t// type: -1拒绝接单，3确定接单，4已出发，5已到达，6开始服务，7已完成\r\n\t\t\tasync toConfirm(index) {\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid: order_id,\r\n\t\t\t\t\tpay_type,\r\n\t\t\t\t\tcan_refund_price,\r\n\t\t\t\t\tis_add = 0,\r\n\t\t\t\t\tstore_id = 0,\r\n\t\t\t\t\tadd_flow_path = 1,\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tlet type = (is_add && pay_type == 2 && add_flow_path == 2) || (pay_type === 3 && (is_add ||\r\n\t\t\t\t\t\tstore_id)) ?\r\n\t\t\t\t\t5 : pay_type == 8 ? -1 : pay_type\r\n\t\t\t\tthis.popupInfo = {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\torder_id,\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\ttext_type: is_add && pay_type == 2 ? 2 : type,\r\n\t\t\t\t\tcan_refund_price\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.change_item.open()\r\n\t\t\t},\r\n\t\t\t// pay_type 2待接单，3接单，4出发，5到达，6服务中，7已完成  \r\n\t\t\tasync confirmChangeOrder() {\r\n\t\t\t\tlet param = this.$util.deepCopy(this.popupInfo)\r\n\t\t\t\tlet {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\ttext_type\r\n\t\t\t\t} = param\r\n\t\t\t\tparam.type = type == -1 ? type : type + 1\r\n\t\t\t\tlet {\r\n\t\t\t\t\tis_add,\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tlet text = ``\r\n\t\t\t\tlet len = 0\r\n\t\t\t\tif (param.type == 7 && !is_add) {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tagent\r\n\t\t\t\t\t} = this.options\r\n\t\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tpay_type,\r\n\t\t\t\t\t\tadd_service = []\r\n\t\t\t\t\t} = await this.$api[methodKey].orderInfo({\r\n\t\t\t\t\t\tid: param.order_id\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlen = add_service.length\r\n\t\t\t\t\tif (add_service && add_service.length > 0) {\r\n\t\t\t\t\t\tadd_service.map(item => {\r\n\t\t\t\t\t\t\ttext += `【${item}】`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (text && text.length > 0) {\r\n\t\t\t\t\tlet [res_del, {\r\n\t\t\t\t\t\tconfirm\r\n\t\t\t\t\t}] = await uni.showModal({\r\n\t\t\t\t\t\tcontent: `该订单还有${text}${len}项加钟服务，完成主订单会将加钟订单一并结束，确认是否结束？`,\r\n\t\t\t\t\t\tconfirmText: `确认`\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (!confirm) {\r\n\t\t\t\t\t\tthis.$refs.change_item.close()\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tdelete param.index\r\n\t\t\t\tdelete param.can_refund_price\r\n\t\t\t\tdelete param.text_type\r\n\t\t\t\tlet {\r\n\t\t\t\t\tactiveIndex,\r\n\t\t\t\t\ttechnicianStatusOperType\r\n\t\t\t\t} = this\r\n\t\t\t\tlet msg = text_type == -1 ? '退款成功' : `操作成功`\r\n\t\t\t\tif (this.lockTap) return\r\n\t\t\t\tthis.lockTap = true\r\n\t\t\t\tthis.$util.showLoading()\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet methodKey = agent ? 'agent' : 'admin'\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.$refs.change_item.close()\r\n\t\t\t\t\tawait this.$api[methodKey].adminUpdateOrder(param)\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: msg\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (activeIndex == 0) {\r\n\t\t\t\t\t\tthis.list.data[index].pay_type = param.type\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.list.data.splice(index, 1)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\t\tval: true\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (activeIndex) {\r\n\t\t\t\t\t\tawait this.getList(true)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t}, 500)\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tthis.updateTechnicianItem({\r\n\t\t\t\t\t\tkey: 'haveOperItem',\r\n\t\t\t\t\t\tval: true\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.param.page = 1\r\n\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t\tthis.lockTap = false\r\n\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 订单详情\r\n\t\t\tgoDetail(index) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tid\r\n\t\t\t\t} = this.list.data[index]\r\n\t\t\t\tlet {\r\n\t\t\t\t\tagent\r\n\t\t\t\t} = this.options\r\n\t\t\t\tlet url = `/agent/pages/order/detail?id=${id}&agent=${agent}`\r\n\t\t\t\tthis.$util.goUrl({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n</style>"], "sourceRoot": ""}