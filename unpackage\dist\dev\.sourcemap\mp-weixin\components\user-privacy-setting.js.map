{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/user-privacy-setting.vue?c45d", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/user-privacy-setting.vue?f534", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/user-privacy-setting.vue?ff09", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/user-privacy-setting.vue?0fc6", "uni-app:///components/user-privacy-setting.vue"], "names": ["name", "components", "props", "show", "type", "default", "mounted", "data", "computed", "primaryColor", "subColor", "userPrivacySetting", "methods", "init", "privacyCheck", "check", "needAuthorization", "open", "openPrivacyContract", "wx", "success", "fail", "complete", "toConfirmOper", "val", "event", "buttonId", "key"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;;;AAGnE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuyB,CAAgB,uzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqB3zB;AAIA;AAAA;AAAA,gBACA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC,uDACA,+CACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA,IACAC,oBACA,wBADAA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAnB;gBACAoB;kBACAC;gBACA;kBACAC;kBACAD;gBACA;gBACA;kBACAE;kBACAH;gBACA;gBAAA,MACApB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAEA;AAAA,4B", "file": "components/user-privacy-setting.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user-privacy-setting.vue?vue&type=template&id=e6b22378&scoped=true&\"\nvar renderjs\nimport script from \"./user-privacy-setting.vue?vue&type=script&lang=js&\"\nexport * from \"./user-privacy-setting.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e6b22378\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/user-privacy-setting.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-privacy-setting.vue?vue&type=template&id=e6b22378&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-privacy-setting.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-privacy-setting.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uni-popup ref=\"user_privacy_setting\" type=\"center\" :custom=\"true\" :zIndex=\"9999999992\">\r\n\t\t<view class=\"common-popup-content fill-base radius-26\">\r\n\t\t\t<view class=\"title\">小程序隐私保护指引</view>\r\n\t\t\t<span class=\"f-paragraph c-title mt-lg\">\r\n\t\t\t\t在您使用之前，请仔细阅读<span @tap.stop=\"openPrivacyContract\"\r\n\t\t\t\t\t:style=\"{color:primaryColor}\">{{userPrivacySetting.privacyContractName}}</span>，如您同意{{userPrivacySetting.privacyContractName}},请点击“同意”开始使用。\r\n\t\t\t</span>\r\n\t\t\t<view class=\"button\">\r\n\t\t\t\t<view @tap.stop=\"toConfirmOper(1)\" class=\"item-child\">拒绝</view>\r\n\t\t\t\t<button id=\"agree-btn\" open-type=\"agreePrivacyAuthorization\"\r\n\t\t\t\t\t@agreeprivacyauthorization=\"toConfirmOper(2)\" class=\"item-child\"\r\n\t\t\t\t\t:style=\"{background:primaryColor,color:'#fff'}\">\r\n\t\t\t\t\t同意\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tname: 'userPrivacySetting',\r\n\t\tcomponents: {},\r\n\t\tprops: {\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tuserPrivacySetting: state => state.user.userPrivacySetting\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getPrivacySetting']),\r\n\t\t\t...mapMutations(['updateUserItem']),\r\n\t\t\tasync init() {\n\t\t\t\tawait this.getPrivacySetting() \r\n\t\t\t\tif (!this.show) return\r\n\t\t\t\tlet privacyCheck = this.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.open()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcheck() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tneedAuthorization\r\n\t\t\t\t} = this.userPrivacySetting\r\n\t\t\t\treturn needAuthorization\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.$refs.user_privacy_setting.open()\r\n\t\t\t},\r\n\t\t\topenPrivacyContract() {\r\n\t\t\t\twx.openPrivacyContract({\r\n\t\t\t\t\tsuccess: () => {}, // 打开成功\r\n\t\t\t\t\tfail: () => {}, // 打开失败\r\n\t\t\t\t\tcomplete: () => {}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync toConfirmOper(type = 1) {\r\n\t\t\t\tlet val = type == 1 ? {\r\n\t\t\t\t\tevent: 'disagree'\r\n\t\t\t\t} : {\r\n\t\t\t\t\tbuttonId: 'agree-btn',\r\n\t\t\t\t\tevent: 'agree'\r\n\t\t\t\t}\r\n\t\t\t\tthis.updateUserItem({\r\n\t\t\t\t\tkey: 'resolvePrivacy',\r\n\t\t\t\t\tval\r\n\t\t\t\t})\r\n\t\t\t\tif (type == 2) {\r\n\t\t\t\t\tawait this.getPrivacySetting()\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.user_privacy_setting.close()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>"], "sourceRoot": ""}