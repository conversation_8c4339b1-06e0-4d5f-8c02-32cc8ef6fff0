{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?6422", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?dd3d", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?b63f", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?fd50", "uni-app:///components/upload.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?37d5", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/upload.vue?3994"], "names": ["components", "WCompress", "props", "imagelist", "type", "default", "imgtype", "imgsize", "filetype", "imgclass", "text", "videoSize", "sourceType", "isEdit", "isCompress", "computed", "primaryColor", "subColor", "userInfo", "methods", "previewImage", "urls", "res_urls", "uni", "current", "to<PERSON><PERSON>", "fileName", "content", "res_del", "confirm", "chooseImage", "privacyCheck", "is_upload_img", "chooseModel", "count", "param", "res_upload", "res_info", "size", "tempFiles", "tempFile<PERSON>ath", "console", "title", "filePath", "arr", "mask", "pixels", "quality", "formData", "path", "toCompressImg", "i", "onPlay", "onPause", "onEnded", "onTimeUpdate", "onWaiting", "onProgress", "onLoadedMetaData"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAyxB,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6E7yB;AAGA;EAAA;IAAA;EAAA;AAAA;AAAA,gBACA;EACAA;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;EACA;EACAU;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;QACAC;MACA;MACAC;QACAC;QACAH;MACA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OAGAH;kBACAI;gBACA;cAAA;gBAAA;gBAAA;gBAJAC;gBACAC;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;kBACAvB;kBACAH;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAKAzB,UAMA,OANAA,SACAC,UAKA,OALAA,SACAC,WAIA,OAJAA,UACAG,YAGA,OAHAA,WACAC,aAEA,OAFAA,YACAE,aACA,OADAA;gBAKAX;gBACA6B;gBACAC;gBACAC;gBAEAA;gBAEAC;kBACAD;gBACA;gBACA;kBACAC;gBACA;gBACAA;gBAAA;gBAAA,OACAZ;cAAA;gBAAA;gBAAA;gBAAAa;gBAAAC;gBAAA,KACAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,iBAKAC,SAHAC,6DACAC,YAEAF,SAFAE,mCAEAF,SADAG;gBAEAC;gBAAA,MACAjC;kBAAA;kBAAA;gBAAA;gBACA;kBACAkC;gBACA;gBAAA;cAAA;gBAGAC,eACA;gBAAA,KACAX;kBAAA;kBAAA;gBAAA;gBACAY;kBACA;gBACA;gBACA;kBACArB;oBACAmB;oBACAG;kBACA;kBACA;oBACAC;oBAAA;oBACAC;kBACA;oBACA;oBACA;oBACA;kBACA;oBACAN;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;kBACAC;gBACA;gBAAA;gBAAA,OAGA;kBACAC;kBACAK;oBACA5C;kBACA;gBACA;cAAA;gBAAA;gBANA6C;gBAOA9C;kBACA8C;gBACA;gBACA;gBACA;kBACA3C;kBACAH;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAR;gBACA;gBACAS;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAR;kBACAK;oBACA5C;kBACA;gBACA;cAAA;gBAAA;gBANA6C;gBAOA;kBACA9C;oBACA8C;kBACA;gBACA;kBACA9C;oBACA8C;kBACA;gBACA;cAAA;gBAjBAE;gBAAA;gBAAA;cAAA;gBAmBA;gBACA;kBACA7C;kBACAH;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiD;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/TA;AAAA;AAAA;AAAA;AAAw9C,CAAgB,46CAAG,EAAC,C;;;;;;;;;;;ACA5+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./upload.vue?vue&type=template&id=1f6e267c&\"\nvar renderjs\nimport script from \"./upload.vue?vue&type=script&lang=js&\"\nexport * from \"./upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./upload.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/upload.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload.vue?vue&type=template&id=1f6e267c&\"", "var components\ntry {\n  components = {\n    wCompress: function () {\n      return import(\n        /* webpackChunkName: \"components/w-compress/w-compress\" */ \"@/components/w-compress/w-compress.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imagelist.length < _vm.imgsize && _vm.isEdit\n  var g1 = g0 && _vm.imgsize > 1 ? _vm.imagelist.length : null\n  var g2 = _vm.imagelist.length < _vm.imgsize && !_vm.isEdit\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\r\n\t\t<view\r\n\t\t\t:class=\"[{'flex-warp':!imgclass || imgclass =='mini' || imgclass == 'apply'},{'flex-center flex-column':imgclass && imgclass != 'mini' && imgclass!=='apply'}]\">\r\n\t\t\t<block v-for=\"(item,index) in imagelist\" :key=\"index\">\r\n\t\t\t\t<view class=\"rel item-child radius-16\" :class=\"[imgclass,{'margin': imgsize > 1}]\"\r\n\t\t\t\t\t:style=\"{border:imgclass == 'apply' || imgclass == 'apply-idcard'?`4rpx solid ${primaryColor}`:''}\">\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view class=\"upload-img radius-16\" v-if=\"filetype == 'picture'\">\r\n\t\t\t\t\t\t<view @tap.stop=\"previewImage(item,imagelist)\" class=\"h5-image upload-img radius-16\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundImage : `url('${item.path}')`}\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<image mode=\"aspectFill\" @tap.stop=\"previewImage(item,imagelist)\" class=\"upload-img radius-16\"\r\n\t\t\t\t\t\t:src=\"item.path\" v-if=\"filetype == 'picture'\"></image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t\t<video :id=\"`video_${index}`\" class=\"upload-video rel radius-16\" :loop=\"false\" enable-play-gesture\r\n\t\t\t\t\t\tenable-progress-gesture :show-center-play-btn=\"true\" :controls=\"true\" :src=\"item.path\"\r\n\t\t\t\t\t\t:data-id=\"item.id\" objectFit=\"cover\" :data-index=\"index\" @play=\"onPlay\" @pause=\"onPause\"\r\n\t\t\t\t\t\t@ended=\"onEnded\" @timeupdate=\"onTimeUpdate\" @waiting=\"onWaiting\" @progress=\"onProgress\"\r\n\t\t\t\t\t\t@loadedmetadata=\"onLoadedMetaData\" v-if=\"filetype == 'video'\">\r\n\t\t\t\t\t\t<cover-view @tap=\"toDel(index)\" class=\"item-delete abs flex-center f-icontext c-base\"\r\n\t\t\t\t\t\t\tv-if=\"isEdit\" :style=\"{background:primaryColor}\">\r\n\t\t\t\t\t\t\t删除\r\n\t\t\t\t\t\t</cover-view>\r\n\t\t\t\t\t</video>\r\n\r\n\t\t\t\t\t<block v-if=\"filetype == 'picture' && isEdit\">\r\n\t\t\t\t\t\t<view @tap=\"toDel(index)\" class=\"guanbi abs flex-center\" :class=\"[imgclass]\" style=\"z-index: 1;\"\r\n\t\t\t\t\t\t\tv-if=\"imgsize>1\"><i class=\"iconfont icon-add rotate-45 c-base\"></i></view>\r\n\t\t\t\t\t\t<view @tap=\"chooseImage\" class=\"flex-center flex-column item-child upload-item radius-16 abs\"\r\n\t\t\t\t\t\t\tstyle=\"top:0;margin-top:0;background:rgba(0,0,0,0.5);\" v-else>\r\n\t\t\t\t\t\t\t<view class=\"upload-icon flex-center c-title radius-10\">\r\n\t\t\t\t\t\t\t\t<i class=\"iconfont icon-camera\"></i>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"f-caption c-base mt-sm\">重新上传</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t<view @tap=\"chooseImage\" class=\"radius-16 item-child fill-body radius-16\"\r\n\t\t\t\t:class=\"[imgclass,{'margin': imgsize > 1}]\"\r\n\t\t\t\t:style=\"{border:imgclass == 'apply' || imgclass == 'apply-idcard'?`4rpx solid ${primaryColor}`:''}\"\r\n\t\t\t\tv-if=\"imagelist.length < imgsize && isEdit\">\r\n\t\t\t\t<view class=\"flex-center flex-column upload-item\">\r\n\t\t\t\t\t<view class=\"upload-icon flex-center c-title radius-10\">\r\n\t\t\t\t\t\t<i class=\"iconfont icon-camera\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f-caption c-caption mt-sm\" v-if=\"text\">{{text}}</view>\r\n\t\t\t\t\t<view class=\"cur-imgsize f-caption c-caption\" v-if=\"imgsize>1\">{{`${imagelist.length}/${imgsize}`}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"radius-16 item-child fill-body radius-16\" :class=\"[imgclass]\"\r\n\t\t\t\t:style=\"{border:imgclass == 'apply' || imgclass == 'apply-idcard'?`4rpx solid ${primaryColor}`:''}\"\r\n\t\t\t\tv-if=\"imagelist.length < imgsize && !isEdit\">\r\n\t\t\t\t<view class=\"flex-center flex-column upload-item\">\r\n\t\t\t\t\t<view class=\"upload-icon flex-center c-title radius-10\">\r\n\t\t\t\t\t\t<i class=\"iconfont icon-camera\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"f-caption c-caption mt-sm\">暂未上传</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<w-compress ref='wCompress' />\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<user-privacy ref=\"user_privacy\" :show=\"false\"></user-privacy>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport WCompress from '@/components/w-compress/w-compress.vue'\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t} from 'vuex';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tWCompress\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 图片列表\r\n\t\t\timagelist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 图片参数名\r\n\t\t\timgtype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 图片张数\r\n\t\t\timgsize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 9\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 上传类型\r\n\t\t\tfiletype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 'picture'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 图片样式\r\n\t\t\timgclass: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 备注信息\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 视频大小限制\r\n\t\t\tvideoSize: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 50\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 上传类型\r\n\t\t\tsourceType: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否可上传/删除\r\n\t\t\tisEdit: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 压缩图片\r\n\t\t\tisCompress: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapState({\r\n\t\t\tprimaryColor: state => state.config.configInfo.primaryColor,\r\n\t\t\tsubColor: state => state.config.configInfo.subColor,\r\n\t\t\tuserInfo: state => state.user.userInfo\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\tpreviewImage(current, urls) {\r\n\t\t\t\tlet res_urls = [];\r\n\t\t\t\turls = this.$util.deepCopy(urls);\r\n\t\t\t\turls.forEach((item, index) => {\r\n\t\t\t\t\tres_urls.push(item.path)\r\n\t\t\t\t})\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: current.path,\r\n\t\t\t\t\turls: res_urls,\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync toDel(index) {\r\n\t\t\t\tlet fileName = this.filetype == 'picture' ? '图片' : '视频'\r\n\t\t\t\tlet [res_del, {\r\n\t\t\t\t\tconfirm\r\n\t\t\t\t}] = await uni.showModal({\r\n\t\t\t\t\tcontent: `请确认是否要删除${fileName}`,\r\n\t\t\t\t})\r\n\t\t\t\tif (!confirm) return;\r\n\t\t\t\tthis.imagelist.splice(index, 1);\r\n\t\t\t\tthis.$emit('del', {\r\n\t\t\t\t\timgtype: this.imgtype,\r\n\t\t\t\t\timagelist: this.imagelist\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync chooseImage() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet privacyCheck = this.$refs.user_privacy.check()\r\n\t\t\t\tif (privacyCheck) {\r\n\t\t\t\t\tthis.$refs.user_privacy.open()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet {\r\n\t\t\t\t\timgtype,\r\n\t\t\t\t\timgsize,\r\n\t\t\t\t\tfiletype,\r\n\t\t\t\t\tvideoSize,\r\n\t\t\t\t\tsourceType,\r\n\t\t\t\t\tisCompress,\r\n\t\t\t\t} = this\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tisCompress = false\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet imagelist = this.$util.deepCopy(this.imagelist)\r\n\t\t\t\tlet is_upload_img = filetype == 'picture'\r\n\t\t\t\tlet chooseModel = is_upload_img ? 'chooseImage' : 'chooseVideo'\r\n\t\t\t\tlet count = 1\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tcount = imgsize == 1 ? 1 : imgsize - imagelist.length * 1\r\n\t\t\t\t// #endif\r\n\t\t\t\tlet param = {\r\n\t\t\t\t\tcount\r\n\t\t\t\t}\r\n\t\t\t\tif (is_upload_img) {\r\n\t\t\t\t\tparam.sizeType = ['compressed']\r\n\t\t\t\t}\r\n\t\t\t\tparam.sourceType = sourceType == 1 ? ['camera', 'album'] : ['camera']\r\n\t\t\t\tlet [res_upload, res_info] = await uni[chooseModel](param)\r\n\t\t\t\tif (res_upload) return\r\n\t\t\t\tlet {\r\n\t\t\t\t\tsize = 0,\r\n\t\t\t\t\t\ttempFiles,\r\n\t\t\t\t\t\ttempFilePath = ''\r\n\t\t\t\t} = res_info\r\n\t\t\t\tconsole.log(is_upload_img, size, size / 1024 / 1024, \"=====size\")\r\n\t\t\t\tif (filetype == 'video' && size / 1024 / 1024 > videoSize) {\r\n\t\t\t\t\tthis.$util.showToast({\r\n\t\t\t\t\t\ttitle: `上传视频大小超过限制${videoSize}M`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet filePath = [];\r\n\t\t\t\t// 格式化图片参数\r\n\t\t\t\tif (is_upload_img) {\r\n\t\t\t\t\tlet arr = tempFiles.map(item => {\r\n\t\t\t\t\t\treturn item.path\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (isCompress) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '图片压缩中...',\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.$refs.wCompress.start(arr, {\r\n\t\t\t\t\t\t\tpixels: 1000000, // 最大分辨率，默认二百万\r\n\t\t\t\t\t\t\tquality: 0.9\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t// console.log(res, \"=============== wCompress res\")\r\n\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t\tthis.toCompressImg(res, imgtype, imgsize, imagelist)\r\n\t\t\t\t\t\t}).catch(e => {\r\n\t\t\t\t\t\t\tconsole.log(e)\r\n\t\t\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.toCompressImg(arr, imgtype, imgsize, imagelist)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$util.showLoading({\r\n\t\t\t\t\t\ttitle: \"上传中\"\r\n\t\t\t\t\t});\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tattachment_path: path\r\n\t\t\t\t\t} = await this.$api.base.uploadFile({\r\n\t\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\ttype: this.filetype\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\timagelist.push({\r\n\t\t\t\t\t\tpath\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\t\tthis.$emit('upload', {\r\n\t\t\t\t\t\timgtype,\r\n\t\t\t\t\t\timagelist\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync toCompressImg(imgs, imgtype, imgsize, imagelist) {\r\n\t\t\t\tthis.$util.showLoading({\r\n\t\t\t\t\ttitle: \"上传中\"\r\n\t\t\t\t});\r\n\t\t\t\tfor (let i = 0; i < imgs.length; i++) {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tattachment_path: path\r\n\t\t\t\t\t} = await this.$api.base.uploadFile({\r\n\t\t\t\t\t\tfilePath: imgs[i],\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\ttype: this.filetype\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (imgsize > 1) {\r\n\t\t\t\t\t\timagelist.push({\r\n\t\t\t\t\t\t\tpath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\timagelist = [{\r\n\t\t\t\t\t\t\tpath\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.$util.hideAll()\r\n\t\t\t\tthis.$emit('upload', {\r\n\t\t\t\t\timgtype,\r\n\t\t\t\t\timagelist\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonPlay(e) {},\r\n\t\t\tonPause(e) {},\r\n\t\t\tonEnded(e) {},\r\n\t\t\tonTimeUpdate(e) {},\r\n\t\t\tonWaiting(e) {},\r\n\t\t\tonProgress(e) {},\r\n\t\t\tonLoadedMetaData(e) {},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.item-child {\r\n\t\twidth: 216rpx;\r\n\t\theight: 216rpx;\r\n\t\tbackground: #F7F7F7;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.margin {\r\n\t\tmargin: 0 21rpx 21rpx 0;\r\n\t}\r\n\r\n\t.item-child:nth-child(3n) {\r\n\t\tmargin-right: 0rpx;\r\n\t}\r\n\r\n\t.item-child.sm {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t}\r\n\r\n\t.item-child.apply {\r\n\t\twidth: 176rpx;\r\n\t\theight: 176rpx;\r\n\t\ttransform: scaleZ(360deg);\r\n\t}\r\n\r\n\t.item-child.apply-idcard {\r\n\t\twidth: 290rpx;\r\n\t\theight: 182rpx;\r\n\t\ttransform: scaleZ(360deg);\r\n\t}\r\n\r\n\t.item-child.mini {\r\n\t\twidth: 196rpx;\r\n\t\theight: 196rpx;\r\n\t}\r\n\r\n\t.item-child.md {\r\n\t\twidth: 335rpx;\r\n\t\theight: 210rpx;\r\n\t\tmargin: 0rpx;\r\n\t}\r\n\r\n\t.item-child.lg {\r\n\t\twidth: 686rpx;\r\n\t\theight: 400rpx;\r\n\t}\r\n\r\n\t.upload-img,\r\n\t.upload-video {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\r\n\t.upload-video {\r\n\t\t.item-delete {\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 32rpx;\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\tz-index: 2;\r\n\t\t}\r\n\t}\r\n\r\n\t.upload-item {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\t.upload-icon {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 76rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.cur-imgsize {\r\n\t\t\tline-height: 1.1;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.upload-item.margin {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.guanbi {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\tborder-radius: 0 15rpx 0 0;\r\n\t\ttop: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\tz-index: 1;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.guanbi.lg {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 38rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110363757\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}