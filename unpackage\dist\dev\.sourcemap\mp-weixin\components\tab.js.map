{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?c442", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?e01a", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?dc48", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?571c", "uni-app:///components/tab.vue", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?33a9", "webpack:///C:/Users/<USER>/Desktop/同城上门家政按摩H5小程序源码  上门预约系统/前端未编译/uniapp/wechat/components/tab.vue?8e99"], "names": ["name", "props", "list", "type", "default", "activeIndex", "color", "activeColor", "bgColor", "width", "height", "isLine", "lineClass", "msgRight", "fontSize", "numberType", "created", "data", "methods", "handerTabChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AAC0M;AAC1M,gBAAgB,kNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAsxB,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCuC1yB;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACAU;MACAX;MACAC;QACA;MACA;IACA;IACAW;MACAZ;MACAC;QACA;MACA;IACA;EACA;EACAY,6BAEA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAq9C,CAAgB,y6CAAG,EAAC,C;;;;;;;;;;;ACAz+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tab.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tab.vue?vue&type=template&id=4ca2a42a&\"\nvar renderjs\nimport script from \"./tab.vue?vue&type=script&lang=js&\"\nexport * from \"./tab.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tab.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tab.vue\"\nexport default component.exports", "export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab.vue?vue&type=template&id=4ca2a42a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.is_sign == 1 ? item.title.length : null\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<scroll-view scroll-x class='tab-list' :scroll-into-view=\"'tab'+(activeIndex-1)\" :scroll-with-animation=\"true\"\r\n\t\t\t:style=\"{background: bgColor,fontSize: fontSize}\">\r\n\t\t\t<view class='tab-item rel' v-for=\"(item,index) in list\" :key=\"index\" @tap='handerTabChange(index)'\r\n\t\t\t\tdata-index=\"index\" :id=\"'tab'+index\"\r\n\t\t\t\t:style='{width,height,lineHeight:height,color:index==activeIndex?activeColor:color}'>\r\n\t\t\t\t<view class=\"flex-y-baseline flex-x-center rel\">\r\n\t\t\t\t\t<view class=\"rel\" v-if=\"item.number\">\r\n\t\t\t\t\t\t{{item.title || item}}\r\n\t\t\t\t\t\t<view v-if=\"numberType == 1\" class=\"item-msg c-base abs\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number<10 ? '30rpx' :item.number<100 ? '40rpx' : '50rpx',right:item.number<10 ? '-34rpx' :item.number<100 ? '-44rpx' : '-54rpx' }\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"tab-label c-base abs\"\r\n\t\t\t\t\t\t\t:style=\"{width: item.number<10 ? '24rpx' :item.number<100 ? '36rpx': '48rpx',right:item.number<10 ? '-14rpx' :item.number<100 ? '-26rpx' : '-38rpx'}\"\r\n\t\t\t\t\t\t\tv-if=\"numberType == 2\">\r\n\t\t\t\t\t\t\t{{item.number < 100 ? item.number : '99+'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-else>{{item.title || item}}</block>\r\n\t\t\t\t\t<block v-if=\"item.is_sign == 1\">\r\n\t\t\t\t\t\t<view class=\"tab-item-sanjao abs\" :style=\"{right: item.title.length == 3 ? '-20rpx' : ''}\">\r\n\t\t\t\t\t\t\t<view class=\"up iconfont icon-up-fill rel\"\r\n\t\t\t\t\t\t\t\t:style=\"{color:index==activeIndex && item.sign == 1?activeColor:'#ccc',}\"></view>\r\n\t\t\t\t\t\t\t<view class=\"down iconfont icon-down-fill rel\"\r\n\t\t\t\t\t\t\t\t:style=\"{color:index==activeIndex && item.sign == 0?activeColor:'#ccc',}\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"abs line\" :class=\"[lineClass]\" :style=\"{background: activeColor}\"\r\n\t\t\t\t\tv-if=\"isLine && index==activeIndex && !item.is_sign\">\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'tab',\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tactiveIndex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '#333'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '#e73535'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '#fff'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '100rpx'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tisLine: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlineClass: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn ''\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmsgRight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '5rpx'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn '30rpx'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnumberType: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thanderTabChange(index) {\r\n\t\t\t\tthis.$emit('change', index);\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.tab-list {\r\n\t\twhite-space: nowrap;\r\n\t\tbackground: #fff;\r\n\t\twidth: 100%;\r\n\r\n\t\t.tab-item {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tborder-color: #fff;\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t.tab-label {\r\n\t\t\t\twidth: 24rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 24rpx;\r\n\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\tbackground: #F1381F;\r\n\t\t\t\ttop: 12rpx;\r\n\t\t\t\tright: -14rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item-msg {\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t\tborder-radius: 15rpx 15rpx 15rpx 0;\r\n\t\t\t\tbackground: #f12c20;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t\tright: -34rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.tab-item-sanjao {\r\n\t\t\t\ttop: 18rpx;\r\n\t\t\t\tright: -6rpx;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\ttransform: scale(0.6);\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.up {\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.down {\r\n\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.line {\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\theight: 6rpx;\r\n\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\tbottom: 0rpx;\r\n\t\t\t\tmargin-left: -40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.line.sm {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 6rpx;\r\n\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\tbottom: 0rpx;\r\n\t\t\t\tmargin-left: -30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\software\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754110359779\n      var cssReload = require(\"D:/software/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}