.page-height {
	width: 100%;
	height: 100vh;
	background: #fff;
}

/* 单选/多选按钮 */
.pay-icon {
	font-size: 40rpx;
}

/* 分享按钮 */
.common-share-btn {
	right: 30rpx;
	bottom: 140rpx;
	width: 90rpx;
	height: 90rpx;
	margin-bottom: calc(env(safe-area-inset-bottom) / 2);

	.iconfont {
		font-size: 40rpx;
	}
}

.common-share-btn.detail {
	bottom: 170rpx;
}

.map-info {
	.iconjuli {
		font-size: 42rpx;
	}

	.icon-down {
		transform: scale(0.5);
	}
}

/* 海报 */
.hideCanvasView {
	position: relative;

	.hideCanvas {
		position: absolute;
		left: -9999rpx;
		top: -9999rpx
	}
}

/* 会员专享 */
.member-canbuy-level {
	min-width: 100rpx;
	height: 32rpx;
	background: linear-gradient(270deg, #32281E 0%, #4B3A32 100%);
	border-radius: 6rpx;
	color: #DCCCB5;

	.text {
		height: 32rpx;
		font-size: 22rpx;
		transform: scale(0.85);
	}
}

/* 技-师页面 */
.pages-technician {
	.fix-info {
		height: 225rpx;

		.space-top {
			width: 100%;
			height: 40rpx;
			top: 0;
		}

		.search-info {
			width: 100%;
			top: 0;
			border-radius: 40rpx;

			.city-info {
				width: 170rpx;

				.iconfont {
					font-size: 20rpx;
					transform: scale(0.5);
				}
			}
		}

		.tab-info {
			.tab-list-item {
				width: 90%;
			}

			.tab-select-item {
				height: 44rpx;
				box-shadow: -5px 0 5px -5px rgba(0, 0, 0, 0.8);
			}
		}

		.search-cate-list {
			.item-child {
				width: 155rpx;
				height: 67rpx;
				padding: 0 10rpx;
				color: #484848;
				background: #FFFFFF;
			}
		}
	}

	.fix-info.choose {
		height: 135rpx;
	}


	.choose-city-popup {
		padding: 0 32rpx 0 30rpx;

		.city-img {
			width: 688rpx;
			height: 364rpx;
		}

		.text {
			top: 256rpx;
			left: 78rpx;
			width: 606rpx;
		}

		.flex-between {
			margin-top: 25rpx;

			.item-btn {
				width: 238rpx;
				height: 85rpx;
				background: #F9F9F9;
			}
		}

	}
}


/* 订单相关页面 */
.order-pages {

	.service-type-item {
		min-width: 143rpx;
		height: 59rpx;
		padding: 0 10rpx;
		background: #F5F5F5;
		border-radius: 8rpx
	}

	.address-info {
		.address-icon {
			width: 64rpx;
			height: 64rpx;

			.iconfont {
				font-size: 38rpx;
			}
		}

		.username {
			font-size: 30rpx;
		}
	}

	.store-info {

		.item-icon {
			width: 40rpx;
			height: 40rpx;

			.iconfont {
				font-size: 28rpx;
			}

			.item-icon {
				top: 0;
				left: 0;
				opacity: 0.1;
			}
		}
	}



	.item-child {
		.bell-tag {
			top: 0;
			left: 0;
			width: 120rpx;
			height: 40rpx;
			border-radius: 16rpx 0 16rpx 0;
		}

		.grayscale {

			.c-title,
			.c-warning {
				color: #999;
			}
		}

		.cover {
			width: 155rpx;
			height: 155rpx;
		}

		.refund-img {
			width: 196rpx;
			height: 196rpx;
		}

		.refund-img:nth-child(3n) {
			margin-right: 0
		}
	}

	.item-textarea {
		width: 570rpx;
		height: 300rpx;
	}

	.menu-list {
		margin-top: -30rpx;

		.menu-title {
			height: 90rpx;

			.iconfont {
				font-size: 24rpx;
			}
		}

		.menu-line {
			width: 80%;
			top: 76rpx;
			left: 10%;
		}

		.item-child {
			width: 20%;
			margin: 10rpx 0;

			.item-img {
				width: 72rpx;
				height: 72rpx;
				z-index: 9;
				border: 1px solid #666;

				.iconfont {
					font-size: 40rpx;
				}
			}
		}

	}

	.add-bell {
		.menu-line {
			width: 70%;
			top: 76rpx;
			left: 15%;
		}

		.item-child {
			width: 33.33%;
		}

	}

	.add-normal-bell {
		.menu-line {
			width: 50%;
			top: 76rpx;
			left: 25%;
		}

		.item-child {
			width: 50%;
		}

	}

	.order-agent-info {
		color: #4A4A4A;

		.coach-img {
			width: 94rpx;
			height: 94rpx;
		}

		.title {
			width: 200rpx;
			font-weight: bold;
		}

		.text {
			color: #777;
			font-size: 26rpx;
		}
	}

	.copy-btn {
		width: 60rpx;
		height: 32rpx;
		border: 1rpx solid #fff;
		transform: rotateZ(360deg);
	}

	.copy-btn.fill {
		color: 333;
		borderColor: #eee;
		background: #eee;
		padding: 2rpx 6rpx;
	}

	.copy-btn.span {
		padding: 2rpx 6rpx;
	}

	.footer-info {

		bottom: 0;

		.item-btn {
			min-width: 150rpx;
			height: 64rpx;
			padding: 0 10rpx;
			background: #EEEEEE;
		}

	}

}


/* 申请表单相关页面 */
.apply-pages {
	.apply-form {

		.item-text {
			width: 200rpx;
			height: 30rpx;
			line-height: 30rpx;
			font-size: 30rpx;
			color: #1F1F1F;
		}

		.item-input {
			min-height: 30rpx;
			line-height: 30rpx;
			padding: 25rpx 0;
			font-size: 26rpx;
			color: #A9A9A9;
		}

		.item-input.text {
			padding: 30rpx 0;
		}

		.item-textarea {
			width: 630rpx;
			height: 400rpx;
			color: #A9A9A9;
		}

		.icon-switch,
		.icon-switch-on {
			font-size: 90rpx;
			line-height: 46rpx;
		}
	}
}

/* 储值明细记录 */
.stored-record-pages {
	.list-time {
		z-index: 99999;

		.item-child {
			width: 50%;
			height: 95rpx;

			.iconfont {
				font-size: 28rpx;
			}
		}
	}

	.popup-choose-time {
		.item-child {
			width: 50%;
		}
	}
}

.container {
	width: 100%;
}

/* 技-师等级 */
.technician-level {
	height: 333rpx;
	background: linear-gradient(206deg, #464749 0%, #020202 30%, #000000 100%);
	border-radius: 16rpx;
	transform: rotateZ(360deg);
	overflow: hidden;

	.icon-level-icon {
		font-size: 505rpx;
		top: -86rpx;
		left: 40rpx;
		z-index: -1;
	}

	.level-icon-img {
		top: 22rpx;
		right: 41rpx;
		width: 111rpx;
		height: 111rpx;
	}

	.level-title {
		font-size: 38rpx;
		color: #F9F9F9;
	}

	.level-line {
		width: 100%;
		height: 20rpx;
		background: #2D333C;
	}

	.level-line.cur {
		top: 0;
		left: 0;
	}

	.f-icontext {
		color: rgba(255, 255, 255, 0.7);
	}
}